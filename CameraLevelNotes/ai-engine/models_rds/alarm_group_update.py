import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.orm import relationship

from models_rds.alarm_group import AlarmGroup
from models_rds.rds_base import RDSBase
from vision.services.src.event_processor.models.events import EventType


class AlarmGroupUpdate(RDSBase):
    """
    Class corresponding to alarm_group_update table in RDS
    """

    __tablename__ = "alarm_group_update_v1"
    id = Column(
        "id", String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    alarm_group_id = Column(
        "alarm_group_id", String(36), ForeignKey(AlarmGroup.id)
    )
    alarm_group = relationship("AlarmGroup", backref="alarm_group_updates")
    event_type = Column(
        "event_type",
        Enum(EventType, values_callable=lambda obj: [i.value for i in obj]),
        nullable=False,
    )
    event_details = Column("event_details", JSON, default=dict)
