import datetime

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    TEXT,
    BigInteger,
    Column,
    Foreign<PERSON>ey,
    Integer,
    String,
)
from sqlalchemy.sql import func
from sqlalchemy.types import DateTime

from models_rds.rds_base import RDSBase


class AIOutputsLocationAlarms(RDSBase):
    """
    Class corresponding to ai_outputs_location_alarms table in RDS
    """

    __tablename__ = "ai_outputs_location_alarms"

    int_id = Column(
        "id",
        BigInteger().with_variant(Integer, "sqlite"),
        primary_key=True,
        autoincrement=True,
    )
    location_alarm_id = Column(
        "location_alarm_id",
        BigInteger().with_variant(Integer, "sqlite"),
        nullable=True,
    )
    tenant_id = Column(
        "tenant_id", String(36), ForeignKey("tenants.id"), nullable=False
    )
    summary = Column("summary", TEXT)
    recommendation = Column("recommendation", String(255))
    explanation = Column("explanation", TEXT)
    alarm_group_id = Column(
        "alarm_group_id",
        String(36),
        nullable=True,
    )
    created_at_utc = Column(
        "created_at_utc", DateTime(timezone=True), server_default=func.utcnow()
    )
    updated_at_utc = Column(
        "updated_at_utc",
        DateTime(timezone=True),
        server_default=func.utcnow(),
        onupdate=datetime.datetime.utcnow,
    )
    raw_state = Column("raw_state", JSON)

    def __repr__(self):
        return f"<AI Output {self.int_id} for Location Alarm {self.location_alarm_id}>"
