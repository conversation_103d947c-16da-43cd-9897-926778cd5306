import datetime
from enum import Enum

import sqlalchemy
from sqlalchemy import Column, ForeignKey, String
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.types import DateTime

from models_rds.locations import Locations
from models_rds.rds_base import RDSBase
from models_rds.tenants import Tenants


class TagEnum(str, Enum):
    POLICE = "Police"
    GUARDS = "Guards"
    SPOC = "SPOC"
    RSPNDR = "Rspndr"


class LocationContacts(RDSBase):
    """
    Class corresponding to location_contacts table in RDS
    """

    __tablename__ = "location_contacts"

    id = Column(
        "id",
        sqlalchemy.BigInteger().with_variant(sqlalchemy.Integer, "sqlite"),
        primary_key=True,
        autoincrement=True,
    )
    location_id = Column(
        "location_id",
        sqlalchemy.BigInteger().with_variant(sqlalchemy.Integer, "sqlite"),
        ForeignKey(Locations.id),
        nullable=False,
    )
    name = Column("name", String(255))
    email = Column("email", String(255))
    phone_number = Column("phone_number", String(50))
    priority = Column("priority", sqlalchemy.SMALLINT, default=0)
    tenant_id = Column(
        "tenant_id", String(36), ForeignKey(Tenants.uuid), nullable=False
    )
    created_at_utc = Column(
        "created_at_utc", DateTime(timezone=True), server_default=func.utcnow()
    )
    updated_at_utc = Column(
        "updated_at_utc",
        DateTime(timezone=True),
        server_default=func.utcnow(),
        onupdate=datetime.datetime.utcnow,
    )

    # timestamp at which last SMS was sent for this contact.
    last_sms_at_utc = Column(
        "last_sms_at_utc",
        DateTime(timezone=True),
    )
    tag = Column("tag", sqlalchemy.Enum(TagEnum))
    tenants = relationship("Tenants", backref="location_contacts")
    locations = relationship("Locations", backref="location_contacts")

    def __repr__(self):
        return f"<Location contact {self.id}>"
