import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>TAMP, <PERSON>umn, Enum, ForeignKey, String
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from models_rds.camera_groups import CameraGroups
from models_rds.rds_base import RDSBase
from models_rds.tenants import Tenants
from vision.services.src.event_processor.models.events import (
    AlarmGroupQueueState,
    AlarmGroupRecommendation,
    AlarmGroupState,
    Resolution,
    Severity,
)


class AlarmGroup(RDSBase):
    """
    Class corresponding to alarm_group table in RDS
    """

    __tablename__ = "alarm_group_v1"
    id = Column(
        "id", String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    severity = Column(
        "severity",
        Enum(Severity, values_callable=lambda obj: [i.value for i in obj]),
        nullable=False,
    )
    state = Column(
        "state",
        Enum(
            AlarmGroupState, values_callable=lambda obj: [i.value for i in obj]
        ),
        nullable=False,
    )
    resolution = Column(
        "resolution",
        Enum(Resolution, values_callable=lambda obj: [i.value for i in obj]),
        nullable=False,
    )
    resolution_comment = Column("resolution_comment", String(512), default="")
    start_time_utc = Column("start_time_utc", TIMESTAMP)
    end_time_utc = Column("end_time_utc", TIMESTAMP)
    created_at_utc = Column("created_at_utc", TIMESTAMP)
    camera_group_id = Column(
        "camera_group_id", String(36), ForeignKey(CameraGroups.id)
    )
    camera_group = relationship("CameraGroups", backref="alarm_group")
    tenant_id = Column("tenant_id", String(36), ForeignKey(Tenants.uuid))
    tenant = relationship("Tenants", backref="alarm_group")
    operator_id = Column("operator_id", String(36))
    recommendation = Column(
        "recommendation",
        Enum(
            AlarmGroupRecommendation,
            values_callable=lambda obj: [i.value for i in obj],
        ),
        nullable=False,
    )
    latest_event_time_utc = Column("latest_event_time_utc", TIMESTAMP)
    alarm_group_queue_state = Column(
        "alarm_group_queue_state",
        Enum(
            AlarmGroupQueueState,
            values_callable=lambda obj: [i.value for i in obj],
        ),
        nullable=False,
    )
    alarm_group_queue_state_updated_at_utc = Column(
        "alarm_group_queue_state_updated_at_utc", TIMESTAMP
    )

    @hybrid_property
    def is_active(self):
        return 1 if self.state in ("pending", "in_progress") else None
