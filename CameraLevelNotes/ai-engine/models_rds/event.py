import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, TIMESTAMP, <PERSON>umn, <PERSON><PERSON>, ForeignKey, String
from sqlalchemy.orm import relationship

from models_rds.alarm_group import AlarmGroup
from models_rds.camera_groups import CameraGroups
from models_rds.rds_base import RDSBase
from models_rds.tenants import Tenants
from vision.services.src.event_processor.models.events import (
    EventRecommendation,
    EventState,
    Severity,
)


class Event(RDSBase):
    __tablename__ = "event_v1"
    id = Column(
        "id", String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    severity = Column(
        "severity",
        Enum(Severity, values_callable=lambda obj: [i.value for i in obj]),
        nullable=False,
    )
    event_time_utc = Column("event_time_utc", TIMESTAMP)
    event_details = Column("event_details", JSON, default=dict)
    tenant_id = Column("tenant_id", String(36), ForeignKey(Tenants.uuid))
    tenant = relationship("Tenants", backref="tenants")
    # TODO: use of camera_id is better compare to camera_group_id as mapping to camera_group_id with camera_id is mutable.
    camera_group_id = Column(
        "camera_group_id", String(36), ForeignKey(CameraGroups.id)
    )
    camera_group = relationship("CameraGroups", backref="events")
    camera_id = Column("camera_id", String(36), nullable=True)
    alarm_group_id = Column(
        "alarm_group_id", String(36), ForeignKey(AlarmGroup.id)
    )
    state = Column(
        "state",
        Enum(EventState, values_callable=lambda obj: [i.value for i in obj]),
        nullable=False,
    )
    recommendation = Column(
        "recommendation",
        Enum(
            EventRecommendation,
            values_callable=lambda obj: [i.value for i in obj],
        ),
        nullable=False,
    )
    alarm_group = relationship("AlarmGroup", backref="events")
