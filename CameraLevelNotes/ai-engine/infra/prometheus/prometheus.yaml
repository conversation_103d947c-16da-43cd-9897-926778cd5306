#---
#apiVersion: v1
#kind: PersistentVolume
#metadata:
#  name: prometheus-pv
#  namespace: monitoring
#  labels:
#    app.kubernetes.io/instance: prom-op-kube-prometheus-st-prometheus
#spec:
#  capacity:
#    storage: 150Gi
#  accessModes:
#    - ReadWriteOnce
#  persistentVolumeReclaimPolicy: Retain
#  storageClassName: gp2
#  awsElasticBlockStore:
#    volumeID: vol-0bd7d255f0f57e20e
#    fsType: ext4


# Instance Size vCPU	Memory (GiB)	Instance Storage (GB)	Networking Performance (Gbps)	EBS Bandwidth (Mbps)
# r5a.2xlarge	8	64	EBS-Only	Up to 10 	Up to 2,880

---
apiVersion: helm.fluxcd.io/v1
kind: HelmRelease
metadata:
  name: prom-op
  namespace: monitoring
  annotations:
    fluxcd.io/automated: "false"
spec:
  releaseName: prom-op
  chart:
    repository: https://prometheus-community.github.io/helm-charts
    name: kube-prometheus-stack
    # Releases listed here:https://github.com/prometheus-community/helm-charts/tree/main/charts/kube-prometheus-stack
    # we need to run commands to update the CRD (specified in the
    # above release page) before applying the upgrade.
    version: 56.0.0
  values:
    alertmanager:
      enabled: true
    grafana:
      enabled: false
    prometheus:
      prometheusSpec:
        additionalScrapeConfigs:
          key: prom-federation.yaml
          name: federation-scrape-configs
        retention: 5d            # keep TSDB blocks for 5 days
        scrapeInterval: 60s # Global scrape interval
        scrapeTimeout: 10s  # Global scrape timeout
        walCompression: true
        query:
          timeout: "3m"
          maxSamples: 50000000  # default: 50000000, increase if queries return too many points
        resources:
          requests:
            memory: 32Gi
            cpu: 4
          limits:
            memory: 64Gi
            cpu: 8
        # Needed to allow service monitors for all namespaces
        # managed by other helm deployments to be discovered
        tolerations:
          - effect: NoSchedule
            key: purpose
            operator: Equal
            value: monitoring
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: nvidia.com/gpu.count
                      operator: DoesNotExist
                    - key: purpose
                      operator: In
                      values:
                        - "monitoring"
        serviceMonitorSelector:
          matchExpressions:
            - key: app.kubernetes.io/instance
              operator: In
              values:
                - ai-engine-prod
                - hip
                - rabbitmq
                - prometheus-pushgateway
                - prom-op
                - vision-prod
                - sps-prod
        storageSpec:
          volumeClaimTemplate:
            spec:
              storageClassName: gp2
              accessModes: ["ReadWriteOnce"]
              resources:
                requests:
                  storage: 150Gi
