alertmanager:
  enabled: true
grafana:
  enabled: false
prometheus:
  prometheusSpec:
    additionalScrapeConfigs:
      key: prom-federation.yaml
      name: federation-scrape-configs
    retention: 5d            # keep TSDB blocks for 5 days
    scrapeInterval: 60s # Global scrape interval
    scrapeTimeout: 10s  # Global scrape timeout
    walCompression: true
    query:
      timeout: "3m"
      maxSamples: 50000000  # default: 50000000, increase if queries return too many points
    resources:
      requests:
        memory: 32Gi
        cpu: 4
      limits:
        memory: 64Gi
        cpu: 8
    # Needed to allow service monitors for all namespaces
    # managed by other helm deployments to be discovered
    tolerations:
      - effect: NoSchedule
        key: purpose
        operator: Equal
        value: monitoring
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
                - key: nvidia.com/gpu.count
                  operator: DoesNotExist
                - key: purpose
                  operator: In
                  values:
                    - "monitoring"
    serviceMonitorSelector:
      matchExpressions:
        - key: app.kubernetes.io/instance
          operator: In
          values:
            - ai-engine-prod
            - hip
            - rabbitmq
            - prometheus-pushgateway
            - prom-op
            - vision-prod
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: gp2
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 150Gi
