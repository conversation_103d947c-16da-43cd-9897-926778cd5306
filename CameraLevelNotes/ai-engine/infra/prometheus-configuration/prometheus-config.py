"""
This script needs to run on the k8s cluster that will be scraping metrics from appliance prometheus instances
"""

from argparse import Argument<PERSON>arser
from typing import Any, Dict, List, Optional

import structlog
import yaml

log = structlog.get_logger("hakimo", module="Prometheus Federation")

FEDERATION_FRP_TEMPLATE: Dict[str, Any] = {
    "job_name": "federate",
    "scrape_interval": "30s",
    "scrape_timeout": "30s",
    "honor_labels": True,
    "honor_timestamps": False,
    "scheme": "http",
    "metrics_path": "/prometheus/federate",
    "metric_relabel_configs": [
        {
            "source_labels": ["id"],
            "regex": "^static-agent$",
            "action": "drop",
        }
    ],
    "params": {
        "match[]": [
            '{job="prometheus-.+"}',
            '{__name__=~"kube_.*|cluster_.*|node_.*|container_.*|flower_.*|hakimo_.*|ADD_.*|HAKIMO_.*|VIDEO_.*|ALARM_.*|PENDING_.*|flask_.*|mysql_.*|redis_.*"}',
        ]
    },
}
FRP_TUNNEL_ADDRESS: str = (
    "secure-frp-internal.support-server.svc.cluster.local"
)
FRP_IGNORED_TENANTS: List[str] = ["hulu", "omega", "lucid", "trl"]
PUSHPROX_SERVICE: str = "http://pushprox.monitoring.svc.cluster.local:8080"
PUSHPROX_TEMPLATE: Dict[str, Any] = {
    "job_name": "federate_pushprox",
    "scrape_interval": "5m",
    "metrics_path": "/federate",
    "scrape_timeout": "1m",
    "honor_labels": True,
    "honor_timestamps": False,
    "proxy_url": f"{PUSHPROX_SERVICE}",
    "metric_relabel_configs": [
        {
            "source_labels": ["id"],
            "regex": "^static-agent$",
            "action": "drop",
        }
    ],
    "params": {
        "match[]": [
            '{job="prometheus-.+"}',
            '{__name__=~"kube_.*|cluster_.*|node_.*|container_.*|flower_.*|hakimo_.*|ADD_.*|HAKIMO_.*|VIDEO_.*|ALARM_.*|PENDING_.*|flask_.*|mysql_.*|redis_.*"}',
        ]
    },
}
PUSHPROX_IGNORED_TENANTS: List[str] = []
GPU_TEMPLATE: Dict[str, Any] = {
    "job_name": "gpu-metrics",
    "scrape_interval": "1s",
    "metrics_path": "/metrics",
    "scheme": "http",
    "kubernetes_sd_configs": [
        {"role": "endpoints", "namespaces": {"names": ["gpu-operator"]}}
    ],
    "relabel_configs": [
        {
            "source_labels": ["__meta_kubernetes_pod_node_name"],
            "action": "replace",
            "target_label": "kubernetes_node",
        }
    ],
}


def parser():
    parser = ArgumentParser(description="Prometheus config generator")
    parser.add_argument(
        "--frp-config",
        type=str,
        default=f"helm/frp/values-secure.yaml",
        help="Path to frp config",
    )
    parser.add_argument(
        "--pushprox-config",
        type=str,
        default=f"helm/pushprox/values.yaml",
        help="Path to pushprox config",
    )
    parser.add_argument(
        "--output",
        type=str,
        default=f"infra/prometheus-configuration/additional_config_template.yaml",
        help="Path to output config",
    )
    return parser


def generate_frp_static_configs(
    config_dict: Dict[str, Any], ignored_tenants: Optional[List[str]] = None
):
    if ignored_tenants is None:
        ignored_tenants = []

    static_configs = []
    for tenant_config in config_dict["tenants"]:
        if str(tenant_config["name"]).lower() in ignored_tenants:
            continue
        tenant_config_dict = {
            "targets": [
                (FRP_TUNNEL_ADDRESS + ":" + str(tenant_config["port"]))
            ],
            "labels": {"tenantName": str(tenant_config["name"])},
        }
        static_configs.append(tenant_config_dict)
    return static_configs


def generate_pushprox_static_configs(
    config_dict: Dict[str, Any], ignored_tenants: Optional[List[str]] = None
):
    if ignored_tenants is None:
        ignored_tenants = []

    static_configs = []
    for tenant in config_dict["tenants"]:
        if str(tenant).lower() in ignored_tenants:
            continue
        tenant_config_dict = {
            "targets": [tenant],
            "labels": {"tenantName": tenant},
        }
        static_configs.append(tenant_config_dict)
    return static_configs


def dump_config(destination_path: str, templates: Dict[str, Any]):
    with open(destination_path, "w") as out_stream:
        yaml.dump(
            templates,
            out_stream,
            default_flow_style=False,
            sort_keys=False,
        )


def load_yaml_config(path: str):
    with open(path, "r") as stream:
        try:
            config_dict = dict(yaml.safe_load(stream))
        except yaml.YAMLError as exc:
            log.error("Failed to load yaml config", exc_info=exc)
        except Exception as ex:
            log.error("Error occurred while loading yaml config", exc_info=ex)
    return config_dict


def main():
    args = parser().parse_args()
    frp_config_dict = load_yaml_config(args.frp_config)
    FEDERATION_FRP_TEMPLATE["static_configs"] = generate_frp_static_configs(
        frp_config_dict, ignored_tenants=FRP_IGNORED_TENANTS
    )
    pushprox_config_dict = load_yaml_config(args.pushprox_config)
    PUSHPROX_TEMPLATE["static_configs"] = generate_pushprox_static_configs(
        pushprox_config_dict, ignored_tenants=PUSHPROX_IGNORED_TENANTS
    )
    dump_config(
        args.output,
        templates=[FEDERATION_FRP_TEMPLATE, PUSHPROX_TEMPLATE, GPU_TEMPLATE],
    )


if __name__ == "__main__":
    main()
