apiVersion: v1
kind: Service
metadata:
  name: rds-staging
  namespace: staging
spec:
  selector:
    deploymentPod: "rds-staging"
  ports:
    - protocol: TCP
      port: 3306
      targetPort: rds
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rds-staging-connector
  namespace: staging
  labels:
    deploymentPod: rds-staging
spec:
  replicas: 1
  selector:
    matchLabels:
      deploymentPod: rds-staging
  template:
    metadata:
      labels:
        deploymentPod: rds-staging
    spec:
      containers:
        - name: rds-forward
          image: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-util/alpine/socat:latest
          args: [
              "tcp-listen:3306,fork,reuseaddr",
              # Change this incase RDS IP changes
              "tcp-connect:staging-rds-writer.hk-internal:3306",
            ]
          ports:
            - containerPort: 3306
              name: rds
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 50Mi
