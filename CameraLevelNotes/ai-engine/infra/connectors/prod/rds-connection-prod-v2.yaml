apiVersion: v1
kind: Service
metadata:
  name: rds-prod-v2
  namespace: prod
spec:
  selector:
    deploymentPod: "rds-prod-v2"
  ports:
    - protocol: TCP
      port: 3306
      targetPort: rds
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rds-prod-v2-connector
  namespace: prod
  labels:
    deploymentPod: rds-prod-v2
spec:
  replicas: 1
  selector:
    matchLabels:
      deploymentPod: rds-prod-v2
  template:
    metadata:
      labels:
        deploymentPod: rds-prod-v2
    spec:
      containers:
        - name: rds-forward
          image: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-util/alpine/socat:latest
          args: [
              "tcp-listen:3306,fork,reuseaddr",
              # Change this incase RDS IP changes
              "tcp-connect:hakimo-prod-rds-1-cluster.cluster-crkez1tyf4pc.us-west-2.rds.amazonaws.com:3306",
            ]
          ports:
            - containerPort: 3306
              name: rds
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 50Mi
