apiVersion: helm.fluxcd.io/v1
kind: HelmRelease
metadata:
  name: fluentd-aws
  namespace: monitoring
  annotations:
    fluxcd.io/automated: "false"
spec:
  releaseName: fluentd-aws
  chart:
    repository: https://kokuwaio.github.io/helm-charts
    name: fluentd-elasticsearch
    version: 11.14.0
  values:
    fluentdLogFormat: json
    resources:
      limits:
        cpu: 1000m
        memory: 2000Mi
      requests:
        cpu: 100m
        memory: 200Mi
    updateStrategy:
      rollingUpdate:
        maxSurge: 0
        maxUnavailable: 4
      type: RollingUpdate
    elasticsearch:
      hosts:
        - "search-hakimo-lm5gqsbguzfjva237qv5ride2i.us-west-2.es.amazonaws.com:443"
      # - hakimo-es.es.us-west-2.aws.found.io:9243
      sslVerify: true
      scheme: https
      suppressTypeName: true
      log400Reason: true
      logLevel: "info"
      buffer:
        enabled: true
        flush_interval: 10s
        chunk_limit_size: 2MB
        queue_limit_length: 32
        flushThreadCount: 20
        chunkLimitSize: "100M"
        overflowAction: "throw_exception"
      auth:
        enabled: true
        user: elastic
        existingSecret:
          name: elastic-aws
          # name: elastic-hosted
          key: elastic
      requestTimeout: "60s"
      template:
        enabled: true
        overwrite: true
        name: fluentd-template
        file: fluentd-template.json
        content: |-
          {
            "index_patterns": [
                "logstash-*"
            ],
            "settings": {
                "index": {
                    "number_of_replicas": 0
                    , "number_of_shards": 3
                    , "mapping.total_fields.limit": 5000
                }
            },
            "mappings": {
              "dynamic_templates": [
                {
                  "nested_dict_no_index": {
                    "match_mapping_type": "*",
                    "match_pattern": "regex",
                    "path_match": ".+\\..+\\..+",
                    "path_unmatch": "(kubernetes\\..+|rl.kubernetes.pod_name)",
                    "mapping":{
                      "type": "object",
                      "enabled": "false"
                    }
                  }
                }
              ]
            }
          }
    configMaps:
      useDefaults:
        containersInputConf: false
        outputConf: false
    extraConfigMaps:
      containers.input.conf: |-
        <source>
          @id fluentd-containers.log
          @type tail
          path /var/log/containers/*.log
          pos_file /var/log/containers.log.pos
          tag raw.kubernetes.*
          enable_stat_watcher false
          read_from_head true
          <parse>
            @type multi_format
            <pattern>
              format json
              time_key time
              time_format %Y-%m-%dT%H:%M:%S.%NZ
            </pattern>
            <pattern>
              format /^(?<time>.+) (?<stream>stdout|stderr) [^ ]* (?<log>.*)$/
              time_format %Y-%m-%dT%H:%M:%S.%N%:z
            </pattern>
          </parse>
        </source>

        # Detect exceptions in the log output and forward them as one log entry.
        <match raw.kubernetes.**>
          @id raw.kubernetes
          @type detect_exceptions
          remove_tag_prefix raw
          message log
          stream stream
          multiline_flush_interval 10
          max_bytes 500000
          max_lines 5000
        </match>
        
        # Concatenate multi-line logs
        <filter **>
          @id filter_concat
          @type concat
          key message
          multiline_end_regexp /\n$/
          separator ""
          timeout_label @NORMAL
          flush_interval 10
        </filter>
        
        # Enriches records with Kubernetes metadata
        <filter kubernetes.**>
          @id filter_kubernetes_metadata
          @type kubernetes_metadata
        </filter>

        # Fixes json fields in Elasticsearch
        <filter kubernetes.**>
          @id filter_parser
          @type parser
          key_name log
          reserve_time true
          reserve_data true
          remove_key_name_field true
          <parse>
            @type multi_format
            <pattern>
              format json
            </pattern>
            <pattern>
              format none
            </pattern>
          </parse>
        </filter>

        # Exclude logs with "rl.level" as "debug"
        <filter **>
          @type grep
          <exclude>
            key $.rl.level
            pattern debug
          </exclude>
        </filter>

      metrics.conf: |-
        <filter **>
          @type prometheus
          <metric>
            name fluentd_input_status_num_records_total
            type counter
            desc The total number of incoming records
            <labels>
              tag ${tag}
              hostname ${hostname}
            </labels>
          </metric>
        </filter>
      hash.output.conf: |-
        <filter **>
            @type elasticsearch_genid
            hash_id_key _hash  # storing generated hash id key (default is _hash)
            use_record_as_seed true
            record_keys []
            use_entire_record true
            separator _
            hash_type sha256
            include_time_in_seed false
            include_tag_in_seed false
           </filter>
      output.conf: |-
        # handle timeout log lines from concat plugin
        <match **>
          @type relabel
          @label @NORMAL
        </match>

        <label @NORMAL>
          <match **>
            @type copy
            <store>
              @id elasticsearch
              @type "#{ENV['OUTPUT_TYPE']}"
              @log_level "#{ENV['OUTPUT_LOG_LEVEL']}"
              id_key _hash
              include_tag_key "#{ENV['OUTPUT_INCLUDE_TAG_KEY']}"
              hosts "#{ENV['OUTPUT_HOSTS']}"
              path "#{ENV['OUTPUT_PATH']}"
              scheme "#{ENV['OUTPUT_SCHEME']}"
              ssl_verify "#{ENV['OUTPUT_SSL_VERIFY']}"
              ssl_version "#{ENV['OUTPUT_SSL_VERSION']}"
              type_name "#{ENV['OUTPUT_TYPE_NAME']}"
              user "#{ENV['OUTPUT_USER']}"
              password "#{ENV['OUTPUT_PASSWORD']}"
              logstash_format "#{ENV['LOGSTASH_FORMAT']}"
              logstash_dateformat "#{ENV['LOGSTASH_DATEFORMAT']}"
              logstash_prefix "#{ENV['LOGSTASH_PREFIX']}"
              logstash_prefix_separator "#{ENV['LOGSTASH_PREFIX_SEPARATOR']}"
              template_name "#{ENV['TEMPLATE_NAME']}"
              template_file "#{ENV['TEMPLATE_FILE']}"
              template_overwrite "#{ENV['TEMPLATE_OVERWRITE']}"
              use_legacy_template "#{ENV['USE_LEGACY_TEMPLATE']}"
              log_es_400_reason "#{ENV['OUTPUT_LOG_400_REASON']}"
              reconnect_on_error "#{ENV['OUTPUT_RECONNECT_ON_ERROR']}"
              reload_on_failure "#{ENV['OUTPUT_RELOAD_ON_FAILURE']}"
              reload_connections "#{ENV['OUTPUT_RELOAD_CONNECTIONS']}"
              request_timeout "#{ENV['OUTPUT_REQUEST_TIMEOUT']}"
              suppress_type_name "#{ENV['OUTPUT_SUPPRESS_TYPE_NAME']}"
              ;bulk_message_request_threshold 5000
              <buffer>
                @type "#{ENV['OUTPUT_BUFFER_TYPE']}"
                path "#{ENV['OUTPUT_BUFFER_PATH']}"
                flush_mode "#{ENV['OUTPUT_BUFFER_FLUSH_MODE']}"
                retry_type "#{ENV['OUTPUT_BUFFER_RETRY_TYPE']}"
                flush_thread_count "#{ENV['OUTPUT_BUFFER_FLUSH_THREAD_TYPE']}"
                flush_interval "#{ENV['OUTPUT_BUFFER_FLUSH_INTERVAL']}"
                retry_forever "#{ENV['OUTPUT_BUFFER_RETRY_FOREVER']}"
                retry_max_interval "#{ENV['OUTPUT_BUFFER_RETRY_MAX_INTERVAL']}"
                chunk_limit_size "#{ENV['OUTPUT_BUFFER_CHUNK_LIMIT']}"
                chunk_limit_records 50000
                ;queue_limit_length "#{ENV['OUTPUT_BUFFER_QUEUE_LIMIT']}"
                overflow_action "#{ENV['OUTPUT_BUFFER_OVERFLOW_ACTION']}"
              </buffer>
            </store>
            <store>
              @type prometheus
              <metric>
                name fluentd_output_status_num_records_total
                type counter
                desc The total number of outgoing records
                <labels>
                  tag ${tag}
                  hostname ${hostname}
                </labels>
              </metric>
            </store>
          </match>
        </label>
    serviceMonitor:
      enabled: true
