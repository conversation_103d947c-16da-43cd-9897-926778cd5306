import time
import typing
from dataclasses import dataclass
from http import HTTPStatus

import redis
import structlog

import controller as ctrl
from common_utils.metrics_definitions import (
    LATEST_LOCATION_ALARM_RESOLUTION_TIME,
    LOCATION_ALARM_RESOLUTION_TIME,
    LOCATION_ALARM_RESOLUTION_TIME_HISTOGRAM,
)
from config import backend_config as config
from controller.location_alarms.location_alarm_filters import (
    LocationAlarmFilters,
)
from gateway.common.endpoint_guard import guard_endpoint
from interfaces.location_alarms import (
    LocationAlarmStatus,
    LocationAlarmUpdateType,
    TwilioLogType,
)
from models_rds.location_alarms.location_alarm_update import (
    LocationAlarmUpdates,
)
from models_rds.raw_alarms import RawAlarms
from models_rds.users import Users

log = structlog.get_logger("hakimo.orm.alarm", module="Alarm ORM")

# Initialize Redis connection
redis_client = redis.Redis(
    host=config.HAIE.redis[
        "visionServiceName"
    ],  # Replace with your Redis host
    port=config.HAIE.redis["port"],  # Replace with your Redis port
    db=0,  # Default database
    decode_responses=True,
)


@dataclass
class LocationAlarmUpdateInfo:
    status: typing.Optional[LocationAlarmStatus] = None
    comment: typing.Optional[str] = None
    view: typing.Optional[bool] = None
    twilio_log_type: typing.Optional[TwilioLogType] = None
    twilio_ssid: typing.Optional[str] = None
    twilio_calling_to_name: typing.Optional[str] = None

    def get_update_type(self) -> LocationAlarmUpdateType:
        if self.view:
            return LocationAlarmUpdateType.VIEW_ALARM
        elif self.status:
            return LocationAlarmUpdateType.CHANGE_STATUS
        elif self.comment:
            return LocationAlarmUpdateType.ADD_COMMENT
        elif self.twilio_log_type:
            return LocationAlarmUpdateType.CALL_SMS_LOGS
        else:
            raise KeyError("Invalid update payload")

    def validate(self):
        if (
            not self.comment
            and not self.status
            and not self.view
            and not self.twilio_log_type
        ):
            raise KeyError(
                "Atleast one of comment, view, twilio log type or"
                " status must be present"
            )


def get_update_info(update_payload: typing.Dict) -> LocationAlarmUpdateInfo:
    if status_str := update_payload.get("status"):
        status = LocationAlarmStatus(status_str)
    else:
        status = None
    update_info = LocationAlarmUpdateInfo(
        status=status,
        comment=update_payload.get("comment"),
        view=update_payload.get("viewed"),
        twilio_log_type=update_payload.get("twilio_log_type"),
        twilio_ssid=update_payload.get("twilio_ssid"),
        twilio_calling_to_name=update_payload.get("twilio_calling_to_name"),
    )
    update_info.validate()
    return update_info


class LocationAlarmUpdater:
    def __init__(self, controller: ctrl.ControllerMap):
        self._ctrl_map = controller

    @guard_endpoint(["location_alarm:update"])
    def update_location_alarm(
        self,
        location_alarm_id: typing.Optional[int],
        update_payload: typing.Dict,
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        if location_alarm_id is None:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Invalid patch payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        try:
            update_info = get_update_info(update_payload)
        except KeyError:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Invalid patch payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        location_alarms = self._ctrl_map.location_alarms.get_location_alarms(
            LocationAlarmFilters(location_alarm_ids=[location_alarm_id])
        )

        if not location_alarms or not location_alarms[0]:
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Location Alarm not found"
            return (api_resp, HTTPStatus.NOT_FOUND.value)
        location_alarm = location_alarms[0]
        update_type = update_info.get_update_type()
        if update_type == LocationAlarmUpdateType.VIEW_ALARM:
            if (role := self._ctrl_map.role.get_role(user.role_id)) is None:
                api_resp["status"] = HTTPStatus.UNAUTHORIZED.value
                api_resp["message"] = "Invalid user"
                return (api_resp, HTTPStatus.UNAUTHORIZED.value)
            if role.name == "Hakimo Support":
                api_resp["status"] = HTTPStatus.OK.value
                api_resp["message"] = "No need to mark alarm as viewed"
                return (api_resp, HTTPStatus.OK.value)
        if update_type == LocationAlarmUpdateType.CALL_SMS_LOGS:
            twilio_log_type = update_info.twilio_log_type
            if twilio_log_type not in {
                TwilioLogType.TWILIO_CALLS.value,
                TwilioLogType.Twilio_SMS.value,
            }:
                api_resp["message"] = "Invalid Twilio log type"
                api_resp["status"] = HTTPStatus.BAD_REQUEST.value
                return api_resp, HTTPStatus.BAD_REQUEST.value
            location_alarm_update = LocationAlarmUpdates(
                location_alarm_id=location_alarm_id,
                update_type=update_type,
                user_id=user.uuid,
                update_details={
                    "type": update_info.twilio_log_type,
                    "ssid": update_info.twilio_ssid,
                    "calling_to_name": update_info.twilio_calling_to_name,
                },
            )
        else:
            location_alarm_update = LocationAlarmUpdates(
                location_alarm_id=location_alarm.int_id,
                update_type=update_type,
                update_status=update_info.status,
                update_text=update_info.comment,
                user_id=user.uuid,
            )
        if update_type in {
            LocationAlarmUpdateType.ADD_COMMENT,
            LocationAlarmUpdateType.VIEW_ALARM,
            LocationAlarmUpdateType.CALL_SMS_LOGS,
        }:
            self._ctrl_map.location_alarms.update_location_alarm(
                location_alarm_update=location_alarm_update,
                location_alarm=location_alarm,
            )
        else:
            self._ctrl_map.location_alarms.update_location_alarm_status(
                new_status=update_info.status,
                update_text=update_info.comment,
                location_alarm=location_alarm,
                user_id=user.uuid,
            )
            if (
                update_info.status is not None
                and update_info.comment is not None
                and update_info.status == LocationAlarmStatus.RESOLVED
            ):
                raw_alarms: typing.List[RawAlarms] = location_alarm.raw_alarms
                tenant_id = raw_alarms[0].tenant_id
                tenant_config = self._ctrl_map.tenant.get_config(tenant_id)
                num_cameras = len(
                    set(raw_alarm.source_entity_id for raw_alarm in raw_alarms)
                )
                if (
                    tenant_config is not None
                    and tenant_config.checkVectorDb
                    and num_cameras == 1
                ):
                    self._ctrl_map.pinecone.update_alarm_labels(
                        [raw_alarm.uuid for raw_alarm in raw_alarms],
                        raw_alarms[0].source_entity_id,
                        update_info.comment,
                    )

                # Update last resolution time in Redis
                last_resolution_key = (
                    f"location_alarm_last_resolution_time:{user.email}"
                )
                current_timestamp = int(time.time())
                redis_client.set(last_resolution_key, current_timestamp)

                # Calculate and record alarm resolution time metric
                pick_time_key = f"location_alarm_pick_time:{location_alarm_id}:{user.email}"
                try:
                    pick_time_str = redis_client.get(pick_time_key)
                    if pick_time_str:
                        pick_time = int(pick_time_str)
                        resolution_time_seconds = current_timestamp - pick_time
                        if (
                            resolution_time_seconds >= 0
                        ):  # Ensure valid time difference
                            # Get tenant_id for the metric label
                            tenant_id = (
                                raw_alarms[0].tenant_id
                                if raw_alarms
                                else "unknown"
                            )
                            LOCATION_ALARM_RESOLUTION_TIME.labels(
                                tenant=tenant_id, operator_id=user.email
                            ).observe(resolution_time_seconds)
                            LOCATION_ALARM_RESOLUTION_TIME_HISTOGRAM.labels(
                                tenant=tenant_id, operator_id=user.email
                            ).observe(resolution_time_seconds)
                            LATEST_LOCATION_ALARM_RESOLUTION_TIME.labels(
                                tenant=tenant_id, operator_id=user.email
                            ).set(resolution_time_seconds)
                        # Clean up the pick time key after use
                        redis_client.delete(pick_time_key)
                except (ValueError, TypeError) as e:
                    log.warning(
                        "Failed to calculate alarm resolution time",
                        location_alarm_id=location_alarm_id,
                        operator_id=user.email,
                        error=str(e),
                    )

        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Location Alarm updated successfully"
        return (api_resp, HTTPStatus.OK.value)

    def update_location_alarm_public(
        self,
        location_alarm_id: typing.Optional[int],
        update_payload: typing.Dict,
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        if location_alarm_id is None:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Invalid patch payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        try:
            update_info = get_update_info(update_payload)
        except KeyError:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Invalid patch payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        location_alarms = self._ctrl_map.location_alarms.get_location_alarms(
            LocationAlarmFilters(location_alarm_ids=[location_alarm_id])
        )

        if not location_alarms or not location_alarms[0]:
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Location Alarm not found"
            return (api_resp, HTTPStatus.NOT_FOUND.value)
        location_alarm = location_alarms[0]
        update_type = update_info.get_update_type()
        if update_type == LocationAlarmUpdateType.VIEW_ALARM:
            if (role := self._ctrl_map.role.get_role(user.role_id)) is None:
                api_resp["status"] = HTTPStatus.UNAUTHORIZED.value
                api_resp["message"] = "Invalid user"
                return (api_resp, HTTPStatus.UNAUTHORIZED.value)
            if role.name == "Hakimo Support":
                api_resp["status"] = HTTPStatus.OK.value
                api_resp["message"] = "No need to mark alarm as viewed"
                return (api_resp, HTTPStatus.OK.value)
        if update_type == LocationAlarmUpdateType.CALL_SMS_LOGS:
            twilio_log_type = update_info.twilio_log_type
            if twilio_log_type not in {
                TwilioLogType.TWILIO_CALLS.value,
                TwilioLogType.Twilio_SMS.value,
            }:
                api_resp["message"] = "Invalid Twilio log type"
                api_resp["status"] = HTTPStatus.BAD_REQUEST.value
                return api_resp, HTTPStatus.BAD_REQUEST.value
            location_alarm_update = LocationAlarmUpdates(
                location_alarm_id=location_alarm_id,
                update_type=update_type,
                user_id=user.uuid,
                update_details={
                    "type": update_info.twilio_log_type,
                    "ssid": update_info.twilio_ssid,
                    "calling_to_name": update_info.twilio_calling_to_name,
                },
            )
        else:
            location_alarm_update = LocationAlarmUpdates(
                location_alarm_id=location_alarm.int_id,
                update_type=update_type,
                update_status=update_info.status,
                update_text=update_info.comment,
                user_id=user.uuid,
            )
        if update_type in {
            LocationAlarmUpdateType.ADD_COMMENT,
            LocationAlarmUpdateType.VIEW_ALARM,
            LocationAlarmUpdateType.CALL_SMS_LOGS,
        }:
            self._ctrl_map.location_alarms.update_location_alarm(
                location_alarm_update=location_alarm_update,
                location_alarm=location_alarm,
            )
        else:
            self._ctrl_map.location_alarms.update_location_alarm_status(
                new_status=update_info.status,
                update_text=update_info.comment,
                location_alarm=location_alarm,
                user_id=user.uuid,
            )
            if (
                update_info.status is not None
                and update_info.comment is not None
                and update_info.status == LocationAlarmStatus.RESOLVED
            ):
                raw_alarms: typing.List[RawAlarms] = location_alarm.raw_alarms
                tenant_id = raw_alarms[0].tenant_id
                tenant_config = self._ctrl_map.tenant.get_config(tenant_id)
                num_cameras = len(
                    set(raw_alarm.source_entity_id for raw_alarm in raw_alarms)
                )
                if (
                    tenant_config is not None
                    and tenant_config.checkVectorDb
                    and num_cameras == 1
                ):
                    self._ctrl_map.pinecone.update_alarm_labels(
                        [raw_alarm.uuid for raw_alarm in raw_alarms],
                        raw_alarms[0].source_entity_id,
                        update_info.comment,
                    )

                # Update last resolution time in Redis
                last_resolution_key = (
                    f"location_alarm_last_resolution_time:{user.email}"
                )
                current_timestamp = int(time.time())
                redis_client.set(last_resolution_key, current_timestamp)

                # Calculate and record alarm resolution time metric
                pick_time_key = f"location_alarm_pick_time:{location_alarm_id}:{user.email}"
                try:
                    pick_time_str = redis_client.get(pick_time_key)
                    if pick_time_str:
                        pick_time = int(pick_time_str)
                        resolution_time_seconds = current_timestamp - pick_time
                        if (
                            resolution_time_seconds >= 0
                        ):  # Ensure valid time difference
                            # Get tenant_id for the metric label
                            tenant_id = (
                                raw_alarms[0].tenant_id
                                if raw_alarms
                                else "unknown"
                            )
                            LOCATION_ALARM_RESOLUTION_TIME.labels(
                                tenant=tenant_id, operator_id=user.email
                            ).observe(resolution_time_seconds)
                            LOCATION_ALARM_RESOLUTION_TIME_HISTOGRAM.labels(
                                tenant=tenant_id, operator_id=user.email
                            ).observe(resolution_time_seconds)
                            LATEST_LOCATION_ALARM_RESOLUTION_TIME.labels(
                                tenant=tenant_id, operator_id=user.email
                            ).set(resolution_time_seconds)
                        # Clean up the pick time key after use
                        redis_client.delete(pick_time_key)
                except (ValueError, TypeError) as e:
                    log.warning(
                        "Failed to calculate alarm resolution time",
                        location_alarm_id=location_alarm_id,
                        operator_id=user.email,
                        error=str(e),
                    )

        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Location Alarm updated successfully"
        return (api_resp, HTTPStatus.OK.value)
