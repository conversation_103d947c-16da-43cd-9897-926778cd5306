import os
import os.path as osp
import shutil
import tempfile
import time
import typing
import uuid
from datetime import datetime, timedelta, timezone
from http import HTTPStatus

import opentracing
import pytz
import structlog
from opentracing import Format
from werkzeug.datastructures import FileStorage

import controller as ctrl
import interfaces.alarm as alarm_module
from common_utils.audio_helper import AudioPayload
from common_utils.bridge.msg_sender import Msg<PERSON>ender
from common_utils.cloudfront_utils import CloudFrontUtil
from common_utils.ffmpeg_helpers import transcode_audio
from common_utils.helper_utils import is_onprem_deployment
from common_utils.io_helpers import get_constructed_file_name
from common_utils.metrics_definitions import (
    HAKIMO_HLS_REQUEST_COUNTER,
    HAKIMO_HLS_SYNC_LATENCY_E2E,
    HAKIMO_HLS_SYNC_LATENCY_FROM_MSG_BUS,
)
from common_utils.network_utils import get_ip_from_url
from common_utils.s3_utils import (
    construct_S3_file_name,
    get_last_updated_time,
    upload_file_to_s3,
)
from common_utils.string_helpers import combine_tenant_and_ip
from common_utils.time_utils import utc_to_milliseconds
from config import backend_config as config
from gateway.common.endpoint_guard import guard_endpoint
from gateway.http_api.orm.camera.common import livestream_limit_reached
from gateway.http_api.orm.user.utils import get_user_tenant_ids
from integ.audio.sps.audio_publisher import AudioPublisher
from integ.audio.sps.audio_sqs_publisher import AudioSQSPayloadPublisher
from interfaces.escalations import EscalationUpdateType
from interfaces.location_alarms import LocationAlarmUpdateType
from models_rds.alarm_updates import AlarmUpdates
from models_rds.audio_devices import AudioDevices
from models_rds.cameras import Cameras
from models_rds.escalation_update import EscalationUpdates
from models_rds.livestream_details import LivestreamDetails
from models_rds.location_alarms import LocationAlarmUpdates
from models_rds.users import Users

log = structlog.get_logger(
    "hakimo.orm.camera", module="Camera Livestream Endpoint"
)

CF_URL_EXPIRY_TIME_SECONDS = 24 * 60 * 60
SIGNED_URL_GENERATION_THRESHOLD_SECONDS = 30 * 60


class CameraLiveStream:
    def __init__(self, controller: ctrl.ControllerMap):
        self._ctrl_map = controller
        self._msg_sender = MsgSender(controller.msg)
        if not is_onprem_deployment():
            cloudfront_config = config.gateway().get("cloudfront", {})
            self._cf_configured = True
            self._s3_bucket = cloudfront_config["origin_s3_bucket"]
            self._cf_util = CloudFrontUtil(
                "/secrets/cf_private_key.pem",
                cloudfront_config[
                    "public_key_id"
                ],  # public key identifer uploaded in CloudFront console
            )
            self._cf_url = cloudfront_config["url"]
        else:
            self._cf_configured = False

    @guard_endpoint(["camera/live:view"])
    def get_livestream_url(
        self,
        camera_id: str,
        user: typing.Optional[Users] = None,
    ) -> typing.Tuple[typing.Dict[str, typing.Any], int]:
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        request_start_time = time.time()
        tenant_ids = get_user_tenant_ids(user)
        cam = self._ctrl_map.camera.get_camera_by_id(camera_id)
        if cam is None or cam.tenant_id not in tenant_ids:
            raise ValueError("Camera not found")
        tenant_id = cam.tenant_id

        if not self._cf_configured:
            log.warning(
                "Attempt to get livestream URL, but CF not configured.",
                tenant_id=tenant_id,
            )
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Livestreaming not suppported."
            return api_resp, HTTPStatus.NOT_FOUND.value

        tenant_config = self._ctrl_map.tenant.get_config(tenant_id)
        if (
            tenant_config
            and not tenant_config.hlsEnabled
            and not tenant_config.hlsV2Enabled
        ):
            log.warning(
                "Livestreaming not configured for the tenant.",
                tenant_id=tenant_id,
            )
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Livestreaming not suppported."
            return api_resp, HTTPStatus.NOT_FOUND.value

        current_livestreams = (
            self._ctrl_map.livestream_details.get_livestreams(
                tenant_id=tenant_id,
                is_livestream_running=True,
            )
        )

        (
            ls_limit_reached,
            current_livestream_count,
        ) = livestream_limit_reached(current_livestreams, tenant_config)
        if ls_limit_reached:
            log.warning(
                "Maximum number of livestream requests reached.",
                tenant_id=tenant_id,
                current_livestream_count=current_livestream_count,
            )
            api_resp["status"] = HTTPStatus.TOO_MANY_REQUESTS.value
            api_resp["message"] = (
                "Maximum number of livestream requests reached. Please try after some time."
            )
            return api_resp, HTTPStatus.TOO_MANY_REQUESTS.value

        index_file_path = f"{tenant_id}/{cam.client_camera_id}/index.m3u8"

        if tenant_config.hlsV2Enabled:
            (status, response_str) = self._trigger_livestream(
                tenant_id,
                user.uuid,
                cam,
                index_file_path,
                request_start_time,
                hls_version="v2",
            )
        else:
            (status, response_str) = self._trigger_livestream(
                tenant_id, user.uuid, cam, index_file_path, request_start_time
            )

        HAKIMO_HLS_REQUEST_COUNTER.labels(
            tenant=tenant_id,
            s3_bucket=self._s3_bucket,
            is_success=status,
        ).inc()

        if status:
            api_resp["status"] = HTTPStatus.OK.value
            api_resp["message"] = "Processed Successfully"
            api_resp["payload"] = response_str
            return api_resp, HTTPStatus.OK.value

        api_resp["status"] = HTTPStatus.NOT_FOUND.value
        api_resp["message"] = response_str
        api_resp["payload"] = {}
        return api_resp, HTTPStatus.NOT_FOUND.value

    def _trigger_livestream(
        self,
        tenant_id: str,
        user_id: str,
        camera: Cameras,
        index_file_path: str,
        request_start_time: float,
        hls_version: str = "v1",
    ) -> typing.Tuple[bool, str]:
        """Post a message to start livestreaming if livestream is not
        running for the specified tenant, and camera. If a request is
        made to trigger the livestream wait and check if the
        associated s3 bucket has the latest content resulting from the
        of triggered sync. If s3 bucket has latest content update
        livestream record in the DB to indicate that livestream is
        running. Generate a signed url to access the livestreamed
        content. Return a tuple indicating status of the livestream
        sync (True) and a cloud front signed url to access the
        livestream (in case of success). In case of failure a Tuple
        indicating the failure return (False) and error message
        providing failure reason."""
        livestream_details_controller = self._ctrl_map.livestream_details
        livestream_entries = livestream_details_controller.get_livestreams(
            tenant_id=tenant_id, camera_id=camera.uuid
        )
        livestream_details = None
        is_livestream_running = False
        if [e for e in livestream_entries if e.is_running]:
            log.debug(
                "Livestream is running.",
                tenant_id=tenant_id,
                camera_id=camera.uuid,
            )
            is_livestream_running = True

        # we run 1 livestream per camera/tenant, but there is a separte signed url
        # for each user. So the livestream might be running, but the signed url
        # might not exist for the user or the signed url for that user might have
        # expired.
        for ls_entry in livestream_entries:
            if ls_entry.user_id == user_id:
                livestream_details = ls_entry

        if not is_livestream_running:
            payload = {
                "action": "start",
                "camera_id": camera.client_camera_id,
            }

            posted_to_msg_bus_at = time.time()
            if hls_version == "v1":
                self._msg_sender.send(
                    tenant_id,
                    f"hip/{camera.integration_type}/hls_stream",
                    payload,
                )
            elif hls_version == "v2":
                self._msg_sender.send(
                    tenant_id,
                    f"hip/{camera.integration_type}/hls_stream_v2",
                    payload,
                )
            else:
                log.error(
                    "Invalid Livestream version. Contact Support",
                    tenant_id=tenant_id,
                    camera_id=camera.client_camera_id,
                )
                return (False, "Invalid Livestream version.")
            # If we start the livestream now wait till the video
            # segment is present in the s3 bucket.
            attempts = 80
            duration_since_last_update_seconds = 10.0

            found_recent_stream = False
            stream_requested_at = pytz.utc.localize(datetime.utcnow())
            camera_identifier = f"{camera.uuid}:{camera.client_camera_id}"
            log.info(
                "Waiting for livestream index file to be updated",
                tenant_id=tenant_id,
                camera_id=camera_identifier,
            )
            while attempts:
                time.sleep(0.25)
                last_updated_at = get_last_updated_time(
                    bucket_name=self._s3_bucket, path=index_file_path
                )

                if last_updated_at is not None:
                    if (
                        last_updated_at >= stream_requested_at
                        or (
                            stream_requested_at - last_updated_at
                        ).total_seconds()
                        <= duration_since_last_update_seconds
                    ):
                        found_recent_stream = True
                        if last_updated_at >= stream_requested_at:
                            # update latency metrics only if stream
                            # was started because of the current
                            # request we made, not if it was alaredy
                            # running.
                            HAKIMO_HLS_SYNC_LATENCY_FROM_MSG_BUS.labels(
                                tenant=tenant_id, s3_bucket=self._s3_bucket
                            ).observe((time.time() - posted_to_msg_bus_at))
                        break

                attempts -= 1

            if last_updated_at is None:
                log.warning(
                    "Livestream video not found.",
                    tenant_id=tenant_id,
                    camera_id=camera_identifier,
                )
                return (False, "Livestream video not found.")

            if not found_recent_stream:
                log.warning(
                    "Livestream video is not recent.",
                    tenant_id=tenant_id,
                    camera_id=camera_identifier,
                    last_updated_at=last_updated_at,
                )
                return (False, "Livestream video is not recent.")

        # No livestream record for the users, so the livestream
        # requested first time for the tid,camera, and user
        # from this endpoint
        if (
            livestream_details is None
            or livestream_details.signed_url is None
            or livestream_details.expires_at_utc is None
        ):
            cf_signed_url, expiry_time = self._get_cloudfront_signed_url(
                index_file_path
            )
            signed_url_obj = LivestreamDetails(
                tenant_id=tenant_id,
                user_id=user_id,
                camera_id=camera.uuid,
                expires_at_utc=expiry_time,
                signed_url=cf_signed_url,
                last_requested_at_utc=datetime.utcnow(),
                is_running=True,
            )
            livestream_details_controller.upsert_livestream_details(
                tenant_id,
                user_id,
                camera.uuid,
                signed_url_obj,
            )
            HAKIMO_HLS_SYNC_LATENCY_E2E.labels(
                tenant=tenant_id, s3_bucket=self._s3_bucket
            ).observe((time.time() - request_start_time))
            return (True, cf_signed_url)

        # we started the livestream, but is the signed URL valid for more than 30 mins ?
        # is so we are good, no need to generate a new signed url.
        if livestream_details.expires_at_utc > datetime.utcnow() + timedelta(
            seconds=SIGNED_URL_GENERATION_THRESHOLD_SECONDS
        ):
            # update requested time and when we reach here livestream will be running,
            # hence independent of previous status set is_livestream_running to True.
            signed_url_obj = LivestreamDetails(
                tenant_id=tenant_id,
                user_id=user_id,
                camera_id=camera.uuid,
                is_running=True,
                last_requested_at_utc=datetime.utcnow(),
            )
            livestream_details_controller.upsert_livestream_details(
                tenant_id,
                user_id,
                camera.uuid,
                signed_url_obj,
            )
            HAKIMO_HLS_SYNC_LATENCY_E2E.labels(
                tenant=tenant_id, s3_bucket=self._s3_bucket
            ).observe((time.time() - request_start_time))
            return (True, livestream_details.signed_url)

        # if not create a new signed url, update and send the updated one.
        cf_signed_url_new, expiry_time = self._get_cloudfront_signed_url(
            index_file_path
        )
        signed_url_obj = LivestreamDetails(
            tenant_id=tenant_id,
            user_id=user_id,
            camera_id=camera.uuid,
            expires_at_utc=expiry_time,
            signed_url=cf_signed_url_new,
            last_requested_at_utc=datetime.utcnow(),
            is_running=True,
        )

        livestream_details_controller.upsert_livestream_details(
            tenant_id, user_id, camera.uuid, signed_url_obj
        )
        HAKIMO_HLS_SYNC_LATENCY_E2E.labels(
            tenant=tenant_id, s3_bucket=self._s3_bucket
        ).observe((time.time() - request_start_time))
        return (True, cf_signed_url_new)

    def _get_cloudfront_signed_url(
        self, index_file_path: str
    ) -> typing.Tuple[str, datetime]:
        """Helper method to generate CF URL valid for 24 hrs. Return a
        tuple containing the signed url and time till it is valid."""
        expiry_time = datetime.utcnow() + timedelta(
            seconds=CF_URL_EXPIRY_TIME_SECONDS
        )
        cf_url = self._cf_util.generate_presigned_url(
            f"{self._cf_url}/{index_file_path}", expire_at=expiry_time
        )
        return (cf_url, expiry_time)

    @guard_endpoint(["camera/audio:add"])
    def add_audio(
        self,
        camera_id: str,
        files: typing.Dict,
        alarm_id: typing.Optional[str] = None,
        location_alarm_id: typing.Optional[str] = None,
        escalation_id: typing.Optional[str] = None,
        user: typing.Optional[Users] = None,
    ) -> typing.Tuple[typing.Dict[str, typing.Any], int]:
        assert user is not None
        utc_now = datetime.now(timezone.utc)
        add_audio_start_time_utc = utc_to_milliseconds(utc_now)
        api_resp: typing.Dict[str, typing.Any] = {}
        camera = self._ctrl_map.camera.get_camera_by_id(camera_id)
        tenant_ids = get_user_tenant_ids(user)
        if camera is None or camera.tenant_id not in tenant_ids:
            log.error(
                "Camera to attach audio not found.",
                camera=camera_id,
            )
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Camera not found."
            return api_resp, HTTPStatus.NOT_FOUND.value
        audio_devices = self._ctrl_map.audio_devices_camera_map.get_audio_devices_for_camera(
            camera_id
        )
        tenant_id = camera.tenant_id
        if not audio_devices:
            log.info(
                "No audio devices active for the camera.",
                tenant_id=tenant_id,
                camera_id=camera.client_camera_id,
            )
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Failed to transmit audio file."
            return api_resp, HTTPStatus.NOT_FOUND.value
        if files.get("audio") is None:
            log.error(
                "No audio file attached.",
                tenant_id=tenant_id,
                camera=camera_id,
            )
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Invalid payload. No audio file attached."
            return api_resp, HTTPStatus.BAD_REQUEST.value

        parent_span = None
        if alarm_id is not None:
            alarm = self._ctrl_map.alarm.by_id(alarm_id, tenant_id=tenant_id)
            if alarm is None:
                log.warning(
                    "Invalid alarm.",
                    tenant_id=tenant_id,
                    alarm_id=alarm_id,
                )
            elif alarm.trace_data is not None:
                parent_span = opentracing.tracer.extract(
                    Format.TEXT_MAP, alarm.trace_data
                )

        trace_tags = {"tenant_id": tenant_id, "camera_id": camera_id}
        if alarm_id is not None:
            trace_tags["alarm_id"] = alarm_id

        audio_file = files["audio"]
        conf = config.gateway()
        s3_bucket = conf["cloudfront"]["origin_s3_bucket"]
        s3_file_name, random_file_path = self._transcode(
            audio_file, camera, audio_devices
        )

        current_time_utc = (
            datetime.utcnow()
            .replace(microsecond=0)
            .strftime("%Y-%m-%d %H:%M:%S")
        )

        audio_talkdown_stat = []
        for audio_device in audio_devices:
            # Publish audio to rabbitmq for manual talkdown
            is_sps_enabled = audio_device.is_sps_enabled
            proxy_url = audio_device.proxy_url
            if is_sps_enabled and (proxy_url is not None and proxy_url != ""):
                speaker_url = audio_device.url
                ip_port = get_ip_from_url(speaker_url)
                queue_name = combine_tenant_and_ip(
                    audio_device.tenant_id, ip_port
                )
                sqs_queue_name = audio_device.queue_name
                audio_sqs_publisher = AudioSQSPayloadPublisher()
                publisher = AudioPublisher(audio_sqs_publisher)
                payload_id = uuid.uuid4()
                stat = publisher.process_and_publish_audio(
                    audio_device,
                    camera_id,
                    camera.client_camera_id,
                    alarm_id,
                    location_alarm_id,
                    "manual",
                    s3_file_name,
                    random_file_path,
                    queue_name,
                    sqs_queue_name,
                    add_audio_start_time_utc,
                    camera.integration_type,
                    current_time_utc,
                    escalation_id,
                    payload_id,
                )
                audio_talkdown_stat.append(stat)
                log.info(
                    "Published audio payload to queue",
                    stat=stat,
                    alarm_id=alarm_id,
                    rm_queue_name=queue_name,
                    sqs_queue_name=sqs_queue_name,
                    escalation_id=escalation_id,
                )
                if not stat:
                    log.error(
                        "Failed to publish audio payload to queue",
                        rm_queue_name=queue_name,
                        sqs_queue_name=sqs_queue_name,
                        tenant_id=tenant_id,
                        camera_id=camera.client_camera_id,
                        proxy_url=proxy_url,
                        device_type=audio_device.device_type,
                        talkdown_type="manual",
                        alarm_id=alarm_id,
                        audio_device_url=audio_device.url,
                        escalation_id=escalation_id,
                    )
                    continue
                if alarm_id:
                    now = datetime.now(timezone.utc)
                    au = AlarmUpdates(
                        alarm_id=alarm_id,
                        event=alarm_module.ALARM_UPDATES_SPEAKER_TALKDOWN_EVENT,
                        user_id=user.uuid,
                        tenant_id=tenant_id,
                        update_timestamp_utc=now,
                        update_details={
                            "audio_file_bucket": s3_bucket,
                            "audio_file_path": s3_file_name,
                            "audio_talkdown_id": str(payload_id),
                        },
                    )
                    self._ctrl_map.alarm.update_alarm(au)
            if is_sps_enabled is False:
                log.info(
                    "SPS has been disabled here. is_sps_enaabled set to 0",
                    tenant_id=tenant_id,
                    camera_id=camera.client_camera_id,
                    audio_device_url=audio_device.url,
                    device_type=audio_device.device_type,
                    talkdown_type="manual",
                    location_alarm_id=location_alarm_id,
                )
            if proxy_url is None or proxy_url == "":
                log.info(
                    "Proxy URL has been set to NULL or is empty",
                    tenant_id=tenant_id,
                    camera_id=camera.client_camera_id,
                    audio_device_url=audio_device.url,
                    device_type=audio_device.device_type,
                    talkdown_type="manual",
                    location_alarm_id=location_alarm_id,
                )
        with opentracing.tracer.start_active_span(
            "transcode-upload-audio", child_of=parent_span, tags=trace_tags
        ):
            upload_file_to_s3(s3_bucket, s3_file_name, random_file_path)
            log.info("S3 file name", s3_file_name=s3_file_name)
        if s3_file_name is None:
            api_resp["status"] = HTTPStatus.INTERNAL_SERVER_ERROR.value
            api_resp["message"] = "Failed to transcode audio file."
            return api_resp, HTTPStatus.INTERNAL_SERVER_ERROR.value

        for audio_device in audio_devices:
            audio_payload = AudioPayload(
                self._msg_sender,
                audio_device,
                camera.client_camera_id,
                talkdown_type="manual",
                s3_file_path=s3_file_name,
            )
            is_sps_enabled = audio_device.is_sps_enabled
            proxy_url = audio_device.proxy_url
            if is_sps_enabled and (proxy_url is not None and proxy_url != ""):
                log.info(
                    "sps is enabled for this audio, "
                    "so not publishing to message bridge",
                    tenant_id=tenant_id,
                    camera_id=camera.client_camera_id,
                )
                continue
            log.info(
                "Publishing audio payload to message bridge",
                tenant_id=tenant_id,
                camera_id=camera.client_camera_id,
                audio_device_url=audio_device.url,
                device_type=audio_device.device_type,
                talkdown_type="manual",
                alarm_id=alarm_id,
            )
            stat = audio_payload.post_message(
                tenant_id,
                source_system=camera.integration_type,
            )
            audio_talkdown_stat.append(stat)
            if not stat:
                log.error(
                    "Failed to publish audio payload to message bridge",
                    tenant_id=tenant_id,
                    camera_id=camera.client_camera_id,
                    audio_device_url=audio_device.url,
                    device_type=audio_device.device_type,
                    talkdown_type="manual",
                    alarm_id=alarm_id,
                )
                continue

            if alarm_id:
                now = datetime.now(timezone.utc)
                au = AlarmUpdates(
                    alarm_id=alarm_id,
                    event=alarm_module.ALARM_UPDATES_SPEAKER_TALKDOWN_EVENT,
                    user_id=user.uuid,
                    tenant_id=tenant_id,
                    update_timestamp_utc=now,
                    update_details={
                        "audio_file_bucket": s3_bucket,
                        "audio_file_path": s3_file_name,
                    },
                )
                self._ctrl_map.alarm.update_alarm(au)

        if location_alarm_id is not None and any(audio_talkdown_stat):
            loc_alarm = (
                self._ctrl_map.location_alarms.get_location_alarm_by_id(
                    location_alarm_id
                )
            )
            if loc_alarm is not None:
                loc_update = LocationAlarmUpdates(
                    location_alarm_id=location_alarm_id,
                    update_type=LocationAlarmUpdateType.MANUAL_TALKDOWN,
                    user_id=user.uuid,
                    update_details={
                        "audio_file_bucket": s3_bucket,
                        "audio_file_path": s3_file_name,
                    },
                )
                self._ctrl_map.location_alarms.update_location_alarm(
                    loc_update, loc_alarm, tenant_id=tenant_id
                )

        if escalation_id is not None and any(audio_talkdown_stat):
            escalation_id_int = int(escalation_id)
            escalation = self._ctrl_map.escalations.get_escalation_by_id(
                escalation_id=escalation_id_int
            )
            if escalation is not None:
                escalation_update = EscalationUpdates(
                    escalation_id=escalation_id_int,
                    update_type=EscalationUpdateType.MANUAL_TALKDOWN,
                    user_id=user.uuid,
                    update_details={
                        "audio_file_bucket": s3_bucket,
                        "audio_file_path": s3_file_name,
                    },
                )
                self._ctrl_map.escalations.create_escalation_updates(
                    escalation_update
                )

        # Send a success message if we could talkdown to atleast one
        # of the audio devices.
        if any(audio_talkdown_stat):
            api_resp["status"] = HTTPStatus.OK.value
            api_resp["message"] = "Audio file processed successfully."
            return api_resp, HTTPStatus.OK.value
        api_resp["status"] = HTTPStatus.INTERNAL_SERVER_ERROR.value
        api_resp["message"] = "Audio talk down failed."
        log.error(
            "Audio talk down failed.",
            tenant_id=tenant_id,
            status=api_resp["status"],
            message=api_resp["message"],
        )
        return api_resp, HTTPStatus.INTERNAL_SERVER_ERROR.value

    def _transcode(
        self,
        audio_file: FileStorage,
        camera: Cameras,
        audio_devices: typing.Sequence[AudioDevices],
    ) -> typing.Tuple[typing.Optional[str], typing.Optional[str]]:
        if not audio_file.filename:
            log.error("Audio file must have filename")
            return None, None
        constructed_file_name = get_constructed_file_name(
            datetime.utcnow().replace(tzinfo=pytz.UTC),
            camera.tenant_id,
            audio_file.filename,
        )
        # insert "audio" and camera_id in the path where audio file is saved
        # in s3 bucket and consequently in HIP.
        vals = constructed_file_name.split("+")
        vals.insert(1, camera.client_camera_id)
        vals.insert(1, "audio")
        constructed_file_name = "+".join(vals)
        # TODO: assumes that all audio devices are of the same type
        # Reasonable assumption for now but could be different in the future
        transcode_acs = False
        transcode_ulaw = False
        target_format = ".mov"
        for audio in audio_devices:
            if (
                audio.device_type.lower() == "acs"
                or audio.device_type.lower() == "sip"
            ):
                transcode_acs = True
                target_format = ".mp3"
                break
            elif audio.device_type.lower() == "eagle_eye":
                transcode_ulaw = True
                target_format = ".wav"
        with tempfile.TemporaryDirectory() as tmpdir:
            local_audio_path = osp.join(
                tmpdir,
                constructed_file_name,
            )
            with opentracing.tracer.start_active_span("transcode-audio"):
                audio_file.save(local_audio_path)
                (basepath, _) = osp.splitext(local_audio_path)
                output_filepath = f"{basepath}{target_format}"
                random_dir_name = uuid.uuid4().hex
                random_dir_path = os.path.join("/tmp", random_dir_name)
                os.makedirs(random_dir_path, exist_ok=True)
                if transcode_acs:
                    ret = transcode_audio(
                        input_filepath=local_audio_path,
                        output_filepath=output_filepath,
                        sample_rate="44100",
                        bitrate="320k",
                        encoding_format="mp3",
                    )
                    random_file_name = uuid.uuid4().hex + ".mp3"
                    random_file_path = os.path.join(
                        random_dir_path, random_file_name
                    )
                    log.info(
                        "Copying audio file to random dir",
                        file_path=random_file_path,
                    )
                    shutil.copy(output_filepath, random_file_path)
                elif transcode_ulaw:
                    ret = transcode_audio(
                        input_filepath=local_audio_path,
                        output_filepath=output_filepath,
                        sample_rate="8000",
                    )
                    random_file_name = uuid.uuid4().hex + target_format
                    random_file_path = os.path.join(
                        random_dir_path, random_file_name
                    )
                    log.info(
                        "Copying audio file to random dir",
                        file_path=random_file_path,
                    )
                    shutil.copy(output_filepath, random_file_path)
                else:
                    ret = transcode_audio(
                        input_filepath=local_audio_path,
                        output_filepath=output_filepath,
                    )
                    random_file_name = uuid.uuid4().hex + ".mov"
                    random_file_path = os.path.join(
                        random_dir_path, random_file_name
                    )
                    log.info(
                        "Copying audio file to random dir",
                        file_path=random_file_path,
                    )
                    shutil.copy(output_filepath, random_file_path)
            if not ret:
                log.error(
                    "Failed to transcode audio file.",
                    tenant_id=camera.tenant_id,
                )
                return None, None

            (filebase, _) = osp.splitext(constructed_file_name)
            s3_file_name = construct_S3_file_name(f"{filebase}{target_format}")
        return s3_file_name, random_file_path
