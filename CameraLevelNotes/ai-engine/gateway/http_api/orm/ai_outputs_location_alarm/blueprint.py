from http import H<PERSON>PStatus

from flask import Blueprint, request

import controller as ctrl

from .endpoint import AIOutputsLocationAlarmGetter


def ai_outputs_location_alarms_blueprint(ctrl_map: ctrl.ControllerMap):
    api = Blueprint("ai_outputs_location_alarms_blueprint", __name__)
    endpoint = AIOutputsLocationAlarmGetter(ctrl_map)

    @api.route("/", methods=["GET"])
    def get_location_alarm_ai_outputs_by_id():
        """
        Get AI Outputss for location alarm by id
        ---
        description:  Get AI Outputs for location alarm by id
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token authorization for the API
          - name: location_alarm_id
            in: path
            required: true
            schema:
              type: integer
            required: true
            description: Location Alarm Id
        tags:
          - AI Output Location Alarms
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/AIOutputsLocationAlarm'
          400:
            description: Bad Request
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/BadRequest'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        alarm_id = request.args.get("alarm_id")
        camera_group_id = request.args.get("camera_group_id")
        alarm_group_id = request.args.get("alarm_group_id")
        if not alarm_id and not camera_group_id and not alarm_group_id:
            return {
                "status": HTTPStatus.BAD_REQUEST.value,
                "message": "Either alarm_id, camera_group_id or alarm_group_id shoule be passed in query",
            }, HTTPStatus.BAD_REQUEST.value
        return endpoint._get_ai_outputs_location_alarms(
            alarm_id, camera_group_id, alarm_group_id
        )

    return api
