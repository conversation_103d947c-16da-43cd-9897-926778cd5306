import datetime
import json
import os
import tempfile
import threading
import typing
from http import HTTPStatus
from urllib.parse import quote

import pytz
import requests
import structlog
from boto3.exceptions import Boto3Error

import controller as ctrl
from common_utils.s3_utils import (
    S3_BUCKET_NAME_KEY,
    S3_FILE_NAME_KEY,
    construct_S3_file_name,
    get_s3_path_details,
    get_signed_s3_url,
    upload_file_to_s3,
)
from common_utils.time_utils import parse_utc_timestamp_str
from config import backend_config as config
from controller import TalkdownsController
from controller.escalations.escalations_filters import EscalationFilters
from gateway.common.endpoint_guard import guard_endpoint
from gateway.http_api.orm.constants import (
    NEGATIVE_FILTER_TOKEN,
    QUERY_SEPARATOR,
)
from gateway.http_api.orm.utils import get_page_params, parse_relative_time
from interfaces.escalations import (
    EscalationStatus,
    EscalationUpdateType,
    TwilioLogType,
)
from models_rds.escalation_update import EscalationUpdates
from models_rds.escalations import Escalations
from models_rds.escalations_media import EscalationsMedia
from models_rds.talkdowns import TalkDowns
from models_rds.users import Users

log = structlog.get_logger("hakimo.orm", module="Escalations ORM")
s3_bucket = "vision-videos"
video_duration_seconds = 30


# For now only positive filters are supported
def _parse_filter_str(
    filter_str: typing.Optional[str],
) -> typing.Optional[typing.List[str]]:
    if not filter_str:
        return None
    filter_options = filter_str.split(QUERY_SEPARATOR)
    if NEGATIVE_FILTER_TOKEN in filter_options:
        filter_options.remove(NEGATIVE_FILTER_TOKEN)
    if not filter_options:
        return None
    return filter_options


class EscalationsOrmEndpoint:
    def __init__(self, controller: ctrl.ControllerMap):
        self._ctrl_map = controller

    def _unmarshal_create_escalation_request(
        self,
        payload: typing.Optional[typing.Dict],
    ) -> typing.Dict:
        assert payload is not None
        ret: typing.Dict[str, typing.Any] = {}
        ret["location_id"] = payload["location_id"]
        ret["camera_id"] = payload.get("camera_id")
        ret["video_duration_seconds"] = payload.get(
            "video_duration_seconds", 30
        )
        ret["playback_time"] = payload.get("playback_time", None)
        if ret["playback_time"] is None:
            current_time = datetime.datetime.now(datetime.timezone.utc)
            time_with_delta = current_time - datetime.timedelta(seconds=30)
            playback_time = time_with_delta.isoformat()
            ret["playback_time"] = playback_time
        return ret

    def _unmarshal_create_media_escalation_request(
        self,
        payload: typing.Optional[typing.Dict],
    ) -> typing.Dict:
        assert payload is not None
        ret: typing.Dict[str, typing.Any] = {}
        ret["playback_time"] = payload.get("playback_time", None)
        ret["camera_id"] = payload.get("camera_id")
        ret["video_duration_seconds"] = payload.get(
            "video_duration_seconds", 30
        )
        if ret["playback_time"] is None:
            current_time = datetime.datetime.now(datetime.timezone.utc)
            time_with_delta = current_time - datetime.timedelta(seconds=30)
            playback_time = time_with_delta.isoformat()
            ret["playback_time"] = playback_time
        ret["s3_file_path"] = payload.get("s3_file_path", None)
        return ret

    def _unmarshal_update_escalation_request(
        self,
        payload: typing.Dict[str, typing.Any],
    ) -> typing.Dict:
        assert payload is not None
        ret: typing.Dict[str, typing.Any] = {}
        ret["escalation_id"] = payload["escalation_id"]
        if status_str := payload.get("status"):
            status = EscalationStatus(status_str)
        else:
            status = None
        ret["status"] = status
        ret["resolution_comment"] = payload.get("resolution_comment")
        update_type = payload.get("update_type")
        ret["update_type"] = update_type

        return ret

    def _unmarshal_get_escalations_query_params(
        self, query_dict: typing.Optional[typing.Dict]
    ):
        offset = 0
        limit = 10
        if not query_dict:
            return EscalationFilters(), offset, limit

        filter_params = EscalationFilters(
            utc_time_interval=(
                parse_utc_timestamp_str(query_dict.get("dateFrom")),
                parse_utc_timestamp_str(query_dict.get("dateTo")),
            ),
            location_ids=_parse_filter_str(query_dict.get("locationIds")),
            status=_parse_filter_str(query_dict.get("statuses")),
            camera_ids=_parse_filter_str(query_dict.get("cameraIds")),
        )
        if query_dict.get("relativeTimeMinutes") or query_dict.get(
            "relativeTimeHours"
        ):
            rtime_mins, rtime_hours = (
                query_dict.get("relativeTimeMinutes"),
                query_dict.get("relativeTimeHours"),
            )
            filter_params.utc_time_interval = parse_relative_time(
                rtime_mins, rtime_hours
            )
        page_params: typing.Dict[str, int] = get_page_params(query_dict)

        return (
            filter_params,
            page_params.get("offset", offset),
            page_params.get("limit", limit),
        )

    def _marshal_single_escalation(
        self,
        escalation: Escalations,
        media: typing.Sequence[EscalationsMedia],
    ) -> typing.Dict[str, typing.Any]:
        ret = {
            "id": escalation.int_id,
            "cameraId": escalation.camera_id,
            "location": {
                "id": escalation.location.id,
                "name": escalation.location.name,
                "city": escalation.location.city,
                "state": escalation.location.state,
                "country": escalation.location.country,
                "timezone": escalation.location.timezone,
                "description": escalation.location.description,
                "tenant_id": escalation.location.tenant_id,
            },
            "status": escalation.current_status.value,
            "createdAt": escalation.created_at_utc.replace(
                tzinfo=pytz.utc
            ).isoformat(),
            "updatedAt": escalation.updated_at_utc.replace(
                tzinfo=pytz.utc
            ).isoformat(),
            "resolutionComment": escalation.resolution_comment,
            "createdByUserId": escalation.created_by_user_id,
            "createdByUserName": escalation.created_by_user.name or None,
            "resolvedByUserId": escalation.resolved_by_user_id,
            "resolvedByUserName": (
                escalation.resolved_by_user.name
                if escalation.resolved_by_user
                else None
            ),
            "media": media,
        }
        return ret

    def _marshal_get_escalations_query_response(
        self,
        escalations: typing.Sequence[Escalations],
        medias: typing.Sequence[EscalationsMedia] = [],
    ) -> typing.Dict[str, typing.Any]:
        media_map: typing.Dict = {}
        for item in medias:
            if item.escalation_id not in media_map:
                media_map[item.escalation_id] = []

            media_map[item.escalation_id].append(
                {
                    "cameraId": item.camera_id,
                    "mediaUrl": get_signed_media_url(item.media_url),
                    "createdAtUTC": item.created_at_utc,
                }
            )
        return {
            "items": [
                self._marshal_single_escalation(i, media_map.get(i.int_id, []))
                for i in escalations
            ],
            "total": 1000,
        }

    @guard_endpoint(["escalation:view"])
    def get_escalations(
        self,
        user: typing.Optional[Users] = None,
        query_params: typing.Optional[typing.Dict] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None
        try:
            (
                filters,
                offset,
                limit,
            ) = self._unmarshal_get_escalations_query_params(query_params)
        except ValueError as exc:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = str(exc)
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        vision_tenants = user.vision_tenants
        results = self._ctrl_map.escalations.get_escalations(
            filters=filters,
            offset=offset,
            limit=limit,
            vision_tenant_ids=vision_tenants,
        )
        escalation_ids = [esc.int_id for esc in results]
        ecalations_media = (
            self._ctrl_map.escalations_media.get_escalations_media(
                escalation_ids=escalation_ids
            )
        )

        api_resp["payload"] = self._marshal_get_escalations_query_response(
            results, ecalations_media
        )
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed successfully"
        return api_resp

    def _create_escalation_media(
        self,
        escalation_id: int,
        camera_id: str,
        ret: typing.Dict[str, typing.Any],
    ):
        # cameraId from request payload, get camera details like tenantId
        cam_details = self._ctrl_map.camera.get_camera_by_id(
            cam_id=camera_id,
        )
        if cam_details is None:
            log.error("Camera Id not found", camera_id=camera_id)
            raise ValueError("Camera Id not found")

        # fetch video from playback url and save it in s3
        playback_time = ret["playback_time"]
        video_duration_seconds = ret["video_duration_seconds"]
        cam_playback_url = cam_details.playback_url

        playback_params = (
            f"&start={playback_time}&duration={video_duration_seconds}"
        )

        playback_url_with_params = f"{cam_playback_url}{playback_params}"

        try:
            thread = threading.Thread(
                target=download_and_save_media_to_s3,
                args=(
                    playback_url_with_params,
                    camera_id,
                    cam_details.tenant_id,
                    escalation_id,
                    self._ctrl_map,
                ),
            )
            thread.start()

        except ValueError as exc:
            log.error("Erorr in downloading app", error=str(exc))

    @guard_endpoint(["escalation:create"])
    def create_escalation(
        self,
        payload: typing.Optional[typing.Dict] = None,
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            ret = self._unmarshal_create_escalation_request(payload=payload)
        except (ValueError, KeyError, TypeError):
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Bad payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        escalation_id = self._ctrl_map.escalations.create_escalation(
            location_id=ret["location_id"],
            camera_id=ret["camera_id"],
            created_by_user_id=user.uuid,
        )
        try:
            self._create_escalation_media(
                escalation_id,
                ret["camera_id"],
                ret,
            )
        except (ValueError, KeyError, TypeError):
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Bad payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Escalation created successfully"
        api_resp["escalation_id"] = escalation_id
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["escalation:update"])
    def update_escalation(
        self,
        escalation_id: int,
        payload: typing.Dict[str, typing.Any],
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            ret = self._unmarshal_update_escalation_request(payload=payload)
        except (ValueError, KeyError, TypeError):
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Bad payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        escalation = self._ctrl_map.escalations.get_escalation_by_id(
            ret["escalation_id"]
        )
        if not escalation:
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Escalation not found"
            return (api_resp, HTTPStatus.NOT_FOUND.value)

        if ret.get("status", None) == EscalationStatus.RESOLVED:
            resolved_by_user_id = user.uuid
            if not ret["resolution_comment"]:
                api_resp["status"] = HTTPStatus.BAD_REQUEST.value
                api_resp["message"] = "Resolution comment is required"
                return (api_resp, HTTPStatus.BAD_REQUEST.value)
        if (
            ret.get("update_type", None)
            == EscalationUpdateType.CALL_SMS_LOGS.value
        ):
            twilio_log_type = payload.get("twilio_log_type")
            if twilio_log_type not in {
                TwilioLogType.TWILIO_CALLS.value,
                TwilioLogType.Twilio_SMS.value,
            }:
                api_resp["message"] = "Invalid Twilio log type"
                api_resp["status"] = HTTPStatus.BAD_REQUEST.value
                return api_resp, HTTPStatus.BAD_REQUEST.value
            escalation_update = EscalationUpdates(
                escalation_id=escalation_id,
                update_type=EscalationUpdateType.CALL_SMS_LOGS.value,
                user_id=user.uuid,
                update_details={
                    "type": twilio_log_type,
                    "ssid": payload.get("twilio_ssid"),
                    "calling_to_name": payload.get("twilio_calling_to_name"),
                },
            )
            self._ctrl_map.escalations.create_escalation_updates(
                escalation_update
            )
        else:
            self._ctrl_map.escalations.update_escalation(
                escalation_id=escalation.int_id,
                current_status=ret["status"],
                resolution_comment=ret["resolution_comment"],
                resolved_by_user_id=resolved_by_user_id,
            )
            escalation_update = EscalationUpdates(
                escalation_id=escalation_id,
                update_type=EscalationUpdateType.CHANGE_STATUS,
                update_status=ret.get("status", None),
                user_id=user.uuid,
                update_text=ret.get("resolution_comment", None),
            )
            self._ctrl_map.escalations.create_escalation_updates(
                escalation_update
            )
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Escalation updated successfully"
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["escalation:update"])
    def update_rspndr_escalation_update(
        self,
        escalation_id: int,
        payload: typing.Dict[str, typing.Any],
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}

        escalation = self._ctrl_map.escalations.get_escalation_by_id(
            escalation_id
        )
        if not escalation:
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Escalation not found"
            return (api_resp, HTTPStatus.NOT_FOUND.value)

        location = self._ctrl_map.locations.get_location_by_id(
            escalation.location_id
        )
        if not location:
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Location not found"
            return (api_resp, HTTPStatus.NOT_FOUND.value)

        rspndr_details = payload.get("rspndr_details", {})
        notes = rspndr_details.get("notes", "no notes")

        update_text = (
            f"RSPNDR escalation {{ id: '{escalation_id}' }} with note: {notes}"
        )

        escalation_update = EscalationUpdates(
            escalation_id=escalation_id,
            update_type=EscalationUpdateType.RSPNDR,
            update_text=update_text,
            user_id=user.uuid,
            update_details={
                "location_name": location.name,
                "location_address": location.description,
                "rspndr_details": payload.get("rspndr_details", {}),
            },
            tenant_id=location.tenant_id,
        )
        self._ctrl_map.escalations.create_escalation_updates(escalation_update)

        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "RSPNDR escalation update created successfully"
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["escalation/updates:view"])
    def get_escalation_updates(
        self,
        escalation_id: int,
        user: typing.Optional[Users] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None
        escalation = self._ctrl_map.escalations.get_escalation_by_id(
            escalation_id=escalation_id
        )
        if not escalation:
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Escalation not found"
            return (api_resp, HTTPStatus.NOT_FOUND.value)
        updates = self._ctrl_map.escalations.get_escalation_updates(
            [escalation_id]
        )
        talkdown_ctrl = self._ctrl_map.talkdowns
        talkdowns = _marshal_talkdown_location_alarm(escalation, talkdown_ctrl)
        api_resp["payload"] = {
            "items": [_marshal_single_escalation_update(i) for i in updates],
            "talkdowns": talkdowns.get("talkdowns", []),
            "total": len(updates),
        }
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed successfully"
        return (api_resp, HTTPStatus.OK.value)


def _marshal_talkdown_location_alarm(
    escalation, talkdown_ctrl
) -> typing.Dict[str, typing.Any]:
    talkdowns = _fetch_talkdowns(talkdown_ctrl, escalation)
    ret = {
        "talkdowns": [
            _marshal_single_talkdown(talkdown) for talkdown in talkdowns
        ],
    }
    return ret


def _marshal_single_escalation_update(
    update: EscalationUpdates,
) -> typing.Dict[str, typing.Any]:
    ret = {
        "id": update.int_id,
        "update_type": update.update_type.value,
        "update_status": (
            update.update_status.value if update.update_status else None
        ),
        "update_text": update.update_text,
        "update_time": update.update_timestamp_utc.replace(
            tzinfo=pytz.utc
        ).isoformat(),
        "user": (
            {"id": update.user.uuid, "name": update.user.name}
            if update.user
            else None
        ),
    }
    if (
        update.update_type == EscalationUpdateType.MANUAL_TALKDOWN
        and update.update_details
    ):
        details = update.update_details
        audio_file_bucket = details.get("audio_file_bucket")
        audio_file_path = details.get("audio_file_path")
        if audio_file_bucket and audio_file_path:
            ret["audio_url"] = get_signed_s3_url(
                audio_file_bucket, audio_file_path
            )
    elif update.update_type == EscalationUpdateType.CALL_SMS_LOGS:
        details = update.update_details
        ret["twilio_ssid"] = details.get("ssid")
        ret["twilio_log_type"] = details.get("type")
        ret["twilio_calling_to_name"] = details.get("calling_to_name")

    return ret


def _fetch_talkdowns(
    talkdown_ctrl: TalkdownsController, escalation: Escalations
) -> typing.List[TalkDowns]:
    escalation_talkdowns = talkdown_ctrl.get_escalation_talkdowns(
        escalation_id=escalation.int_id
    )
    talkdowns = {}
    for talkdown in escalation_talkdowns:
        talkdowns[talkdown.talkdown_id] = talkdown
    talkdown_list = list(talkdowns.values())
    return talkdown_list


def _marshal_single_talkdown(talkdown):
    def parse_talkdown_details(details):
        if not details:
            return None
        if details.startswith("{") and details.endswith("}"):
            try:
                return json.loads(details)
            except json.JSONDecodeError:
                log.error(
                    "Failed to parse talkdown details as JSON", details=details
                )
                return None
        else:
            return {"audio_file_path": details, "audio_file_bucket": None}

    talkdown_details = parse_talkdown_details(talkdown.actual_talkdown_path)
    talkdown_path = ""
    if talkdown_details:
        audio_file_bucket = talkdown_details.get("audio_file_bucket")
        audio_file_path = talkdown_details.get("audio_file_path")
        if audio_file_bucket and audio_file_path:
            talkdown_path = get_signed_s3_url(
                audio_file_bucket, audio_file_path
            )
        elif audio_file_path:
            talkdown_path = audio_file_path

    ingested_talkdown_details = parse_talkdown_details(
        talkdown.recorded_talkdown_path
    )
    ingested_talkdown_s3_url = ""
    if ingested_talkdown_details:
        audio_file_bucket = ingested_talkdown_details.get("audio_file_bucket")
        audio_file_path = ingested_talkdown_details.get("audio_file_path")
        if audio_file_bucket and audio_file_path:
            ingested_talkdown_s3_url = get_signed_s3_url(
                audio_file_bucket, audio_file_path
            )
        elif audio_file_path:
            ingested_talkdown_s3_url = audio_file_path
    return {
        "id": talkdown.talkdown_id,
        "talkdown_type": talkdown.talkdown_type,
        "talkdown_state": talkdown.talkdown_state,
        "status": talkdown.status,
        "talkdown_audio": talkdown_path,
        "ingested_talkdown_audio": ingested_talkdown_s3_url,
        "custom_message": talkdown.custom_message,
        "talkdown_completed_utc": talkdown.request_completed_utc,
        "raw_alarm_id": talkdown.raw_alarm_id,
        "location_alarm_id": talkdown.location_alarm_id,
        "escalation_id": talkdown.escalation_id,
        "ingestion_status": talkdown.ingestion_status,
    }


def download_and_save_media_to_s3(
    playback_url: str,
    camera_id: str,
    tenant_id: str,
    escalation_id,
    ctrl_map,
):
    with tempfile.TemporaryDirectory() as tmpdir:
        # fetch media file from playback server
        try:
            current_datetime_str = datetime.datetime.now().strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            constructed_file_name = f"{tenant_id}+{camera_id}+video_segment_{current_datetime_str}.mp4"
            local_media_path = os.path.join(tmpdir, constructed_file_name)
            ecoded_url = quote(playback_url, safe=":/?=&")
            response = requests.get(ecoded_url)
            response.raise_for_status()
            with open(local_media_path, "wb") as f:
                f.write(response.content)
        except requests.RequestException as re:
            log.error(
                "Failed to download escalation media.",
                tenantId=tenant_id,
                playback_url=ecoded_url,
                camera_id=camera_id,
                exc_info=re,
            )
            raise ValueError("Failed to download escalation media")

        # upload to s3
        s3_file_name = construct_S3_file_name(constructed_file_name)
        try:
            upload_file_to_s3(s3_bucket, s3_file_name, local_media_path)
            # save s3 url and escalations Id in escalations_media table
            s3_file_object_url = f"https://{s3_bucket}.s3.{config.HAIE.AWS_S3_REGION}.amazonaws.com/{s3_file_name}"
            ctrl_map.escalations_media.add_escalations_media(
                camera_id=camera_id,
                escalation_id=escalation_id,
                media_url=s3_file_object_url,
            )
        except Boto3Error as be:
            log.error(
                "Failed to upload escalation media file to S3 bucket.",
                camera_id=camera_id,
                tenantId=tenant_id,
                exc_info=be,
            )
            raise ValueError(
                "Failed to upload escalation media file to S3 bucket."
            )


def get_signed_media_url(media_url: str):
    s3_details = get_s3_path_details(media_url)
    assert s3_details is not None, "Invalid media URL"
    signed_url = get_signed_s3_url(
        s3_details[S3_BUCKET_NAME_KEY],
        s3_details[S3_FILE_NAME_KEY],
    )
    return signed_url
