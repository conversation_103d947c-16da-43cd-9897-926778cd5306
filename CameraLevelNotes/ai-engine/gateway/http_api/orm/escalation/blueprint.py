import json

from flask import Blueprint, request

import controller as ctrl

from .endpoint import EscalationsOrmEndpoint


def escalations_blueprint(ctrl_map: ctrl.ControllerMap):
    api = Blueprint("escalations_blueprint", __name__)
    endpoint = EscalationsOrmEndpoint(ctrl_map)

    @api.route("", methods=["GET"])
    def _get_escalations_wrapper():
        """
        description: Get all escalations
        tags:
          - Escalation
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token authorization for the API
          - name: dateFrom
            in: query
            schema:
              type: string
            required: false
            description: Start time of the Escalation
          - name: dateTo
            in: query
            schema:
              type: string
            required: false
            description: End time of the Escalation
          - name: relativeTimeMinutes
            in: query
            schema:
              type: integer
            required: false
            description: Time period specified as a relative period in minutes
              Will add onto the relativeTimeHours parameter
              Overrides the dateFrom and dateTo query parameters if provided
          - name: relativeTimeHours
            in: query
            schema:
              type: integer
            required: false
            description: Time period specified as a relative period in hours.
              Will add onto the relativeTimeMinutes parameter
              Overrides the dateFrom and dateTo query parameters if provided
          - name: locationIds
            in: query
            schema:
              type: string
            required: false
            description: ^^^ separated list of location ids to filter
          - name: cameraIds
            in: query
            schema:
              type: string
            required: false
            description: ^^^ separated list of camera ids to filter
          - name: statuses
            in: query
            schema:
              type: string
            required: false
            description: Comma separated list of statuses
          - name: page
            in: query
            schema:
              type: integer
            required: false
            description: Page Number for Pagination of response
          - name: pageSize
            in: query
            schema:
              type: integer
            required: false
            description: Page Size for Pagination of response
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/CreateEscalationsResponse'
          400:
            description: Bad Request
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/BadRequest'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        query_params = request.args.to_dict()
        response = endpoint.get_escalations(query_params=query_params)
        return response

    @api.route("", methods=["POST"])
    def _create_escalation_wrapper():
        """
        description: Creates a new escalation
        tags:
          - Escalation
        requestBody:
          description: Details of the Escalation to be created
          required: true
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateEscalationRequest'
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token authorization for the API
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/CreateEscalationResponse'
          400:
            description: Bad Request
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/BadRequest'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        req_data = json.loads(request.data)
        response = endpoint.create_escalation(payload=req_data)
        return response

    @api.route("/<int:escalation_id>", methods=["PUT"])
    def _update_escalation_wrapper(escalation_id: int):
        """
        description: Update escalation
        tags:
          - Escalation
        requestBody:
          description: Escalation Update Request
          required: true
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateEscalationRequest'
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token authorization for the API
          - name: escalation_id
            in: path
            required: true
            schema:
              type: integer
            description: Escalation Id
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/OKResponse'
          400:
            description: Bad Request
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/BadRequest'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        update_payload = json.loads(request.data)
        return endpoint.update_escalation(escalation_id, update_payload)

    @api.route("/<int:escalation_id>/events", methods=["GET"])
    def _get_escalations_update_wrapper(escalation_id: int):
        return endpoint.get_escalation_updates(escalation_id=escalation_id)

    @api.route("/<int:escalation_id>/rspndr-update", methods=["POST"])
    def _create_rspndr_escalation_update_wrapper(escalation_id: int):
        """
        description: Create RSPNDR escalation update
        tags:
          - Escalation
        requestBody:
          description: RSPNDR escalation update details
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  rspndr_details:
                    type: object
                    description: RSPNDR escalation details
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token authorization for the API
          - name: escalation_id
            in: path
            required: true
            schema:
              type: integer
            description: Escalation Id
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/OKResponse'
          400:
            description: Bad Request
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/BadRequest'
          404:
            description: Escalation not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/NotFound'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        req_data = json.loads(request.data)
        return endpoint.update_rspndr_escalation_update(
            escalation_id, req_data
        )

    return api
