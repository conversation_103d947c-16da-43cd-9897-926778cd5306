import json
import typing
from http import H<PERSON><PERSON>tatus

import structlog

import controller as ctrl
import gateway.http_api.orm.audit_endpoint as ae
from controller.audit_logs.audit_log_operation import audit_door_group
from gateway.common.endpoint_guard import guard_endpoint
from gateway.http_api.orm.constants import QUERY_SEPARATOR
from gateway.http_api.orm.user.utils import get_user_tenant_ids
from gateway.http_api.orm.utils import get_page_params
from interfaces.camera_info import CameraInfo
from interfaces.doors_camera_params import LabelStatus
from models_rds.cameras import Cameras
from models_rds.door_camera_params import DoorCameraParams
from models_rds.door_groups import DoorGroups
from models_rds.doors import Doors
from models_rds.locations import Locations
from models_rds.users import Users

log = structlog.get_logger("hakimo.orm.door", module="Door Endpoint")


class DoorEndpoint:
    def __init__(
        self,
        ctrl_map: ctrl.ControllerMap,
    ):
        self._ctrl_map = ctrl_map

    @guard_endpoint(["door:view"])
    def get_door_names(
        self,
        user: typing.Optional[Users] = None,
        query_params: typing.Optional[typing.Dict[str, str]] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            query = unmarshal_door_name_query(query_params)
        except KeyError:
            api_resp["message"] = "Invalid query params"
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            return api_resp, HTTPStatus.BAD_REQUEST.value

        tenant_ids = user.msp_tenants
        if not tenant_ids:
            tenant_ids = [user.tenant_id]
        doors = self._ctrl_map.door.get_doors_by_door_name(
            query["door_substr"],
            limit=10,
            tenant_ids=tenant_ids,
        )

        api_resp["payload"] = marshal_door_autocomplete_response(doors)
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed Successfully"
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["door:view"])
    def get_door_locations(
        self,
        user: typing.Optional[Users] = None,
        query_params: typing.Optional[typing.Dict[str, str]] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            query = unmarshal_door_location_query(query_params)
        except KeyError:
            api_resp["message"] = "Invalid query params"
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            return api_resp, HTTPStatus.BAD_REQUEST.value

        tenant_ids = user.msp_tenants
        if not tenant_ids:
            tenant_ids = [user.tenant_id]
        doors_location_resp = self._ctrl_map.door.get_doors_by_location_name(
            query["location_substr"],
            limit=10,
            tenant_ids=tenant_ids,
        )

        api_resp["payload"] = marshal_location_autocomplete_response(
            doors_location_resp
        )
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed Successfully"
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["door:view"])
    def get_door_by_groups(
        self,
        user: typing.Optional[Users] = None,
        query_params: typing.Optional[typing.Dict[str, str]] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            query = unmarshal_door_group_query(query_params)
        except KeyError:
            api_resp["message"] = "Invalid query params"
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            return api_resp, HTTPStatus.BAD_REQUEST.value

        tenant_ids = user.msp_tenants
        if not tenant_ids:
            tenant_ids = [user.tenant_id]
        door_groups_resp = self._ctrl_map.door.get_doors_by_group_name(
            query["doorgroup_substr"],
            limit=10,
            tenant_ids=tenant_ids,
        )

        api_resp["payload"] = marshal_doorgroup_autocomplete_response(
            door_groups_resp
        )
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed Successfully"
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["door/group:update"])
    def update_door_group_for_door(
        self,
        door_id: str,
        door_group_req: typing.Dict[str, str],
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        tenant_id = user.tenant_id
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            door_group_id = int(door_group_req["door_group"])
        except (KeyError, ValueError):
            api_resp["message"] = "Invalid Request Payload"
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            return api_resp, HTTPStatus.BAD_REQUEST.value

        try:
            door_group = self._ctrl_map.door_groups.get_door_group_by_id(
                door_group_id, tenant_id
            )
            if door_group is None:
                category = ae.DOOR_GROUP_CREATION_ACTION
            else:
                category = ae.DOOR_GROUP_MODIFICATION_ACTION
            self._ctrl_map.door.add_update_doors_to_door_group(
                door_group_id, [door_id], tenant_id=tenant_id
            )
            door_group_updated = (
                self._ctrl_map.door_groups.get_door_group_by_id(
                    door_group_id, tenant_id
                )
            )
            assert door_group_updated is not None
            audit_door_group(
                user.email,
                tenant_id,
                ae.DOOR_GROUP_AUDIT,
                category,
                self._ctrl_map,
                door_group_id,
                door_group_updated.name,
                door_ids=[door_id],
                door_added=True,
            )
        except Exception:  # pylint: disable=broad-except
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Unknown Error while adding door to group"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed Successfully"
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["door/group:update"])
    def remove_door_group_for_door(
        self,
        door_id: str,
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        tenant_id = user.tenant_id
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            door = self._ctrl_map.door.get_door(door_id)
            door_group = self._ctrl_map.door_groups.get_door_group_by_id(
                door.group_id
            )
            assert door_group is not None
            self._ctrl_map.door.remove_doors_from_door_group(
                [door_id], tenant_id=tenant_id
            )
            audit_door_group(
                user.email,
                tenant_id,
                ae.DOOR_GROUP_AUDIT,
                ae.DOOR_GROUP_DELETION_ACTION,
                self._ctrl_map,
                door.group_id,
                door_group.name,
                door_ids=[door_id],
                door_added=False,
            )
        except Exception:  # pylint: disable=broad-except
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Unknown Error while adding door to group"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed Successfully"
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["door/location:update"])
    def update_door_location_for_door(
        self,
        door_id: str,
        location_req: typing.Dict[str, int],
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        tenant_id = user.tenant_id
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            location_data = location_req["location"]
        except KeyError:
            api_resp["message"] = "Invalid Request Payload"
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            return api_resp, HTTPStatus.BAD_REQUEST.value

        try:
            self._ctrl_map.door.add_update_doors_location(
                location_data, [door_id], tenant_id=tenant_id
            )
        except Exception:  # pylint: disable=broad-except
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Unknown Error while adding door to location"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed Successfully"
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["door:view"])
    def get_all_doors(
        self,
        user: typing.Optional[Users] = None,
        query_params: typing.Optional[typing.Dict[str, str]] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            query = unmarshal_doors_query(query_params)
        except KeyError:
            api_resp["message"] = "Invalid query params"
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            return api_resp, HTTPStatus.BAD_REQUEST.value

        tenant_ids = get_user_tenant_ids(user)
        ret = self._ctrl_map.door.get_all_doors(tenant_ids, **query)
        query.pop("limit", None)
        query.pop("offset", None)
        total = self._ctrl_map.door.get_count(tenant_ids, **query)
        api_resp["payload"] = marshal_all_doors_response(ret, total)
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed Successfully"
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["camera:view"])
    def get_door_camera(
        self,
        door_id: str,
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        tenant_ids = get_user_tenant_ids(user)
        dcp = self._ctrl_map.door_camera_params.get_door_camera_mapping(
            door_id=door_id
        )

        if dcp is None or dcp.tenant_id not in tenant_ids:
            api_resp["status"] = HTTPStatus.NOT_FOUND.value
            api_resp["message"] = "Door camera mapping not found"
            return api_resp, HTTPStatus.NOT_FOUND.value

        camera_info = dcp.camera_info
        api_resp["payload"] = marshal_camera_info_response(camera_info)
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed Successfully"
        return api_resp, HTTPStatus.OK.value


def unmarshal_doors_query(
    query_dict: typing.Optional[typing.Dict[str, str]],
) -> typing.Dict:
    ret: typing.Dict[str, typing.Any] = {}
    if query_dict is None:
        return ret
    if query_dict.get("labelStatus") is None:
        label_status = None
    else:
        label_query = query_dict["labelStatus"].split(QUERY_SEPARATOR)
        if "completed" in label_query and "pending" in label_query:
            label_status = None
        elif "completed" in label_query:
            label_status = getattr(LabelStatus, "COMPLETE")
        elif "pending" in label_query:
            label_status = getattr(LabelStatus, "INCOMPLETE")
        else:
            raise ValueError("Unrecognized query param for label status")
    ret["labelling_status"] = label_status
    if query_dict.get("locations") is None:
        locations = None
    else:
        locations = query_dict["locations"].split(QUERY_SEPARATOR)
    ret["locations"] = locations
    if query_dict.get("locationIds") is None:
        location_ids = None
    else:
        location_ids = query_dict["locationIds"].split(QUERY_SEPARATOR)
    ret["location_ids"] = location_ids
    if query_dict.get("doors") is None:
        doors = None
    else:
        doors = query_dict["doors"].split(QUERY_SEPARATOR)
    ret["doors"] = doors
    ret.update(get_page_params(query_dict))
    if query_dict.get("imageAvailableOnly"):
        ret["image_available_only"] = query_dict.get(
            "imageAvailableOnly", False
        )
    return ret


def marshal_all_doors_response(
    results: typing.Sequence[
        typing.Tuple[
            Doors,
            typing.Optional[DoorCameraParams],
            typing.Optional[Cameras],
            typing.Optional[Locations],
        ]
    ],
    total: int,
) -> typing.Dict[str, typing.Any]:
    ret: typing.Dict[str, typing.Any] = {"total": total}
    all_doors: typing.List[typing.Dict[str, typing.Any]] = []

    for door, dcp, cam, loc in results:
        tmp_dict = {
            "id": door.uuid,
            "name": door.door_name,
            "sourceSystem": door.source_system,
        }
        if loc:
            tmp_dict["location"] = {
                "city": loc.city,
                "country": loc.country,
                "state": loc.state,
                "name": loc.name,
                "timezone": loc.timezone,
                "id": loc.id,
            }
        if door.door_groups:
            tmp_dict["doorGroup"] = {
                "id": door.door_groups.id,
                "name": door.door_groups.name,
                "description": door.door_groups.description,
            }
        if dcp is not None:
            assert cam is not None, "Camera must exist if DCP entry exists"
            tmp_dict["dcp"] = {
                "id": dcp.uuid,
                "cameraName": cam.name,
                "label": {
                    "doorMarkers": json.loads(
                        dcp.door_coordinates_in_cam_frame
                    )
                    if dcp.door_coordinates_in_cam_frame is not None
                    else None,
                    "floorMarker": dcp.door_orientation_point,
                    "cameraPosition": dcp.camera_position.name.lower(),
                },
            }
        all_doors.append(tmp_dict)
    ret["items"] = all_doors
    return ret


def unmarshal_door_name_query(
    query_dict: typing.Optional[typing.Dict[str, str]],
) -> typing.Dict:
    ret: typing.Dict[str, typing.Any] = {}
    if query_dict is None:
        return ret
    # name must be there for door name endpoint query
    # otherwise key error will be raised
    ret["door_substr"] = query_dict["name"]
    return ret


def unmarshal_door_location_query(
    query_dict: typing.Optional[typing.Dict[str, str]],
) -> typing.Dict:
    ret: typing.Dict[str, typing.Any] = {}
    if query_dict is None:
        return ret
    # name must be there for location name endpoint query
    # otherwise key error will be raised
    ret["location_substr"] = query_dict["name"]
    return ret


def unmarshal_door_group_query(
    query_dict: typing.Optional[typing.Dict[str, str]],
) -> typing.Dict:
    ret: typing.Dict[str, typing.Any] = {}
    if query_dict is None:
        return ret
    # name must be there for group description endpoint query
    # otherwise key error will be raised
    ret["doorgroup_substr"] = query_dict["name"]
    return ret


def marshal_door_autocomplete_response(
    doors: typing.Sequence[Doors],
) -> typing.Sequence[typing.Dict]:
    ret = []
    for door in doors:
        ret.append({"id": door.uuid, "name": door.door_name})
    return ret


def marshal_location_autocomplete_response(
    doors_location_resp: typing.Sequence[
        typing.Tuple[Doors, typing.Optional[Locations]]
    ],
) -> typing.Sequence[typing.Dict]:
    ret = {}
    for door, location in doors_location_resp:
        if door.city_and_building not in ret:
            ret[door.city_and_building] = {
                "id": door.uuid,
                "name": door.city_and_building,
                "door_name": door.door_name,
            }
            if location is not None:
                ret[door.city_and_building].update(
                    {
                        "name": location.name,
                        "city": location.city,
                        "state": location.state,
                        "country": location.country,
                        "timezone": location.timezone,
                    }
                )
    return list(ret.values())


def marshal_doorgroup_autocomplete_response(
    doors_groups_resp: typing.Sequence[typing.Tuple[Doors, DoorGroups]],
) -> typing.Sequence[typing.Dict]:
    ret = {}
    for door, doorgroup in doors_groups_resp:
        if door.city_and_building not in ret:
            ret[door.city_and_building] = {
                "id": door.uuid,
                "door_name": door.door_name,
                "group_description": doorgroup.description,
            }
    return list(ret.values())


def marshal_camera_info_response(
    camera_info: CameraInfo,
) -> typing.Dict[str, typing.Any]:
    return {
        "cameraId": camera_info.camera_id,
        "cameraName": camera_info.camera_name,
        "clientCameraId": camera_info.client_camera_id,
        "serverUrl": camera_info.server_url,
        "cameraTimezone": camera_info.camera_timezone,
        "rawCameraInfo": camera_info.raw_camera_info,
        "rtspUrl": camera_info.rtsp_url,
        "integrationType": camera_info.integration_type,
        "camSiteId": camera_info.cam_site_id,
        "tenantId": camera_info.tenant_id,
        "livestreamUrl": camera_info.livestream_url,
    }
