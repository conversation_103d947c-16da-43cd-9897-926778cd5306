import json

import structlog
from flask import Blueprint, request

import controller as ctrl

from .endpoint import DoorEndpoint

log = structlog.get_logger("hakimo.orm.door", module="Door Blueprint v2")


def door_blueprint_v2(ctrl_map: ctrl.ControllerMap):
    api = Blueprint("door_blueprint_v2", __name__)
    endpoint = DoorEndpoint(
        ctrl_map,
    )

    @api.route("/names", methods=["GET"])
    def door_name_wrapper():  # pylint: disable=unused-variable
        query_params = request.args.to_dict()
        return endpoint.get_door_names(query_params=query_params)

    @api.route("", methods=["GET"])
    def get_all_doors():  # pylint: disable=unused-variable
        """
        Gets all the doors available for the user in the database
        ---
        description: Returns all doors available for the user in the database, supplemented with\
        door_camera_mapping info, camera info and location info.
        tags:
          - Doors
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token authorization for the API
          - name: page
            in: query
            schema:
              type: string
            required: false
            description: Page Number for Pagination of response
          - name: pageSize
            in: query
            schema:
              type: string
            required: false
            description: Page Size for Pagination of response
          - name: labelStatus
            in: query
            schema:
              type: string
            required: false
            description: Labelling Status of the door (completed or pending or both)
          - name: locations
            in: query
            schema:
              type: string
            required: false
            description: Locations filter for the doors
          - name: locationIds
            in: query
            schema:
              type: string
            required: false
            description: Location Id filter for the doors
          - name: doors
            in: query
            schema:
              type: string
            required: false
            description: Door Name filter for the user's doors list
          - name: imageAvailableOnly
            in: query
            schema:
              type: boolean
            required: false
            description: Return only doors where door camera mapping image is available
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/GetDoorsResponse'
          400:
            description: Bad Request
            content:
              application/json:
                schema:
                    $ref: '#/components/schemas/BadRequest'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        query_params = request.args.to_dict()
        return endpoint.get_all_doors(query_params=query_params)

    @api.route("/locations", methods=["GET"])
    def location_name_wrapper():  # pylint: disable=unused-variable
        query_params = request.args.to_dict()
        return endpoint.get_door_locations(query_params=query_params)

    @api.route("/groups", methods=["GET"])
    def door_groups_name_wrapper():  # pylint: disable=unused-variable
        query_params = request.args.to_dict()
        return endpoint.get_door_by_groups(query_params=query_params)

    @api.route("/group/<door_id>", methods=["PATCH"])
    def add_update_door_groups_wrapper(
        door_id,
    ):  # pylint: disable=unused-variable
        door_group_req = json.loads(request.data)
        return endpoint.update_door_group_for_door(door_id, door_group_req)

    @api.route("/group/<door_id>", methods=["DELETE"])
    def remove_doors_from_group_wrapper(
        door_id,
    ):  # pylint: disable=unused-variable
        return endpoint.remove_door_group_for_door(door_id)

    @api.route("/location/<door_id>", methods=["PATCH"])
    def add_update_door_location_wrapper(
        door_id,
    ):  # pylint: disable=unused-variable
        location_req = json.loads(request.data)
        return endpoint.update_door_location_for_door(door_id, location_req)

    @api.route("/<door_id>/camera", methods=["GET"])
    def get_door_camera_wrapper(door_id):
        return endpoint.get_door_camera(door_id)

    return api
