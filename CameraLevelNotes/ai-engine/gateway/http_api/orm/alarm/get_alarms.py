import datetime
import json
import typing
from collections import defaultdict
from http import HTTPStatus

import pytz
import structlog

import controller as ctrl
from common_utils.cloud_config_utils import cloud_config_manager
from common_utils.s3_utils import (
    S3_BUCKET_NAME_KEY,
    S3_FILE_NAME_KEY,
    get_s3_path_details,
    get_signed_s3_url,
)
from controller import ControllerMap, TalkdownsController
from controller.alarm.alarm_fetcher import FETCHED_ALARMS
from controller.alarm.alarm_filters import AlarmFilters
from gateway.auth.common import GuardEndpointOperatorEnum
from gateway.common.endpoint_guard import guard_endpoint
from gateway.http_api.orm.constants import MAX_RELATIVE_HOURS_FOR_MSP_LOCATIONS
from gateway.http_api.orm.user.utils import get_user_tenant_ids
from gateway.http_api.orm.utils import (
    get_page_params,
    parse_aged_alarm_times,
    parse_filter_str,
    parse_relative_time,
)
from interfaces.alarm import ALARM_UPDATES_SPEAKER_TALKDOWN_EVENT
from interfaces.alarm_mapper import AlarmMapper
from interfaces.entities.entity_types import (
    EntityType,
    PersonSubClassType,
    VehicleSubClassType,
)
from interfaces.entities.person import Person
from interfaces.entities.vehicle import Vehicle
from interfaces.source_systems import SourceEntityType
from interfaces.tenant_config import TenantConfig
from ml_module.detection import BaseDetection
from models_rds.alarm_media import AlarmMedia
from models_rds.alarm_updates import AlarmUpdates
from models_rds.cameras import Cameras
from models_rds.doors import Doors
from models_rds.employees import Employees
from models_rds.locations import Locations
from models_rds.ml_entities import MLEntities
from models_rds.raw_alarms import (
    RawAlarms,
    compute_partition_key,
    get_default_partition_key,
)
from models_rds.raw_alarms_metadata import RawAlarmsMetadata
from models_rds.talkdowns import TalkDowns
from models_rds.users import Users

from .utils import (
    get_alarm_local_time,
    get_alarm_media_details,
    get_box_around_entities,
)

log = structlog.get_logger("hakimo.orm.alarm", module="Alarm ORM")


class AlarmGetter:
    def __init__(self, controller: ctrl.ControllerMap):
        self._ctrl_map = controller
        self._alarm_mapper = AlarmMapper(self._ctrl_map)

    # Fetching raw alarms are used in location alarms as well.
    # But Operator should not have alarms:view permission as it will access to Alarms tab in UI
    # Hence using OR operator to handle this scenario
    @guard_endpoint(
        ["alarm:view", "location_alarm:view"],
        GuardEndpointOperatorEnum.OR,
    )
    def get_alarms(
        self,
        user: typing.Optional[Users] = None,
        query_params: typing.Optional[typing.Dict] = None,
        return_only_timeline: typing.Optional[bool] = False,
    ):
        """
        parameters:
            - name: user
              description: calling user
            - name: query_params
              description: query parameters passed in the URL of the request
            - name: return_only_timeline
              description: If True, returns only alarm_id, videoStartTime and videoEndTime else returns alarm details entirely
        """
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None
        try:
            filters, offset, limit, include_media = unmarshal_get_alarms_query(
                query_params
            )
        except ValueError as exc:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = str(exc)
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        # Get a list of all tenants (msp_tenants + user's own tenant)
        tenant_ids = [user.tenant_id]
        if user.msp_tenants:
            tenant_ids.extend(user.msp_tenants)

        # Verify that tenants sent in the filters are valid for the calling user
        # condition is to block any request which fetches alarms for all tenanats as it might break the DB
        if filters.tenant:
            for query_tenant in filters.tenant[0]:
                if query_tenant not in tenant_ids:
                    api_resp["status"] = HTTPStatus.FORBIDDEN.value
                    api_resp["message"] = "Invalid tenant IDs"
                    return (api_resp, HTTPStatus.FORBIDDEN.value)
        elif filters.location is None:
            api_resp["status"] = HTTPStatus.FORBIDDEN.value
            api_resp["message"] = "Tenant Id or location Id should be provided"
            return (api_resp, HTTPStatus.FORBIDDEN.value)
        msp_locations = user.msp_locations
        if msp_locations:
            try:
                filters.utc_time_interval = (
                    get_update_filter_time_range_for_msp_locations(filters)
                )
            except ValueError as exc:
                api_resp["status"] = HTTPStatus.FORBIDDEN.value
                api_resp["message"] = str(exc)
                return (api_resp, HTTPStatus.FORBIDDEN.value)

        # Check if the tenant in filter is enabled and supports fetching MSP tenant alarms
        if (
            cloud_config_manager
            and cloud_config_manager.is_enabled(
                "fetch_msp_tenants_alarms.is_enabled"
            )
            and filters.tenant is not None
            and any(
                tenant_id
                in cloud_config_manager._feature_flag.get(
                    "fetch_msp_tenants_alarms.active_tenants", []
                )
                for tenant_id in filters.tenant[0]
            )
        ):
            # Update the tenant filter with all MSP tenants
            filters.tenant = (
                tenant_ids,
                filters.tenant[1],
            )

        results = self._ctrl_map.alarm.fetch_alarms(
            tenant_ids, filters, offset, limit, msp_locations
        )

        if filters.tenant is not None and any(
            t not in tenant_ids for t in filters.tenant[0]
        ):
            api_resp["status"] = HTTPStatus.FORBIDDEN.value
            api_resp["message"] = "Invalid tenant filter values"
            return (api_resp, HTTPStatus.FORBIDDEN.value)
        # video_tags = self._ctrl_map.alarm.fetch_latest_video_tags(
        #     [i[0] for i in results]
        # )
        # log.debug("Got video tags")
        # for tag in video_tags:
        #     video_tag_data[tag[1]].append(tag[0])
        video_tag_data: typing.Dict[str, typing.List[str]] = defaultdict(list)
        media_info = None
        if include_media:
            media_info = [
                self._get_alarm_media(
                    result[0].uuid,
                    tenant_ids=tenant_ids,
                    partition_key=get_default_partition_key(
                        created_at_utc=result[0].created_at_utc
                    ),
                )
                for result in results
            ]

        api_resp["payload"] = marshal_get_alarms_query_result(
            results,
            video_tag_data,
            media_info,
            1000,
            return_only_timeline=return_only_timeline,
        )

        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed successfully"
        return api_resp

    @guard_endpoint(["alarm/details:view"])
    def get_alarm(
        self,
        alarm_id: str,
        user: typing.Optional[Users] = None,  # pylint: disable=unused-argument
        query_params: typing.Optional[dict] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None

        try:
            ret = self.get_single_alarm(
                alarm_id,
                user,
                (
                    query_params.get("partitionKey", None)
                    if query_params
                    else None
                ),
            )
        except ValueError as exc:
            message = str(exc)
            log.error(
                "Error in fetching alarm details",
                error_message=message,
                alarm_id=alarm_id,
            )
            ret_message = "Alarm does not exist"
            if message == "Alarm not found":
                status = HTTPStatus.NOT_FOUND.value
            elif message == "Alarm from different tenant requested":
                status = HTTPStatus.FORBIDDEN.value
            elif message == "Alarm from different location requested":
                status = HTTPStatus.FORBIDDEN.value
            else:
                status = HTTPStatus.BAD_REQUEST.value
            api_resp["status"] = status
            api_resp["message"] = ret_message
            return (api_resp, status)
        api_resp["payload"] = ret
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed successfully"
        return api_resp

    @guard_endpoint(["alarm/details:view"])
    def get_alarms_media(
        self,
        request_payload: typing.Dict,
        user: typing.Optional[Users] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None

        try:
            alarm_ids, include_boxes, timestamp = (
                unmarshal_get_alarms_media_query(request_payload)
            )
        except ValueError as exc:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = str(exc)
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        user_tenant_ids = get_user_tenant_ids(user)
        alarms = self._ctrl_map.alarm.by_ids(alarm_ids, timestamp=timestamp)

        eligile_alarms: typing.List[RawAlarms] = []
        for alrm in alarms:
            if alrm.tenant_id in user_tenant_ids:
                eligile_alarms.append(alrm)

        if not eligile_alarms:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Alarms not found"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        eligible_alarm_ids = [alrm.uuid for alrm in eligile_alarms]

        alarms_media = self._ctrl_map.alarm_media.get_alarms_media(
            eligible_alarm_ids
        )

        alarm_to_media: typing.Dict[str, typing.List[AlarmMedia]] = {}
        for media in alarms_media:
            if media.alarm_id not in alarm_to_media:
                alarm_to_media[media.alarm_id] = []
            alarm_to_media[media.alarm_id].append(media)

        ret = []

        for alarm in eligile_alarms:
            alarm_data = {}
            alarm_data["alarmId"] = alarm.uuid
            alarm_data["startTime"] = alarm.video_start_timestamp_utc
            alarm_data["endTime"] = alarm.video_end_timestamp_utc
            # take first media url
            media_url = alarm_to_media[alarm.uuid][0].media_url
            s3_details = get_s3_path_details(media_url)
            assert s3_details is not None, "Invalid media URL"
            signed_url = get_signed_s3_url(
                s3_details[S3_BUCKET_NAME_KEY],
                s3_details[S3_FILE_NAME_KEY],
                1200,
            )
            alarm_data["url"] = signed_url
            ret.append(alarm_data)

        if include_boxes:
            alarm_tenant_id = eligile_alarms[0].tenant_id
            t_config: TenantConfig = self._ctrl_map.tenant.get_config(
                alarm_tenant_id
            )
            rescale_boxes = t_config.rescaleBoxes or False
            alarms_media_boxes = get_boxes_for_alarms_media(
                self._ctrl_map, eligile_alarms, rescale_boxes
            )
            for alarm_media_data in ret:
                alarm_media_data["boxes"] = alarms_media_boxes.get(
                    alarm_media_data["alarmId"], []
                )

        api_resp["payload"] = ret
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed successfully"
        return api_resp

    @guard_endpoint(["alarm/details:view"])
    def get_alarm_media(
        self,
        alarm_id: str,
        query_params: typing.Optional[dict] = None,
        user: typing.Optional[Users] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None
        tenant_ids = user.msp_tenants
        if not tenant_ids:
            tenant_ids = [user.tenant_id]
        boxes = (
            query_params.get("includeBoxes", False) if query_params else False
        )
        partition_key = (
            query_params.get("partitionKey", None) if query_params else None
        )
        api_resp["payload"] = self._get_alarm_media(
            alarm_id,
            tenant_ids=tenant_ids,
            return_boxes=boxes,
            partition_key=partition_key,
        )
        api_resp["message"] = "Processed successfully"
        api_resp["status"] = HTTPStatus.OK.value
        return api_resp, HTTPStatus.OK.value

    def _get_alarm_media(
        self,
        alarm_id: str,
        return_boxes: bool = False,
        tenant_ids: typing.Optional[typing.Sequence[str]] = None,
        partition_key: typing.Optional[int] = None,
    ) -> typing.Dict[str, typing.Any]:
        ret: typing.Dict[str, typing.Any] = {}
        alarm = self._ctrl_map.alarm.by_id(
            alarm_id, partition_key=partition_key
        )
        if alarm is None or (tenant_ids and alarm.tenant_id not in tenant_ids):
            log.error(
                "Alarm not found",
                alarm_id=alarm_id,
                partition_key=partition_key,
                alarm=alarm,
            )
            # TODO: change type of exception to reflect reason
            # Do not change error string!
            raise ValueError("Alarm not found")
        t_config: TenantConfig = self._ctrl_map.tenant.get_config(
            alarm.tenant_id
        )
        rescale_boxes = t_config.rescaleBoxes or False
        ret = {}
        media_dict = get_alarm_media_details(
            alarm, self._ctrl_map, self._alarm_mapper
        )
        ret["videoPath"] = media_dict["video_url"]
        if media_dict["image_urls"]:
            ret["imagePath"] = media_dict["image_urls"][0]

        ret["videoDetails"] = {
            "startTime": media_dict["video_start_timestamp"],
            "endTime": media_dict["video_end_timestamp"],
            "markers": marshal_video_markers(media_dict["video_markers"]),
        }
        if return_boxes:
            entities = self._ctrl_map.ml_output.get_entities(
                ml_uuids=[alarm.ml_output_id]
            )
            boxes = get_boxes_from_alarm_entities(entities, rescale_boxes)
            ret["boxes"] = boxes
        return ret

    def get_single_alarm(
        self,
        alarm_id: str,
        requesting_user: typing.Optional[Users] = None,
        partition_key: typing.Optional[int] = None,
    ) -> typing.Dict:
        if partition_key is not None:
            partition_key = int(partition_key)
            if partition_key > compute_partition_key():
                partition_key = compute_partition_key()
        result = self._ctrl_map.alarm.fetch_alarm(
            alarm_id, partition_key=partition_key
        )
        if result is None:
            # TODO: change type of exception to reflect reason
            # Do not change error string!
            raise ValueError("Alarm not found")
        (
            alarm,
            alarm_type_str,
            door,
            source_entity_cam,
            employee,
            door_mapped_cam_name,
            sop,
            door_location,
            cam_location,
            raw_alarms_metadata,
        ) = result
        if requesting_user:
            tenant_ids = requesting_user.msp_tenants
            msp_locations = requesting_user.msp_locations
            if not tenant_ids:
                tenant_ids = [requesting_user.tenant_id]
            if alarm.tenant_id not in tenant_ids:
                # TODO: change type of exception to reflect reason
                # Do not change error string!
                raise ValueError("Alarm from different tenant requested")
            if (
                msp_locations
                and door_location is not None
                and door_location.id not in msp_locations
            ):
                raise ValueError("Alarm from different location requested")

        tags = [
            i[0] for i in self._ctrl_map.alarm.fetch_latest_video_tags([alarm])
        ]
        location = door_location if door_location is not None else cam_location

        ret = marshal_single_alarm_result(
            alarm,
            alarm_type_str,
            tags,
            door,
            source_entity_cam,
            employee,
            door_mapped_cam_name,
            sop,
            extra_details=True,
            return_only_timeline=False,
            controller=self._ctrl_map,
            location=location,
            raw_alarms_metdata=raw_alarms_metadata,
        )
        media_dict = get_alarm_media_details(
            alarm, self._ctrl_map, self._alarm_mapper
        )
        ret["videoPath"] = media_dict["video_url"]
        if media_dict["image_urls"]:
            ret["imagePath"] = media_dict["image_urls"][0]

        ret["videoDetails"] = {
            "startTime": media_dict["video_start_timestamp"],
            "markers": marshal_video_markers(media_dict["video_markers"]),
        }

        if (
            alarm.source_entity_type == SourceEntityType.CAMERA
            and source_entity_cam is not None
        ):
            # marshal_single_alarm function doesn't has access to
            # controller map, hence we need to populate this attribute
            # here.
            audio_devices = self._ctrl_map.audio_devices_camera_map.get_audio_devices_for_camera(
                source_entity_cam.uuid
            )
            if "sourceEntity" in ret:
                ret["sourceEntity"]["audioDeviceMapped"] = bool(audio_devices)

        return ret

    @guard_endpoint(["alarm/details:view-updates"])
    def get_updates(
        self,
        alarm_id: str,
        user: typing.Optional[Users] = None,  # pylint: disable=unused-argument
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            ret = self._get_alarm_updates(alarm_id, user)
        except ValueError as exc:
            message = str(exc)
            if message == "Alarm updates do not exist":
                api_resp["status"] = HTTPStatus.FORBIDDEN.value
                api_resp["message"] = "Alarm updates do not exist"
                return (api_resp, HTTPStatus.FORBIDDEN.value)
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Bad request"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        api_resp["payload"] = ret
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Processed successfully"
        return api_resp

    def _get_alarm_updates(
        self, alarm_id: str, requesting_user: typing.Optional[Users] = None
    ):
        results = self._ctrl_map.alarm_updates.get_alarm_updates([alarm_id])
        if requesting_user:
            tenant_ids = requesting_user.msp_tenants
            if not tenant_ids:
                tenant_ids = [requesting_user.tenant_id]
            if any((i[0].tenant_id not in tenant_ids) for i in results):
                raise ValueError("Alarm updates do not exist")
        talkdown_ctrl = self._ctrl_map.talkdowns
        talkdown_dict = _marshal_talkdown_raw_alarm(alarm_id, talkdown_ctrl)
        return marshal_alarm_updates_result(results, talkdown_dict)


def marshal_video_markers(
    markers: typing.Sequence[typing.Dict],
) -> typing.Sequence[typing.Dict]:
    ret = []
    for marker in markers:
        marker_type = "Info"
        if marker["label"] == "Alarm Time":
            marker_type = "Warning"
        elif marker["label"] in ["Alarm Canceled"]:
            marker_type = "Error"
        ret.append(
            {
                "timestamp": marker["timestamp"],
                "label": marker["label"],
                "type": marker_type,
            }
        )

    return ret


def _marshal_talkdown_raw_alarm(
    alarm_id: typing.Optional[str],
    talkdown_ctrl: TalkdownsController,
) -> typing.Dict[str, typing.Any]:
    talkdowns = _fetch_talkdowns(talkdown_ctrl, alarm_id)
    ret = {
        "talkdowns": [
            _marshal_single_talkdown(talkdown) for talkdown in talkdowns
        ],
    }
    # TODO: Remove - keeping the above object for backward comaptibility
    # spreading to fix issue with showing correct talkdown on portal
    marshaled_talkdowns = ret["talkdowns"]
    for talkdown in marshaled_talkdowns:
        ret[talkdown["id"]] = talkdown
    return ret


def _fetch_talkdowns(
    talkdown_ctrl: TalkdownsController, raw_alarm_id: typing.Optional[str]
) -> typing.List[TalkDowns]:
    raw_alarm_ids = [raw_alarm_id]
    raw_alarm_talkdowns = talkdown_ctrl.get_raw_alarm_talkdowns(raw_alarm_ids)
    if len(raw_alarm_talkdowns) == 0:
        return []
    return raw_alarm_talkdowns


def _marshal_single_talkdown(talkdown):
    if not talkdown:
        return {}

    def parse_talkdown_details(details):
        if not details:
            return None
        if details.startswith("{") and details.endswith("}"):
            try:
                return json.loads(details)
            except json.JSONDecodeError:
                log.error(
                    "Failed to parse talkdown details as JSON", details=details
                )
                return None
        else:
            return {"audio_file_path": details, "audio_file_bucket": None}

    talkdown_details = parse_talkdown_details(talkdown.actual_talkdown_path)
    talkdown_path = ""
    if talkdown_details:
        audio_file_bucket = talkdown_details.get("audio_file_bucket")
        audio_file_path = talkdown_details.get("audio_file_path")
        if audio_file_bucket and audio_file_path:
            talkdown_path = get_signed_s3_url(
                audio_file_bucket, audio_file_path
            )
        elif audio_file_path:
            talkdown_path = audio_file_path

    ingested_talkdown_details = parse_talkdown_details(
        talkdown.recorded_talkdown_path
    )
    ingested_talkdown_s3_url = ""
    if ingested_talkdown_details:
        audio_file_bucket = ingested_talkdown_details.get("audio_file_bucket")
        audio_file_path = ingested_talkdown_details.get("audio_file_path")
        if audio_file_bucket and audio_file_path:
            ingested_talkdown_s3_url = get_signed_s3_url(
                audio_file_bucket, audio_file_path
            )
        elif audio_file_path:
            ingested_talkdown_s3_url = audio_file_path
    return {
        "id": talkdown.talkdown_id,
        "talkdown_type": talkdown.talkdown_type,
        "talkdown_state": talkdown.talkdown_state,
        "status": talkdown.status,
        "talkdown_audio": talkdown_path,
        "ingested_talkdown_audio": ingested_talkdown_s3_url,
        "custom_message": talkdown.custom_message,
        "talkdown_completed_utc": talkdown.request_completed_utc,
        "raw_alarm_id": talkdown.raw_alarm_id,
        "location_alarm_id": talkdown.location_alarm_id,
        "ingestion_status": talkdown.ingestion_status,
    }


def _get_talkdown_stack(talkdowns):
    if not talkdowns:
        return []
    talkdown_stack = []
    for talkdown in reversed(talkdowns):
        talkdown_stack.append(talkdown)
    return talkdown_stack


def marshal_alarm_updates_result(
    updates: typing.Sequence[typing.Tuple[AlarmUpdates, Users]],
    talkdown_dict: typing.Dict,
) -> typing.Dict:
    ret: typing.Dict[str, typing.Any] = {"items": []}
    talkdowns_stack = _get_talkdown_stack(talkdown_dict.get("talkdowns", None))
    for update, user in updates:
        tmp_dict = {
            "event": update.event,
            "timestamp": update.update_timestamp_utc.replace(
                tzinfo=pytz.utc
            ).isoformat(),
        }
        if update.plain_text_comment:
            tmp_dict["comment"] = update.plain_text_comment
        if update.current_status:
            tmp_dict["status"] = update.current_status
        details = update.update_details
        if details:
            audio_file_bucket = details.get("audio_file_bucket")
            audio_file_path = details.get("audio_file_path")
            if audio_file_bucket and audio_file_path:
                tmp_dict["audio_url"] = get_signed_s3_url(
                    audio_file_bucket, audio_file_path
                )
        if user is not None:
            tmp_dict["user"] = {"id": user.uuid, "name": user.name}
        if update.event == ALARM_UPDATES_SPEAKER_TALKDOWN_EVENT:
            if (
                details
                and details.get("audio_talkdown_id")
                and talkdown_dict.get(details.get("audio_talkdown_id"))
            ):
                tmp_dict["talkdown"] = [
                    talkdown_dict.get(details.get("audio_talkdown_id"))
                ]
            ## TODO : Only to accomodate old data. Remove after 1 month
            else:
                if not talkdowns_stack:
                    tmp_dict["talkdown"] = []
                else:
                    tmp_dict["talkdown"] = [talkdowns_stack.pop()]
        ret["items"].append(tmp_dict)
    return ret


def marshal_single_alarm_result(
    alarm: RawAlarms,
    alarm_type_str: str,
    tags: typing.Sequence[str],
    door: typing.Optional[Doors] = None,
    camera: typing.Optional[Cameras] = None,
    employee: typing.Optional[Employees] = None,
    mapped_cam: typing.Optional[str] = None,
    sop: typing.Optional[str] = None,
    extra_details=False,
    return_only_timeline=False,
    controller: typing.Optional[ctrl.ControllerMap] = None,
    tenant_name: typing.Optional[str] = None,
    location: typing.Optional[Locations] = None,
    raw_alarms_metdata: typing.Optional[RawAlarmsMetadata] = None,
):
    alarm_time_utc = alarm.alarm_timestamp_utc.replace(
        tzinfo=pytz.UTC
    ).isoformat()

    if return_only_timeline:
        tmp_dict = {
            "id": alarm.uuid,
            "videoStartTime": (
                alarm.video_start_timestamp_utc.replace(
                    tzinfo=pytz.utc
                ).isoformat()
                if alarm.video_start_timestamp_utc
                else None
            ),
            "videoEndTime": (
                alarm.video_end_timestamp_utc.replace(
                    tzinfo=pytz.utc
                ).isoformat()
                if alarm.video_end_timestamp_utc
                else None
            ),
        }
        return tmp_dict

    tmp_dict = {
        "id": alarm.uuid,
        "status": alarm.current_status,
        "timestamp": alarm_time_utc,
        "tap": alarm.true_alarm_probability,
        "sourceSystem": alarm.source_system,
        "videoStartTime": (
            alarm.video_start_timestamp_utc.replace(
                tzinfo=pytz.utc
            ).isoformat()
            if alarm.video_start_timestamp_utc
            else None
        ),
        "videoEndTime": (
            alarm.video_end_timestamp_utc.replace(tzinfo=pytz.utc).isoformat()
            if alarm.video_end_timestamp_utc
            else None
        ),
        "type": alarm_type_str,
        "door": {
            "id": door.uuid if door else None,
            "name": door.door_name if door else None,
        },
        "tenant": {
            "id": alarm.tenant_id,
            "name": tenant_name,
        },
        "tags": tags,
    }
    if location is not None:
        tmp_dict["location"] = {
            "id": location.id,
            "city": location.city,
            "country": location.country,
            "state": location.state,
            "timezone": location.timezone,
            "name": location.name,
            "description": location.description,
            "tenant_id": location.tenant_id,
        }
    elif door:
        tmp_dict["location"] = {
            "id": door.uuid,
            "city": "Unknown",
            "country": "Unknown",
            "state": "Unknown",
            "timezone": "Unknown",
            "name": door.city_and_building,
            "description": "Unknown",
        }

    if raw_alarms_metdata:
        tmp_dict["metadata"] = {
            "badgeNumber": raw_alarms_metdata.source_badge_num,
        }

    if employee is not None:
        tmp_dict["employee"] = {
            "id": employee.uuid,
            "name": employee.get_full_name(),
            "phoneNumber": employee.phone_number,
        }
    if alarm.source_entity_type == SourceEntityType.CAMERA:
        assert camera is not None, f"Camera missing for alarm {alarm.uuid}"
        tmp_dict["sourceEntity"] = {
            "id": camera.uuid,
            "name": camera.name,
            "type": SourceEntityType.CAMERA,
        }
    else:
        tmp_dict["sourceEntity"] = tmp_dict["door"]
        tmp_dict["sourceEntity"]["type"] = SourceEntityType.DOOR
    # return tmp_dict

    if not extra_details:
        return tmp_dict
    assert controller is not None, "Controller needed for extra alarm details!"
    return marshal_extra_detail_result(
        tmp_dict,
        (alarm, door, mapped_cam, employee, sop, location),
        controller,
    )


def marshal_extra_detail_result(
    base_dict: typing.Dict,
    details: typing.Tuple[
        RawAlarms,
        typing.Optional[Doors],
        typing.Optional[str],
        typing.Optional[Employees],
        typing.Optional[str],
        typing.Optional[Locations],
    ],
    controller: ctrl.ControllerMap,
):
    alarm, door, cam, _, sop, location = details
    floorplan_url = None
    if location and location.floorplan_url:
        floorplan_url = location.floorplan_url
    elif door and door.floorplan_image_path:
        floorplan_url = door.floorplan_image_path
    if floorplan_url is not None:
        s3_details = get_s3_path_details(floorplan_url)
        if s3_details is not None:
            base_dict["floorplanPath"] = get_signed_s3_url(
                s3_details[S3_BUCKET_NAME_KEY],
                s3_details[S3_FILE_NAME_KEY],
                expiration_secs=30,
            )

    if cam:
        base_dict["camera"] = cam
    if sop:
        base_dict["sop"] = sop
    if alarm_local_time := get_alarm_local_time(alarm, controller):
        if alarm_local_time.tzinfo is not None:
            alarm_local_time = alarm_local_time.replace(tzinfo=None)
        base_dict["localTime"] = alarm_local_time.isoformat()
    if alarm.resolution_timestamp_utc:
        base_dict["resolvedAt"] = alarm.resolution_timestamp_utc.replace(
            tzinfo=pytz.UTC
        ).isoformat()

    return base_dict


def marshal_get_alarms_query_result(
    results: typing.Sequence[FETCHED_ALARMS],
    tag_dict: typing.Dict[str, typing.List[str]],
    media_info: typing.Optional[typing.List[typing.Dict[str, typing.Any]]],
    total: typing.Optional[int],
    return_only_timeline: typing.Optional[bool] = False,
):
    ret: typing.Dict[str, typing.Any] = {"items": [], "total": total}
    if media_info:
        assert len(results) == len(media_info)
    for i, r in enumerate(results):
        (
            alarm,
            alarm_type_str,
            door,
            camera,
            employee,
            tenant_name,
            door_location,
            cam_location,
            raw_alarms_metadata,
        ) = r
        tmp_dict = marshal_single_alarm_result(
            alarm,
            alarm_type_str,
            tag_dict[alarm.uuid],
            door,
            camera,
            employee,
            location=door_location or cam_location,
            tenant_name=tenant_name,
            return_only_timeline=return_only_timeline,
            raw_alarms_metdata=raw_alarms_metadata,
        )
        if media_info is not None:
            tmp_dict["media"] = media_info[i]
        ret["items"].append(tmp_dict)
    return ret


def unmarshal_get_alarms_query(
    query_dict: typing.Optional[typing.Dict],
) -> typing.Tuple[
    AlarmFilters,
    typing.Optional[int],
    typing.Optional[int],
    typing.Optional[bool],
]:
    """Return filters, offset and limit"""
    if not query_dict:
        return AlarmFilters(display_only=True), None, None, None
    ret: AlarmFilters = AlarmFilters(
        utc_time_interval=(
            query_dict.get("dateFrom"),
            query_dict.get("dateTo"),
        ),
        display_only=True,
    )
    start_time: typing.Optional[datetime.datetime] = None
    if date_from := query_dict.get("dateFrom"):
        start_time = datetime.datetime.utcfromtimestamp(
            float(date_from) / 1000
        )
    end_time: typing.Optional[datetime.datetime] = None
    if date_to := query_dict.get("dateTo"):
        end_time = datetime.datetime.utcfromtimestamp(float(date_to) / 1000)
    if query_dict.get("relativeTimeMinutes") or query_dict.get(
        "relativeTimeHours"
    ):
        rtime_mins, rtime_hours = (
            query_dict.get("relativeTimeMinutes"),
            query_dict.get("relativeTimeHours"),
        )
        ret.utc_time_interval = parse_relative_time(rtime_mins, rtime_hours)
    else:
        ret.utc_time_interval = (start_time, end_time)

    # Aged alarms filters
    if aged_time_mins := query_dict.get("agedAlarmTimeMinutes"):
        aged_starttime, aged_endtime = parse_aged_alarm_times(
            ret.utc_time_interval[0], ret.utc_time_interval[1], aged_time_mins
        )
        ret.utc_time_interval = (aged_starttime, aged_endtime)

    if status_str := query_dict.get("statuses"):
        ret.status = parse_filter_str(status_str)
    if tenant_str := query_dict.get("tenants"):
        ret.tenant = parse_filter_str(tenant_str)
    if location_str := query_dict.get("locationIds"):
        ret.location = parse_filter_str(location_str)
    if door_str := query_dict.get("doors"):
        ret.door = parse_filter_str(door_str)
    if tenant_str := query_dict.get("tenants"):
        ret.tenant = parse_filter_str(tenant_str)
    if source_entity_str := query_dict.get("sourceEntities"):
        ret.source_entity = parse_filter_str(source_entity_str)
    if source_entity_id_str := query_dict.get("sourceEntityIds"):
        ret.source_entity_id = parse_filter_str(source_entity_id_str)
    if source_sys_str := query_dict.get("sourceSystems"):
        ret.source = parse_filter_str(source_sys_str)[0]
    if type_str := query_dict.get("types"):
        ret.alarm_type = parse_filter_str(type_str)
    if employee_str := query_dict.get("employeeIds"):
        ret.employee_uuid = parse_filter_str(employee_str)
    include_media = None
    page_params = get_page_params(query_dict)
    if alarm_uuids_str := query_dict.get("alarmIds"):
        ret.alarm_uuids, _ = parse_filter_str(alarm_uuids_str)
    if include_media_str := query_dict.get("includeMedia"):
        include_media = include_media_str == "true"
    if showMannedLocationAlarms := query_dict.get("showMannedLocationAlarms"):
        ret.showMannedLocationAlarms = showMannedLocationAlarms == "true"
    if partition_key := query_dict.get("partitionKey"):
        ret.partition_key = partition_key
    return (
        ret,
        page_params.get("offset"),
        page_params.get("limit"),
        include_media,
    )


def get_update_filter_time_range_for_msp_locations(filters: AlarmFilters):
    if filters.utc_time_interval and filters.utc_time_interval[0]:
        filter_start_time = filters.utc_time_interval[0]
        now = datetime.datetime.now()
        if filter_start_time is not None:
            # 10 minute buffer so that one month filter works properly
            if now - filter_start_time > datetime.timedelta(
                hours=MAX_RELATIVE_HOURS_FOR_MSP_LOCATIONS, minutes=10
            ):
                raise ValueError(
                    "Fetching alarms older than one month is not allowed"
                )
    ret = parse_relative_time(
        rtime_minutes=None, rtime_hours=MAX_RELATIVE_HOURS_FOR_MSP_LOCATIONS
    )
    return ret


def unmarshal_get_alarms_media_query(
    request_payload: typing.Dict,
) -> typing.Tuple[typing.List[str], bool, typing.Optional[datetime.datetime]]:
    """
    Unmarshal query parameters for get_alarms_media endpoint.

    Args:
        request_payload: Dictionary containing request parameters

    Returns:
        Tuple containing:
        - List of alarm IDs
        - Boolean indicating whether to include boxes
        - Timestamp for fetching media

    Raises:
        ValueError: If required parameters are missing or invalid
    """
    if not request_payload:
        raise ValueError("Request payload are required")

    # Get alarm IDs - required data
    alarm_ids = request_payload.get("alarmIds")
    if not alarm_ids:
        raise ValueError("alarmIds parameter is required")

    include_boxes = request_payload.get("includeBoxes") == True
    timestampString = request_payload.get("timestamp")
    timestamp = (
        datetime.datetime.fromisoformat(timestampString.replace("Z", "+00:00"))
        if timestampString
        else None
    )

    return alarm_ids, include_boxes, timestamp


def get_boxes_for_alarms_media(
    _ctrl_map: ControllerMap,
    alarms: typing.List[RawAlarms],
    rescale_boxes: bool,
):
    alarm_id_to_boxes: typing.Dict = {}
    ml_output_id_to_alarm_id = {}
    ml_output_ids = []
    for alarm in alarms:
        ml_output_id_to_alarm_id[alarm.ml_output_id] = alarm.uuid
        ml_output_ids.append(alarm.ml_output_id)

    entities = _ctrl_map.ml_output.get_entities(ml_uuids=ml_output_ids)

    # club entities for each raw alarm
    alarm_id_to_entities: typing.Dict[str, typing.List[MLEntities]] = {}

    for entity in entities:
        entity_alarm_id = ml_output_id_to_alarm_id[entity.ml_output_id]
        alarm_id_to_entities.setdefault(entity_alarm_id, []).append(entity)

    for alrm in alarms:
        if alrm.uuid in alarm_id_to_entities:
            alarm_entities = alarm_id_to_entities[alrm.uuid]
            boxes = get_boxes_from_alarm_entities(
                alarm_entities, rescale_boxes
            )
            if boxes:
                alarm_id_to_boxes[alrm.uuid] = boxes
    return alarm_id_to_boxes


def get_boxes_from_alarm_entities(
    entities: typing.Sequence[MLEntities], rescale_boxes: bool
):
    persons = [
        Person.from_json(entity.raw_data)
        for entity in entities
        if "track" in entity.raw_data
        and all(
            BaseDetection.from_json(box).det_type == EntityType.PERSON
            for box in entity.raw_data["track"]["boxes"]
        )
    ]
    # Filter out false positive person types
    persons = [
        person
        for person in persons
        if isinstance(person, Person)
        and person.person_type != PersonSubClassType.FALSE_POSITIVE
        and person.person_type != PersonSubClassType.REID_FALSE_POSITIVE
        and person.person_type
        != PersonSubClassType.OPTICAL_FLOW_FALSE_POSITIVE
    ]
    vehicles = [
        Vehicle.from_json(entity.raw_data)
        for entity in entities
        if "track" in entity.raw_data
        and all(
            BaseDetection.from_json(box).det_type == EntityType.VEHICLE
            for box in entity.raw_data["track"]["boxes"]
        )
    ]
    vehicles = [
        vehicle
        for vehicle in vehicles
        if isinstance(vehicle, Vehicle)
        and vehicle.vehicle_type == VehicleSubClassType.MOTION
    ]
    # Get largest bounding box around all different entities
    boxes = []
    if persons:
        if coords := get_box_around_entities(persons):
            if rescale_boxes:
                coords = [
                    coords[0] * 4 / 3,
                    coords[1],
                    coords[2] * 4 / 3,
                    coords[3],
                ]
            boxes.append(
                {
                    "frame": -1,
                    "coords": coords,
                    "type": "PERSON",
                }
            )

    if vehicles:
        if coords := get_box_around_entities(vehicles):
            if rescale_boxes:
                coords = [
                    coords[0] * 4 / 3,
                    coords[1],
                    coords[2] * 4 / 3,
                    coords[3],
                ]
            boxes.append(
                {
                    "frame": -1,
                    "coords": coords,
                    "type": "MOVING VEHICLE",
                }
            )
    return boxes
