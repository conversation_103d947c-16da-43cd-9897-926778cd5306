import typing
from http import HTT<PERSON>tatus

import structlog

from common_utils.cloud_config_utils import cloud_config_manager
from gateway.event_ingestion.event_ingestion_trait import EventIngestionTrait
from gateway.event_ingestion.integ_utils import (
    EventIngestionResult,
    EventVideoResponse,
    RequestEventVideo,
)
from gateway.http_api.orm.location_alarm.update_location_alarms import (
    LocationAlarmUpdater,
)
from interfaces.escalations import EscalationStatus, EscalationUpdateType
from models_rds.escalation_update import EscalationUpdates

RSPNDR_DEVICE_EVENTS_WHITELIST: typing.List[str] = []

log = structlog.get_logger("hakimo.webhooks", module="RSPNDR Event Ingestion")


class RspndrEventIngestionTrait(EventIngestionTrait):
    def ingest_event(
        self, payload: typing.Optional[typing.Dict]
    ) -> EventIngestionResult:
        locn_alarm_updater = LocationAlarmUpdater(self._controller)
        incident_id = None
        if payload is None:
            log.warning("Received None for the main Rspndr payload.")
            return EventIngestionResult(
                status=HTTPStatus.BAD_REQUEST.value,
                message="Rspndr Event Ingestion Failed: Payload was None",
            )
        nested_payload = payload.get("payload")
        if nested_payload:
            incident_id = nested_payload.get("incident_id")
            event = nested_payload.get("event")
        else:
            log.warning(
                "Outer 'payload' key not found or is None in the received data."
            )
            return EventIngestionResult(
                status=HTTPStatus.BAD_REQUEST.value,
                message="Rspndr Event Ingestion Failed: Nested payload missing",
            )
        is_rspndr_closure_active = cloud_config_manager.is_enabled(
            "rspndr_escalate.is_enabled"
        )

        escalation = self._controller.escalations.get_escalation_by_id(
            incident_id
        )

        if escalation is not None:
            log.info(
                "Processing RSPNDR response for escalation",
                escalation_id=incident_id,
            )
            location = self._controller.locations.get_location_by_id(
                escalation.location_id
            )
            if location is None:
                log.warning("Location not found for escalation")
                return EventIngestionResult(
                    status=HTTPStatus.BAD_REQUEST.value,
                    message="Rspndr Event Ingestion Failed: Location not found",
                )
            tenant_id = location.tenant_id
            user = self._controller.user.get_users(
                tenant_ids=[tenant_id], names=["Rspndr Support"]
            )
            user_obj, _ = user[0]

            comment = f"RSPNDR notification received : {event}"
            escalation_status = EscalationStatus.IN_PROGRESS
            if is_rspndr_closure_active and event == "FINISHED":
                log.info(
                    "Rspndr closure is active, so closing the escalation",
                    escalation_id=incident_id,
                )
                self._controller.escalations.update_escalation(
                    escalation_id=escalation.int_id,
                    current_status=EscalationStatus.RESOLVED,
                    resolution_comment=comment,
                    resolved_by_user_id=user_obj.uuid,
                )
                escalation_status = EscalationStatus.RESOLVED

            escalation_update = EscalationUpdates(
                escalation_id=incident_id,
                update_type=EscalationUpdateType.RSPNDR,
                update_status=escalation_status,
                user_id=user_obj.uuid,
                update_text=comment,
            )
            self._controller.escalations.create_escalation_updates(
                escalation_update
            )
        else:
            location_alarm = (
                self._controller.location_alarms.get_location_alarm_by_id(
                    incident_id
                )
            )
            if location_alarm is None:
                log.warning(
                    "Neither escalation nor location alarm found",
                    incident_id=incident_id,
                )
                return EventIngestionResult(
                    status=HTTPStatus.BAD_REQUEST.value,
                    message="Rspndr Event Ingestion Failed: Incident not found",
                )
            location_id = location_alarm.location_id
            location = self._controller.locations.get_location_by_id(
                location_id
            )
            if location is None:
                log.warning("Location not found")
                return EventIngestionResult(
                    status=HTTPStatus.BAD_REQUEST.value,
                    message="Rspndr Event Ingestion Failed: Location not found",
                )
            tenant_id = location.tenant_id
            user = self._controller.user.get_users(
                tenant_ids=[tenant_id], names=["Rspndr Support"]
            )

            update_payload = {
                "comment": f"RSPNDR notification received : {event}",
            }

            if is_rspndr_closure_active and event == "FINISHED":
                log.info(
                    "Rspndr closure is active, so closing the alarm",
                    location_alarm_id=incident_id,
                )
                update_payload["status"] = "Resolved"
            user_obj, _ = user[0]
            locn_alarm_updater.update_location_alarm_public(
                incident_id, update_payload, user_obj
            )
        return EventIngestionResult(
            status=HTTPStatus.OK.value,
            message="Rspndr Event Ingested Successfully",
        )

    def request_video(
        self, request_video_payload: RequestEventVideo
    ) -> EventVideoResponse:
        """
        Sends the request for fetching video for the event or creates a video from the given payload
        """
        raise NotImplementedError
