import typing

import structlog

from gateway.event_ingestion.earthcam_event_ingestion import (
    EarthcamEventIngestionTrait,
)
from gateway.event_ingestion.event_ingestion_trait import EventIngestionTrait
from gateway.event_ingestion.integ_utils import WebhookIngestionSourceEnum
from gateway.event_ingestion.reconeyez_event_ingestion import (
    ReconeyezEventIngestionTrait,
)
from gateway.event_ingestion.rspndr_ingestion_trait import (
    RspndrEventIngestionTrait,
)
from gateway.event_ingestion.yoursix_event_ingestion import (
    YoursixEventIngestionTrait,
)
from models_rds.rds_client import RDSClient

log = structlog.get_logger(
    "hakimo.webhooks", module="Webhook Integration Factory"
)


class EventIngestionFactory:
    def __init__(self, rds: RDSClient) -> None:
        self._rds = rds
        self._integ_map: typing.Dict = {}

    def get_integration_trait(
        self, integration_type: WebhookIngestionSourceEnum
    ) -> EventIngestionTrait:
        if integration_type not in self._integ_map:
            self._integ_map[integration_type] = self.setup_integ_trait(
                integration_type
            )
        return self._integ_map[integration_type]

    def setup_integ_trait(
        self, integration_type: WebhookIngestionSourceEnum
    ) -> EventIngestionTrait:
        if integration_type == WebhookIngestionSourceEnum.YOURSIX:
            return YoursixEventIngestionTrait(self._rds)
        elif integration_type == WebhookIngestionSourceEnum.RECONEYEZ:
            return ReconeyezEventIngestionTrait(self._rds)
        elif integration_type == WebhookIngestionSourceEnum.EARTHCAM:
            return EarthcamEventIngestionTrait(self._rds)
        elif integration_type == WebhookIngestionSourceEnum.RSPNDR:
            return RspndrEventIngestionTrait(self._rds)

        log.error(
            "Unsupported Integration Type for Webhook Event Ingestion",
            integ_type=integration_type.name,
        )
        raise Exception(
            "Unsupported Integration Type for Webhook Event Ingestion"
        )
