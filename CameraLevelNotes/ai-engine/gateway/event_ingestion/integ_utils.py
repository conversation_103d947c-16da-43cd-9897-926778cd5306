import datetime
import typing
from dataclasses import dataclass
from enum import Enum

import structlog

from interfaces.alarm import Alarm
from models_rds.cameras import Cameras

log = structlog.get_logger("hakimo.webhooks", module="Webhook Utils")


class WebhookIngestionSourceEnum(str, Enum):
    YOURSIX = "yoursix"
    RECONEYEZ = "reconeyez"
    UNKNOWN = "unknown"
    EARTHCAM = "earthcam"
    RSPNDR = "rspndr"


@dataclass
class EventIngestionResult:
    status: int
    message: str
    event_ingested: bool = False
    return_video: bool = False
    alarm: typing.Optional[Alarm] = None
    camera: typing.Optional[Cameras] = None
    site_id: typing.Optional[str] = ""
    device_id: typing.Optional[str] = ""


@dataclass
class RequestEventVideo:
    alarm_time_utc: datetime.datetime
    last_motion_time_utc: datetime.datetime
    tenant_id: str
    camera: typing.Optional[Cameras] = None
    alarm: typing.Optional[Alarm] = None
    video_required: bool = False
    event_payload: typing.Optional[typing.Dict] = None


@dataclass
class EventVideoResponse:
    success: bool = False
    video_url: typing.Optional[str] = None
    video_response: typing.Optional[dict] = None


def validate_integration_type(
    payload: typing.Optional[typing.Dict],
) -> WebhookIngestionSourceEnum:
    if payload is None:
        log.debug("Invalid Webhook Ingestion Payload", payload=payload)
        return WebhookIngestionSourceEnum.UNKNOWN

    # Every Y6 event will have the associated centralStationAccount, which is in turn subscribed for webhooks.
    # So, this becomes our condition to identify Y6 event payloads
    if "centralStationAccount" in payload:
        return WebhookIngestionSourceEnum.YOURSIX

    if "source_system" in payload and payload["source_system"] == "earthcam":
        return WebhookIngestionSourceEnum.EARTHCAM

    if (
        "request_id" in payload
        and "tenant_id" in payload
        and "payload" in payload
        and "incident_id" in payload["payload"]
    ):
        return WebhookIngestionSourceEnum.RSPNDR

    # TODO: Add a condition for Reconeyez events once the payload is available.
    return WebhookIngestionSourceEnum.RECONEYEZ
