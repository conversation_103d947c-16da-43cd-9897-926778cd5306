import os
import os.path as osp
import typing
import uuid
from urllib.parse import urlparse

import structlog

from common_utils.rpc import Rpc
from config import appliance_config as config
from ingestion_processor.audio_task_utils import (
    get_audio_filepath,
    get_audio_livestream_file_path,
)
from integ.action_cs.acs_audio_api import AcsAudioApi
from integ.axis.audio_api import AxisAudioAPI
from integ.digital_vision_systems.audio_api import (
    VisionDetectionSystemsAudioAPI as VdsAudioAPI,
)
from integ.go2rtc_onvif.audio_api import Go2rtcOnvifAudioAPI
from integ.unv.audio_api import UnvAudioAPI

log = structlog.get_logger("hakimo", module="onvifAudioInterface")

AUDIO_DEVICE_TYPES = (
    "AXIS",
    "ONVIF",
    "ACS",
    "AXIS_SIREN",
    "GO2RTC_ONVIF",
    "VDS",
    "UNV",
)


def get_audio_password_file(
    audio_payload: typing.Dict[str, typing.Any],
) -> str:
    """Get passsword file path from the payload and verify the password file exists. Raise an
    exception if passsword file path doesn't exist."""

    if audio_payload.get("password_file_path") is not None:
        password_file = audio_payload["password_file_path"]
    else:
        raise FileNotFoundError(
            "Password file path not in audio payload",
        )

    if password_file is None:
        if audio_payload.get("client_audio_device_id") is not None:
            password_file = f"/secure/audio_password_{audio_payload['client_audio_device_id']}"
            if not osp.exists(password_file):
                log.warning(
                    "Password file not found, falling back to use camera client id for audio password.",
                    password_file_path=password_file,
                )
                password_file = f"/secure/audio_password_{audio_payload['camera_client_id']}"
        else:
            log.warning("Deprecated. Audio payload uses camera client id.")
            password_file = (
                f"/secure/audio_password_{audio_payload['camera_client_id']}"
            )

    if osp.exists(password_file):
        return password_file

    log.error("Audio password file not found.", password_file=password_file)
    raise FileNotFoundError(f"Name:{password_file}")


def play_axis_siren_profile(
    audio_payload: typing.Dict,
    task_id: typing.Optional[str] = str(uuid.uuid4()),
):
    """
    Calls the Axis siren_and_light.cgi API to run the siren and light
    profile on the AXIS_SIREN products
    """
    password_file = get_audio_password_file(audio_payload)
    axis_rpc = Rpc(
        {
            "url": audio_payload["url"],
            "digest_auth": {
                "username": audio_payload["username"],
                "password_file": password_file,
            },
        }
    )
    axis_client = AxisAudioAPI(axis_rpc)

    siren_resp = axis_client.play_siren_profile(
        audio_payload["clip_name"], task_id=task_id
    )
    log.info(
        "Initiate Axis Siren Status",
        status="Done",
        clip_name=audio_payload["clip_name"],
    )
    return siren_resp


def play_audio_via_ACS(audio_payload: typing.Dict[str, typing.Any]) -> bool:
    audio_url = audio_payload["url"]
    audio_file_path = get_audio_filepath(audio_payload)
    if audio_file_path is None:
        log.error(
            "Couldn't find file containing audio data.",
            payload=audio_payload,
        )
        raise FileNotFoundError("Couldn't find file containing audio data.")

    # Because we store the ACS Audio URLs as http://ip:port/audio/transmit.cgi
    if str(audio_url).endswith("cgi"):
        audio_url = urlparse(audio_url)
        audio_url = audio_url._replace(
            path=audio_url.path[: audio_url.path.rfind("/") + 1]
        ).geturl()

    action_cs_rpc = Rpc(
        {
            "url": audio_url,
        }
    )
    action_cs_client = AcsAudioApi(action_cs_rpc)
    ret_stat = action_cs_client.play_audio_file(audio_url, audio_file_path)
    strobe_ret_stat = action_cs_client.play_acs_strobe("10")
    log.info(
        "Play audio task via ACS",
        audio_status=ret_stat,
        strobe_status=strobe_ret_stat,
    )
    return ret_stat


def play_audio_via_axis(audio_payload: typing.Dict[str, typing.Any]) -> bool:
    password_file = get_audio_password_file(audio_payload)
    axis_rpc = Rpc(
        {
            "url": audio_payload["url"],
            "digest_auth": {
                "username": audio_payload["username"],
                "password_file": password_file,
            },
        }
    )
    axis_client = AxisAudioAPI(axis_rpc)

    if "s3_file_path" in audio_payload:
        delete_post_play = True
        audio_file_path = get_audio_filepath(audio_payload)

        if audio_file_path is None:
            audio_file_path = get_audio_livestream_file_path(
                audio_payload, config.HIP.VMS["LIVESTREAM_ROOT"]
            )
            delete_post_play = False

        if audio_file_path is not None:
            axis_stat = axis_client.transmit_audio(audio_file_path)
            if delete_post_play:
                try:
                    os.remove(audio_file_path)
                except FileNotFoundError:
                    pass
        else:
            log.error(
                "Couldn't find filepath to livestream audio from",
                audio_file_path=audio_file_path,
            )
            axis_stat = False
        log.info("Play audio task via Axis", status="Done")
    elif "clip_name" in audio_payload:
        clips_dict = axis_client.list_media_clips()
        clip_id = None
        for cid in clips_dict:
            if clips_dict[cid]["Name"] == audio_payload["clip_name"]:
                clip_id = cid

        if clip_id:
            axis_stat = axis_client.play_media_clip(clip_id)
        else:
            log.error(
                "Failed to find clip", clip_name=audio_payload["clip_name"]
            )
            axis_stat = False
        log.info("Play audio task via Axis", status="Done")
    else:
        log.error("Invalid payload", payload=audio_payload)
        axis_stat = False
    return axis_stat


def play_audio_via_go2rtc_onvif(
    audio_payload: typing.Dict[str, typing.Any],
) -> bool:
    go2rtc_onvif_rpc = Rpc(
        {
            "url": audio_payload["url"],
        }
    )
    go2rtc_onvif_client = Go2rtcOnvifAudioAPI(
        go2rtc_onvif_rpc, audio_payload["destination"]
    )
    if "clip_name" in audio_payload:
        go2rtc_onvif_stat = go2rtc_onvif_client.play_remote_file(
            audio_payload["clip_name"]
        )
    elif "text" in audio_payload:
        go2rtc_onvif_stat = go2rtc_onvif_client.play_text_to_speech(
            audio_payload["text"]
        )
    else:
        log.error("Invalid payload", payload=audio_payload)
        go2rtc_onvif_stat = False
    log.info("Play audio task via Go2rtc Onvif", status="Done")
    return go2rtc_onvif_stat


def play_audio_via_vds(audio_payload: typing.Dict[str, typing.Any]) -> bool:
    vds_rpc = Rpc(
        {
            "url": audio_payload["url"],
        }
    )
    vds_client = VdsAudioAPI(vds_rpc)
    if "clip_name" in audio_payload:
        vds_stat = vds_client.play_media_clip(audio_payload["clip_name"])
    elif "s3_file_path" in audio_payload:
        delete_post_play = True
        audio_file_path = get_audio_filepath(audio_payload)

        if audio_file_path is None:
            audio_file_path = get_audio_livestream_file_path(
                audio_payload, config.HIP.VMS["LIVESTREAM_ROOT"]
            )
            delete_post_play = False

        if audio_file_path is not None:
            vds_stat = vds_client.upload_and_play_audio(audio_file_path)  # pylint: disable=too-many-function-args
            if delete_post_play:
                try:
                    os.remove(audio_file_path)
                except FileNotFoundError:
                    pass
        else:
            log.error(
                "Couldn't find filepath to livestream audio from",
                audio_file_path=audio_file_path,
            )
            vds_stat = False
        log.info("Play audio task via VDS", status="Done")
    else:
        log.error("Invalid payload", payload=audio_payload)
        vds_stat = False
    return vds_stat


def play_audio_via_unv(
    audio_payload: typing.Dict[str, typing.Any],
) -> bool:
    unv_rpc = Rpc(
        {
            "url": audio_payload["url"],
        }
    )
    unv_client = UnvAudioAPI(unv_rpc)
    if "clip_name" in audio_payload:
        unv_stat = unv_client.play_media_clip(audio_payload["clip_name"])
    elif "s3_file_path" in audio_payload:
        delete_post_play = True
        audio_file_path = get_audio_filepath(audio_payload)

        if audio_file_path is None:
            audio_file_path = get_audio_livestream_file_path(
                audio_payload, config.HIP.VMS["LIVESTREAM_ROOT"]
            )
            delete_post_play = False

        if audio_file_path is not None:
            unv_stat = unv_client.upload_and_play_audio(audio_file_path)  # pylint: disable=too-many-function-args
            if delete_post_play:
                try:
                    os.remove(audio_file_path)
                except FileNotFoundError:
                    pass
        else:
            log.error(
                "Couldn't find filepath to livestream audio from",
                audio_file_path=audio_file_path,
            )
            unv_stat = False
        log.info("Play audio task via UNV", status="Done")
    else:
        log.error("Invalid payload", payload=audio_payload)
        unv_stat = False
    return unv_stat
