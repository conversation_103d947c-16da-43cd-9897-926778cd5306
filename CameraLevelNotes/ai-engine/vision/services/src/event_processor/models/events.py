from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Literal, Optional


class AlarmGroupQueueState(str, Enum):
    PENDING = "pending"  # Waiting to be queued
    QUEUED = "queued"  # In the queue
    ASSIGNED = "assigned"  # Assigned to operator


class AlarmGroupRecommendation(str, Enum):
    ANALYZING = "analyzing"
    RESOLVE = "resolve"
    ESCALATE = "escalate"
    ESCALATE_ZERO_TOLERANCE = "escalate_zero_tolerance"
    ERROR = "error"

    @classmethod
    def from_str(cls, value: str) -> "AlarmGroupRecommendation":
        return cls(value.lower())


class EventRecommendation(str, Enum):
    RESOLVE = "resolve"
    ESCALATE = "escalate"
    NOT_KNOWN = "not_known"
    ERROR = "error"

    @classmethod
    def from_str(cls, value: str) -> "EventRecommendation":
        return cls(value.lower())


class EventState(str, Enum):
    PROCESSED = "PROCESSED"
    UNPROCESSED = "UNPROCESSED"
    ERROR = "ERROR"

    @classmethod
    def from_str(cls, value: str) -> "EventState":
        return cls(value.lower())


class AlarmGroupState(str, Enum):
    PENDING = "pending"
    PENDING_CUTOFF = "pending_cutoff"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"  # Why do we need this?
    RESOLVED_BY_HAKIMO = "resolved_by_hakimo"
    RESOLVED_BY_OPERATOR = "resolved_by_operator"

    @classmethod
    def from_str(cls, value: str) -> "AlarmGroupState":
        return cls(value.lower())


class Resolution(str, Enum):
    OPEN = "open"
    SAFE = "safe"
    ESCALATION_OPEN = "escalation_open"
    ESCALATION_CLOSE = "escalation_close"

    @classmethod
    def from_str(cls, value: str) -> "Resolution":
        return cls(value.lower())


class Severity(str, Enum):
    HIGH = "high"
    LOW = "low"
    MODERATE = "moderate"
    NOT_KNOWN = "not_known"
    ZERO_TOLERANCE = "zero_tolerance"

    @classmethod
    def from_str(cls, value: str) -> "Severity":
        return cls(value.lower())


class EventType(str, Enum):
    DETECTIONEVENT = "detectionevent"
    LLMEVENT = "llmevent"
    LLMALARMGROUPEVENT = "llmalarmgroupevent"
    SAFE = "safe"
    ESCALATION_OPEN = "escalationopen"
    ESCALATION_CLOSE = "escalationclosed"
    ALLOCATION = "allocation"
    ORPHAN = "orphan"
    TALKDOWN = "talkdown"
    TWILIO_CALL = "twiliocall"
    TWILIO_MESSAGE = "twiliomessage"
    INVESTIGATION = "investigation"
    AUTO_RESOLVED = "autoresolvedevent"
    ALARM_GROUP_CUTOFF = "alarmgroupcutoff"
    GET_ALARM = "getalarmevent"
    QUEUE_STATE_UPDATE = "alarm_group_queue_state_update"

    @classmethod
    def from_str(cls, value: str) -> "EventType":
        return cls(value.lower())


@dataclass
class Frame:
    url: str
    timestamp: int

    @classmethod
    def from_list(cls, data: List) -> "Frame":
        return cls(url=data[0], timestamp=data[1])


@dataclass
class BoundingBox:
    x1: float
    y1: float
    x2: float
    y2: float


@dataclass
class Person:
    name: str
    class_id: int
    confidence: float
    box: BoundingBox
    person_id: str
    deadzone_overlap: float

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Person":
        return cls(
            name=data["name"],
            class_id=data["class"],
            confidence=data["confidence"],
            box=BoundingBox(**data["box"]),
            person_id=data["person_id"],
            deadzone_overlap=data["deadzone_overlap"]
            if "deadzone_overlap" in data
            else data["overlap"],
        )


@dataclass
class Metadata:
    frames: List[Frame]
    persons: List[Person]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Metadata":
        return cls(
            frames=[Frame.from_list(frame) for frame in data["frames"]],
            persons=[Person.from_dict(person) for person in data["persons"]],
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "frames": [[frame.url, frame.timestamp] for frame in self.frames],
            "persons": [
                {
                    "name": person.name,
                    "class": person.class_id,
                    "confidence": person.confidence,
                    "box": {
                        "x1": person.box.x1,
                        "y1": person.box.y1,
                        "x2": person.box.x2,
                        "y2": person.box.y2,
                    },
                    "person_id": person.person_id,
                    # "deadzone_overlap": person.deadzone_overlap,
                    "overlap": person.deadzone_overlap,
                }
                for person in self.persons
            ],
        }


@dataclass
class DetectionEvent:
    id: str
    event_id: str
    camera_id: str
    tenant_id: str
    group_id: str
    objects: Dict[str, int]
    timestamp_utc: datetime
    event_timestamp_utc: datetime
    camera_name: str
    metadata: Metadata
    event_type: str
    device_id: str
    severity: Severity
    site_alarm_id: str
    alarm_group_id: str
    is_escalation: bool
    is_past_event: bool
    is_dedup: bool

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DetectionEvent":
        return cls(
            id=data["id"],
            event_id=data["event_id"],
            camera_id=data["camera_id"],
            tenant_id=data["tenant_id"],
            group_id=data["group_id"],
            objects=data["objects"],
            timestamp_utc=datetime.fromtimestamp(
                data["timestamp_utc"] / 1000, timezone.utc
            ),
            event_timestamp_utc=datetime.fromtimestamp(
                data["event_timestamp_utc"] / 1000, timezone.utc
            ),
            camera_name=data["camera_name"],
            metadata=Metadata.from_dict(data["metadata"]),
            event_type=data["event_type"],
            device_id=data["device_id"],
            severity=Severity.from_str(data.get("severity", "not_known")),
            site_alarm_id=data.get("site_alarm_id", ""),
            alarm_group_id=data.get("alarm_group_id", ""),
            is_escalation=data.get("is_escalation", False),
            is_past_event=data.get("is_past_event", False),
            is_dedup=data.get("is_dedup", False),
        )


@dataclass
class BaseEvent:
    event_type: str
    site_alarm_id: Optional[str]
    group_id: str
    timestamp_utc: datetime
    operator_id: str
    tenant_id: str

    @classmethod
    def from_dict(cls, data: dict) -> "BaseEvent":
        return cls(
            event_type=data["event_type"],
            site_alarm_id=data.get("site_alarm_id"),
            group_id=data["group_id"],
            timestamp_utc=datetime.fromtimestamp(
                data["timestamp_utc"] / 1000, timezone.utc
            ),
            operator_id=data["operator_id"],
            tenant_id=data["tenant_id"],
        )


@dataclass
class SafeEvent(BaseEvent):
    resolution_comment: Literal["False", "Guard", "Resident"]
    severity: Severity

    @classmethod
    def from_dict(cls, data: dict) -> "SafeEvent":
        return cls(
            event_type="Safe",
            site_alarm_id=data.get("site_alarm_id"),
            group_id=data["group_id"],
            timestamp_utc=datetime.fromtimestamp(
                data["timestamp_utc"] / 1000, timezone.utc
            ),
            operator_id=data["operator_id"],
            tenant_id=data["tenant_id"],
            resolution_comment=data["resolution_comment"],
            severity=Severity.from_str(data.get("severity", "not_known")),
        )


@dataclass
class AutoResolvedEvent(BaseEvent):
    resolution_comment: str

    @classmethod
    def from_dict(cls, data: dict) -> "AutoResolvedEvent":
        return cls(
            event_type="AutoResolved",
            site_alarm_id=data.get("site_alarm_id"),
            group_id=data["group_id"],
            timestamp_utc=datetime.fromtimestamp(
                data["timestamp_utc"] / 1000, timezone.utc
            ),
            operator_id=data["operator_id"],
            tenant_id=data["tenant_id"],
            resolution_comment=data["resolution_comment"],
        )


@dataclass
class GetAlarmEvent(BaseEvent):
    tenant_ids: List[str]
    limit: int
    order: str
    recommendation: str

    @classmethod
    def from_dict(cls, data: dict) -> "GetAlarmEvent":
        return cls(
            event_type="GetAlarmEvent",
            site_alarm_id=data.get("site_alarm_id"),
            group_id=data["group_id"],
            timestamp_utc=datetime.fromtimestamp(
                data["timestamp_utc"] / 1000, timezone.utc
            ),
            operator_id=data["operator_id"],
            tenant_id=data["tenant_id"],
            tenant_ids=data["tenant_ids"],
            limit=data["limit"],
            order=data["order"],
            recommendation=data["recommendation"],
        )


@dataclass
class AllocationEvent(BaseEvent):
    @classmethod
    def from_dict(cls, data: dict) -> "AllocationEvent":
        return cls(
            event_type="Allocation",
            **{
                k: data[k]
                for k in [
                    "site_alarm_id",
                    "group_id",
                    "timestamp_utc",
                    "operator_id",
                    "tenant_id",
                ]
            },
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "event_type": self.event_type,
            "site_alarm_id": self.site_alarm_id,
            "group_id": self.group_id,
            "timestamp_utc": self.timestamp_utc,
            "operator_id": self.operator_id,
            "tenant_id": self.tenant_id,
        }


# Similar pattern for other events
@dataclass
class OrphanEvent(BaseEvent):
    @classmethod
    def from_dict(cls, data: dict) -> "OrphanEvent":
        return cls(
            event_type="Orphan",
            site_alarm_id=data.get("site_alarm_id"),
            group_id=data["group_id"],
            timestamp_utc=datetime.fromtimestamp(
                data["timestamp_utc"] / 1000, timezone.utc
            ),
            operator_id=data["operator_id"],
            tenant_id=data["tenant_id"],
        )


@dataclass
class TalkDownEvent(BaseEvent):
    @classmethod
    def from_dict(cls, data: dict) -> "TalkDownEvent":
        return cls(
            event_type="TalkDown",
            **{
                k: data[k]
                for k in [
                    "site_alarm_id",
                    "group_id",
                    "timestamp_utc",
                    "operator_id",
                    "tenant_id",
                ]
            },
        )


@dataclass
class TwilioCallEvent(BaseEvent):
    @classmethod
    def from_dict(cls, data: dict) -> "TwilioCallEvent":
        return cls(
            event_type="TwilioCall",
            **{
                k: data[k]
                for k in [
                    "site_alarm_id",
                    "group_id",
                    "timestamp_utc",
                    "operator_id",
                    "tenant_id",
                ]
            },
        )


@dataclass
class TwilioMessageEvent(BaseEvent):
    @classmethod
    def from_dict(cls, data: dict) -> "TwilioMessageEvent":
        return cls(
            event_type="TwilioMessage",
            **{
                k: data[k]
                for k in [
                    "site_alarm_id",
                    "group_id",
                    "timestamp_utc",
                    "operator_id",
                    "tenant_id",
                ]
            },
        )


@dataclass
class EscalationOpenEvent(BaseEvent):
    resolution_comment: str

    @classmethod
    def from_dict(cls, data: dict) -> "EscalationOpenEvent":
        return cls(
            event_type="EscalationOpen",
            site_alarm_id=data.get("site_alarm_id"),
            group_id=data["group_id"],
            timestamp_utc=datetime.fromtimestamp(
                data["timestamp_utc"] / 1000, timezone.utc
            ),
            operator_id=data["operator_id"],
            tenant_id=data["tenant_id"],
            resolution_comment=data.get("resolution_comment", ""),
        )


@dataclass
class EscalationCloseEvent(BaseEvent):
    resolution_comment: str

    @classmethod
    def from_dict(cls, data: dict) -> "EscalationCloseEvent":
        return cls(
            event_type="EscalationClose",
            site_alarm_id=data.get("site_alarm_id"),
            group_id=data["group_id"],
            timestamp_utc=datetime.fromtimestamp(
                data["timestamp_utc"] / 1000, timezone.utc
            ),
            operator_id=data["operator_id"],
            tenant_id=data["tenant_id"],
            resolution_comment=data.get("resolution_comment", ""),
        )


@dataclass
class InvestigationEvent(BaseEvent):
    @classmethod
    def from_dict(cls, data: dict) -> "InvestigationEvent":
        return cls(
            event_type="Investigation",
            site_alarm_id=data.get("site_alarm_id"),
            group_id=data["group_id"],
            timestamp_utc=datetime.fromtimestamp(
                data["timestamp_utc"] / 1000, timezone.utc
            ),
            operator_id=data["operator_id"],
            tenant_id=data["tenant_id"],
        )


@dataclass
class LLMEvent:
    id: str
    alarm_group_id: str
    camera_group_id: str
    camera_id: str
    event_details: Dict[str, Any]
    tenant_id: str
    severity: Severity
    event_time_utc: datetime
    is_created: bool

    """
    Sample event:
    {
        'id': '111de6ea-dbc6-4162-8799-f8c3e798edae',
        'severity': 'high',
        'event_time_utc': '2025-04-12T10:15:17.720000',
        'event_details': {'metadata': {...}, 'objects': {...}},
        'tenant_id': 'eisley-test',
        'camera_group_id': '4df5f1e9-fb07-47c3-ab13-9543d3d0584a',
        'camera_id': '0cbf51aa-304f-4f27-9d2e-a5d59d5928cc',
        'alarm_group_id': '15d5fb50-59f4-4682-938e-f8a32f127759',
        'is_created': True
    }
    """

    @classmethod
    def from_dict(cls, data: dict) -> "LLMEvent":
        # Handle ISO format datetime string
        if isinstance(data["event_time_utc"], str):
            try:
                # Try to parse as ISO format
                event_time = datetime.fromisoformat(data["event_time_utc"])
            except ValueError:
                # If not ISO format, try to parse as numeric string
                event_time = datetime.fromtimestamp(
                    float(data["event_time_utc"]) / 1000,
                    tz=timezone.utc,
                )
        else:
            # Handle numeric timestamp
            event_time = datetime.fromtimestamp(
                data["event_time_utc"] / 1000, tz=timezone.utc
            )

        return cls(
            id=data["id"],
            alarm_group_id=data["alarm_group_id"],
            camera_group_id=data["camera_group_id"],
            camera_id=data["camera_id"],
            event_details=data["event_details"],
            tenant_id=data["tenant_id"],
            severity=Severity.from_str(data["severity"]),
            event_time_utc=event_time,
            is_created=data.get("is_created", False),
        )


@dataclass
class LLMAlarmGroupEvent:
    alarm_group_id: str
    camera_group_id: str
    tenant_id: str
    is_created: bool

    """
    Sample event:
    {
        'tenant_id': 'eisley-test',
        'camera_group_id': '4df5f1e9-fb07-47c3-ab13-9543d3d0584a',
        'alarm_group_id': '15d5fb50-59f4-4682-938e-f8a32f127759',
        'is_created': True
    }
    """

    @classmethod
    def from_dict(cls, data: dict) -> "LLMAlarmGroupEvent":
        return cls(
            alarm_group_id=data["alarm_group_id"],
            camera_group_id=data["camera_group_id"],
            tenant_id=data["tenant_id"],
            is_created=data.get("is_created", False),
        )


def event_details_json(detection_event: DetectionEvent) -> Dict[str, Any]:
    """
    id: str
    event_id: str
    camera_id: str
    tenant_id: str
    group_id: str
    objects: Dict[str, int]
    timestamp_utc: int
    event_timestamp_utc: str
    camera_name: str
    metadata: Metadata
    event_type: str
    device_id: str
    severity: Severity
    site_alarm_id: str
    """
    severity = detection_event.severity
    if severity is None:
        severity = Severity.LOW
    event_details = {
        "metadata": detection_event.metadata.to_dict(),
        "objects": detection_event.objects,
        "device_id": detection_event.device_id,
        "camera_id": detection_event.camera_id,
        "camera_name": detection_event.camera_name,
        "camera_group_id": detection_event.group_id,
        "group_id": detection_event.group_id,
        "timestamp_utc": detection_event.timestamp_utc.isoformat(),
        "event_timestamp_utc": detection_event.event_timestamp_utc.isoformat(),
        "event_type": detection_event.event_type,
        "severity": severity,
        "id": detection_event.id,
        "site_alarm_id": detection_event.site_alarm_id,
        "alarm_group_id": detection_event.alarm_group_id,
        "is_escalation": detection_event.is_escalation,
        "is_past_event": detection_event.is_past_event,
        "is_dedup": detection_event.is_dedup,
        "tenant_id": detection_event.tenant_id,
        "event_id": detection_event.event_id,
    }
    return event_details


def queue_name_alarm_group_recommendation(
    recommendation: AlarmGroupRecommendation,
) -> str:
    if recommendation == AlarmGroupRecommendation.ANALYZING:
        return "alarm_group_analyzing_queue"
    elif recommendation == AlarmGroupRecommendation.RESOLVE:
        return "alarm_group_resolve"
    elif recommendation == AlarmGroupRecommendation.ESCALATE:
        return "alarm_group_escalate"
    elif recommendation == AlarmGroupRecommendation.ESCALATE_ZERO_TOLERANCE:
        return "alarm_group_escalate_zero_tolerance"
    elif recommendation == AlarmGroupRecommendation.ERROR:
        return "alarm_group_error"
    else:
        # This should not happen if all enum values are covered
        raise ValueError(f"Unknown recommendation: {recommendation}")
