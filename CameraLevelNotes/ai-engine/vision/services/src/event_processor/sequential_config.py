from dataclasses import dataclass, field
from typing import Optional, Set

from common_utils_v1.io_helpers import read_file
from config import backend_config as config

from .models.events import EventType, Severity


@dataclass
class SequentialConsumerConfig:
    queue_url: str
    aws_region: str
    max_messages: int = 1
    wait_time_seconds: int = 5
    visibility_timeout: int = 30
    polling_interval: int = 0  # 0 means continuous polling

    # Event type filters
    allowed_event_types: Set[EventType] = field(default_factory=set)

    # For Detection Events
    allowed_severities: Set[Severity] = field(default_factory=set)

    # AWS Configuration
    aws_access_key_id: Optional[str] = read_file(
        config.HAIE.AWS_ACCESS_KEY_ID, missing=""
    )
    aws_secret_access_key: Optional[str] = read_file(
        config.HAIE.AWS_SECRET_KEY, missing=""
    )
    aws_session_token: Optional[str] = None

    # Error handling
    max_retries: int = 3
    retry_delay: int = 5  # seconds
    dead_letter_queue_url: Optional[str] = None

    # Performance
    batch_size: int = 1

    def __post_init__(self):
        # Convert string event types to enum if needed
        if self.allowed_event_types:
            self.allowed_event_types = {
                EventType(et) if isinstance(et, str) else et
                for et in self.allowed_event_types
            }

        # Convert string severities to enum if needed
        if self.allowed_severities:
            self.allowed_severities = {
                Severity(s) if isinstance(s, str) else s
                for s in self.allowed_severities
            }
