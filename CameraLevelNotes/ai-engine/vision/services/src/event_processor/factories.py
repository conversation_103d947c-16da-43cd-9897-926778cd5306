from typing import Dict, Type

import structlog

from .handlers.base import <PERSON>Handler
from .handlers.event_types import (
    AllocationEvent<PERSON><PERSON><PERSON>,
    AutoResolvedEvent<PERSON>andler,
    DetectionEvent<PERSON>andler,
    Escalation<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>sca<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Get<PERSON>larmEvent<PERSON><PERSON>ler,
    InvestigationEvent<PERSON><PERSON>ler,
    LLMAlarmGroupEventHandler,
    LLMEvent<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON>,
    SafeEventHandler,
    TalkDownEventHandler,
    TwilioCallEventHandler,
    TwilioMessageEventHandler,
)
from .models.events import EventType

logger = structlog.get_logger("hakimo", module="EventHandlerFactory")


class HandlerFactory:
    _event_handlers: Dict[EventType, Type[EventHandler]] = {
        EventType.DETECTIONEVENT: DetectionEventHandler,
        EventType.SAFE: SafeEventHandler,
        EventType.ALLOCATION: AllocationEventHandler,
        EventType.ORPHAN: <PERSON>phan<PERSON><PERSON><PERSON><PERSON><PERSON>,
        EventType.TALKDOWN: TalkDownEventHandler,
        EventType.TWILIO_CALL: Twi<PERSON>Call<PERSON>ventHand<PERSON>,
        EventType.TWILIO_MESSAGE: TwilioMessageEventHandler,
        EventType.ESCALATION_OPEN: EscalationOpenHandler,
        EventType.ESCALATION_CLOSE: EscalationCloseHandler,
        EventType.INVESTIGATION: InvestigationEventHandler,
        EventType.LLMEVENT: LLMEventHandler,
        EventType.LLMALARMGROUPEVENT: LLMAlarmGroupEventHandler,
        EventType.AUTO_RESOLVED: AutoResolvedEventHandler,
        EventType.GET_ALARM: GetAlarmEventHandler,
    }

    @classmethod
    def create_event_handler(cls, event_type: str) -> EventHandler:
        try:
            event_type_str = event_type.get("StringValue").lower()
            event_type_enum = EventType(event_type_str)
            handler_class = cls._event_handlers.get(event_type_enum)
            if handler_class is None:
                raise ValueError(
                    f"No handler found for event type: {event_type}"
                )
            return handler_class()
        except ValueError as e:
            logger.error("Invalid event type", event_type=event_type, error=e)
            raise
