from typing import Any, Dict

import structlog

from .factories import HandlerFactory
from .models.events import (
    AllocationEvent,
    AutoResolvedEvent,
    DetectionEvent,
    EscalationCloseEvent,
    EscalationOpenEvent,
    GetAlarmEvent,
    InvestigationEvent,
    LLMAlarmGroupEvent,
    LLMEvent,
    OrphanEvent,
    SafeEvent,
    TalkDownEvent,
    TwilioCallEvent,
    TwilioMessageEvent,
)

logger = structlog.get_logger("hakimo", module="EventProcessor")


class EventProcessor:
    _event_classes = {
        "DetectionEvent": DetectionEvent,
        "Safe": SafeEvent,
        "Allocation": AllocationEvent,
        "Investigation": InvestigationEvent,
        "Orphan": OrphanEvent,
        "TalkDown": TalkDownEvent,
        "TwilioCall": TwilioCallEvent,
        "TwilioMessage": TwilioMessageEvent,
        "EscalationOpen": EscalationOpenEvent,
        "EscalationClosed": EscalationCloseEvent,
        "LLMEvent": LLMEvent,
        "LLMAlarmGroupEvent": LLMAlarmGroupEvent,
        "AutoResolvedEvent": AutoResolvedEvent,
        "GetAlarmEvent": GetAlarmEvent,
    }

    async def process_event(
        self, body: Dict[str, Any], attributes: Dict[str, Any]
    ) -> None:
        try:
            event_type = attributes.get("eventType")
            if not event_type:
                raise ValueError("Event type not found in message attributes")

            # Get the appropriate event class and create the event object
            event_class = self._event_classes.get(
                event_type.get("StringValue")
            )
            if not event_class:
                raise ValueError(f"Unknown event type: {event_type}")

            event = event_class.from_dict(body)

            # Create and execute appropriate handler
            handler = HandlerFactory.create_event_handler(event_type)
            await handler.handle(event)

        except Exception as e:
            logger.error(
                "Error processing event",
                error=e,
                event_type=attributes.get("eventType"),
            )
            # Just re-raise the exception to be caught by AsyncSQSConsumer
            raise

    def process_event_sync(
        self, body: Dict[str, Any], attributes: Dict[str, Any]
    ) -> None:
        try:
            event_type = attributes.get("eventType")
            if not event_type:
                raise ValueError("Event type not found in message attributes")

            # Get the appropriate event class and create the event object
            event_class = self._event_classes.get(
                event_type.get("StringValue")
            )
            if not event_class:
                raise ValueError(f"Unknown event type: {event_type}")

            event = event_class.from_dict(body)

            # Create and execute appropriate handler
            handler = HandlerFactory.create_event_handler(event_type)
            handler.handle_sync(event)

        except Exception as e:
            logger.error(
                "Error processing event",
                error=e,
                event_type=attributes.get("eventType"),
            )
            # Just re-raise the exception to be caught by AsyncSQSConsumer
            raise
