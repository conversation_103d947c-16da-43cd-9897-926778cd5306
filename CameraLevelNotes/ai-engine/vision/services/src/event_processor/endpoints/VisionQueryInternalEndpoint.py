from http import HTTPStatus

import redis
import structlog
from flask import jsonify, request

from common_utils_v1.vision_metrics_definitions import (
    VISION_ALARM_GROUPS_RETRIEVED_TOTAL,
    VISION_CAMERA_LOOKUPS_TOTAL,
)
from db_controller import DBControllerV1
from vision.services.src.event_processor.endpoints.decorators import (
    track_metrics,
)

logger = structlog.get_logger(
    "hakimo", module="vision_http_server_vision_query_internal_endpoint"
)


class VisionQueryInternalEndpoint:
    def __init__(self, controller: DBControllerV1, redis_client: redis.Redis):
        self._ctrl_map = controller
        self._redis_client = redis_client

    @track_metrics("get_camera_info")
    def get_camera_info(self, camera_id):
        """Get camera info for a given camera id"""
        camera_info = self._ctrl_map.camera.get_camera_by_id_without_cache(
            camera_id
        )
        if camera_info:
            # Track successful camera lookup
            VISION_CAMERA_LOOKUPS_TOTAL.labels(success="true").inc()
            # Convert Row object to dictionary
            camera_dict = dict(camera_info._mapping)
            return jsonify(camera_dict)
        else:
            # Track failed camera lookup
            VISION_CAMERA_LOOKUPS_TOTAL.labels(success="false").inc()
            return (
                jsonify({"error": "Camera not found"}),
                HTTPStatus.NOT_FOUND,
            )

    @track_metrics("get_pending_alarms_for_camera_group")
    def get_pending_alarms_for_camera_group(self, camera_group_id):
        """Get pending alarms for a camera group"""
        try:
            alarm_group_controller = self._ctrl_map.alarm_group
            event_datas = alarm_group_controller.get_active_alarm_groups(
                [camera_group_id]
            )
            # Track alarm group retrieval
            VISION_ALARM_GROUPS_RETRIEVED_TOTAL.labels(
                camera_group_id=camera_group_id
            ).inc()
            return jsonify({"status": "ok", "event_datas": event_datas})
        except Exception as e:
            logger.error(
                "Error getting pending alarms for camera group",
                error=str(e),
                camera_group_id=camera_group_id,
            )
            return (
                jsonify({"error": "Internal server error", "details": str(e)}),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    @track_metrics("get_alarm_group_list")
    def get_alarm_group_list(self):
        """Get the list of alarm groups"""
        try:
            data = request.get_json()
            if not data or "camera_group_ids" not in data:
                return (
                    jsonify(
                        {
                            "error": "camera_group_ids is required in request body"
                        }
                    ),
                    HTTPStatus.BAD_REQUEST,
                )

            camera_group_ids = data["camera_group_ids"]

            if not isinstance(camera_group_ids, list):
                return (
                    jsonify({"error": "camera_group_ids must be a list"}),
                    HTTPStatus.BAD_REQUEST,
                )

            alarm_group_controller = self._ctrl_map.alarm_group
            event_datas = alarm_group_controller.get_active_alarm_groups(
                camera_group_ids
            )
            # Track alarm groups retrieved for each camera group
            for camera_group_id in camera_group_ids:
                VISION_ALARM_GROUPS_RETRIEVED_TOTAL.labels(
                    camera_group_id=camera_group_id
                ).inc()
            return jsonify({"status": "ok", "event_datas": event_datas})
        except Exception as e:
            logger.error("Error getting alarm group list", error=str(e))
            return (
                jsonify({"error": "Internal server error"}),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    @track_metrics("get_alarm_group_event")
    def get_alarm_group_event(self, alarm_group_id):
        """Get the event for an alarm group"""
        try:
            limit = request.args.get("limit", 10)
            event_controller = self._ctrl_map.event
            events = event_controller.get_all_events_for_alarm_group(
                alarm_group_id, limit=limit, order_by="desc"
            )
            event_detail_list = [event.event_details for event in events]
            return jsonify(
                {"status": "ok", "event_detail_list": event_detail_list}
            )
        except Exception as e:
            logger.error("Error getting alarm group event", error=str(e))
            return (
                jsonify({"error": "Internal server error"}),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    def get_operator_queue_count(self):
        try:
            data = request.get_json()
            operator_id = data.get("operator_id")
            tenant_ids = data.get("tenant_ids")

            if not operator_id or not tenant_ids:
                return (
                    jsonify({"error": "Missing operator_id or tenant_ids"}),
                    HTTPStatus.BAD_REQUEST,
                )

            alarm_group_controller = self._ctrl_map.alarm_group
            total_open_count = (
                alarm_group_controller.get_total_pending_alarm_group_count(
                    tenant_ids
                )
            )
            total_escalation_open_count = (
                alarm_group_controller.get_total_escalation_open_count(
                    tenant_ids
                )
            )
            total_operator_allocation_count = (
                alarm_group_controller.get_total_operator_allocation_count(
                    operator_id
                )
            )
            total_unallocation_count = (
                alarm_group_controller.get_total_unallocation_count(
                    operator_id, tenant_ids
                )
            )
            return jsonify(
                {
                    "total_open_count": total_open_count,
                    "total_escalation_open_count": total_escalation_open_count,
                    "total_operator_allocation_count": total_operator_allocation_count,
                    "total_unallocation_count": total_unallocation_count,
                }
            )
        except Exception as e:
            logger.error("Failed to fetch operator queue count", error=str(e))
            return (
                jsonify({"error": "Internal server error", "details": str(e)}),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    def get_operator_queue_count_v1(self):
        """Get the operator queue count"""
        try:
            data = request.get_json()
            operator_id = data.get("operator_id")
            tenant_ids = data.get("tenant_ids")

            if not operator_id or not tenant_ids:
                return (
                    jsonify({"error": "Missing operator_id or tenant_ids"}),
                    400,
                )

            alarm_group_controller = self._ctrl_map.alarm_group
            total_open_count = (
                alarm_group_controller.get_total_pending_alarm_group_count(
                    tenant_ids
                )
            )
            total_escalation_open_count = (
                alarm_group_controller.get_total_escalation_open_count(
                    tenant_ids
                )
            )
            total_operator_allocation_count = (
                alarm_group_controller.get_total_operator_allocation_count(
                    operator_id
                )
            )
            total_unallocation_count = (
                alarm_group_controller.get_total_unallocation_count(
                    operator_id, tenant_ids
                )
            )
            return jsonify(
                {
                    "total_open_count": total_open_count,
                    "total_escalation_open_count": total_escalation_open_count,
                    "total_operator_allocation_count": total_operator_allocation_count,
                    "total_unallocation_count": total_unallocation_count,
                }
            )
        except Exception as e:
            logger.error("Failed to fetch operator queue count", error=str(e))
            return (
                jsonify({"error": "Internal server error", "details": str(e)}),
                500,
            )
