from datetime import datetime, timedelta, timezone
from http import H<PERSON><PERSON><PERSON><PERSON>
from typing import Any, Dict, List, Optional

import redis
import structlog
from dateutil import parser
from flask import Blueprint, jsonify

from common_utils_v1.common import get_user_tenant_ids
from common_utils_v1.guard_endpoint import guard_endpoint
from common_utils_v1.s3_utils import (
    S3_BUCKET_NAME_KEY,
    S3_FILE_NAME_KEY,
    get_s3_path_details,
    get_signed_s3_url,
)
from db_controller import DBControllerV1
from db_controller.alarm_group.alarm_group_filters import AlarmGroupFilters
from models_rds.alarm_group import AlarmGroup
from models_rds.alarm_group_update import AlarmGroupUpdate
from models_rds.event import Event
from models_rds.users import Users
from vision.services.src.event_processor.utils.constant import QUERY_SEPARATOR

logger = structlog.get_logger(
    "hakimo", module="vision_http_server_vision_query_authenticated_endpoint"
)


class VisionQueryAuthenticatedEndpoint:
    def __init__(self, controller: DBControllerV1, redis_client: redis.Redis):
        self.api = Blueprint(
            "vision_http_server_vision_query_authenticated_endpoint", __name__
        )
        self._ctrl_map = controller
        self._redis_client = redis_client

    @guard_endpoint(["location_alarm/detail:view"])
    def get_alarm_groups(
        self,
        user: Optional[Users] = None,
        query_params: Optional[Dict] = None,
    ):
        """
        GET /alarm_groups?...
        """
        assert user is not None
        try:
            filters = _unmarshal_get_alarm_groups_query(query_params)
        except ValueError as ve:
            return self._create_response(
                [], HTTPStatus.BAD_REQUEST.value, str(ve)
            )
        except Exception as e:
            logger.exception("unmarshal query error")
            return self._create_response(
                [],
                HTTPStatus.INTERNAL_SERVER_ERROR.value,
                f"Error parsing query: {e}",
            )
        user_vision_tenant_ids = get_user_tenant_ids(
            user=user,
            include_vision_tenants=True,
            include_msp_tenants=False,
        )

        # make sure that filters.tenant_ids is a subset of user_vision_tenant_ids
        if filters.tenant_ids:
            updated_tenant_ids = [
                tenant_id
                for tenant_id in filters.tenant_ids
                if tenant_id in user_vision_tenant_ids
            ]
            filters.tenant_ids = updated_tenant_ids
        else:
            filters.tenant_ids = user_vision_tenant_ids

        try:
            # get alarm groups
            alarm_groups = self._ctrl_map.alarm_group.get_alarm_groups(filters)

            # get event counts for the alarm groups
            alarm_group_ids = [ag.id for ag in alarm_groups]
            event_counts = (
                self._ctrl_map.alarm_group.get_event_counts_for_alarm_groups(
                    alarm_group_ids
                )
            )

            # Combine alarm groups with their event counts
            rows = [(ag, event_counts.get(ag.id, 0)) for ag in alarm_groups]

        except ValueError as ve:
            logger.warning(
                "Invalid parameters for get_alarm_groups", error=str(ve)
            )
            return self._create_response(
                [],
                HTTPStatus.BAD_REQUEST.value,
                f"Invalid parameters: {ve}",
            )
        except Exception:
            logger.exception("controller.get_alarm_groups failed")
            return self._create_response(
                [],
                HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "internal server error",
            )

        # 3) marshal
        try:
            alarm_group_items = _marshal_get_alarm_groups_response(rows)
        except Exception:
            logger.exception("marshal response error")
            return self._create_response(
                [],
                HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "Error formatting response",
            )

        payload = {
            "items": alarm_group_items,
            "items_count": len(alarm_group_items),
            "total": 1000,
            "page": filters.page,
            "page_size": filters.page_size,
        }

        return self._create_response(payload, HTTPStatus.OK.value, "success")

    @guard_endpoint(["location_alarm/detail:view"])
    def get_alarm_group_details(
        self,
        alarm_group_id: str,
        user: Optional[Users] = None,
    ):
        """
        GET /alarm_groups/{alarm_group_id}
        """
        assert user is not None
        try:
            # get alarm group by ID
            alarm_group = self._ctrl_map.alarm_group.get_alarm_group_by_id(
                alarm_group_id
            )

            if not alarm_group:
                return self._create_response(
                    None,
                    HTTPStatus.NOT_FOUND.value,
                    "Alarm group not found",
                )

            user_vision_tenant_ids = get_user_tenant_ids(
                user=user,
                include_vision_tenants=True,
                include_msp_tenants=False,
            )

            if alarm_group.tenant_id not in user_vision_tenant_ids:
                return self._create_response(
                    None,
                    HTTPStatus.FORBIDDEN.value,
                    "You are not authorized to access this alarm group",
                )

            # get events for this alarm group
            events = self._ctrl_map.alarm_group.get_alarm_group_events(
                alarm_group_id
            )

            # calculate event count in endpoint
            event_count = len(events)

        except ValueError as ve:
            logger.warning(
                "Invalid parameters for get_alarm_group_details",
                error=str(ve),
            )
            return self._create_response(
                None,
                HTTPStatus.BAD_REQUEST.value,
                f"Invalid parameters: {ve}",
            )
        except Exception:
            logger.exception("controller.get_alarm_group_details failed")
            return self._create_response(
                None,
                HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "internal server error",
            )

        # marshal response
        try:
            payload = _marshal_get_alarm_group_details_response(
                alarm_group, events, event_count
            )
        except Exception:
            logger.exception("marshal response error")
            return self._create_response(
                None,
                HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "Error formatting response",
            )

        return self._create_response(payload, HTTPStatus.OK.value, "success")

    @guard_endpoint(["location_alarm/detail:view"])
    def get_alarm_group_updates(
        self,
        alarm_group_id: str = None,
        user: Optional[Users] = None,
    ):
        """
        GET /alarm_group_updates/{alarm_group_id}
        """
        assert user is not None
        try:
            alarm_group_details = (
                self._ctrl_map.alarm_group.get_alarm_group_by_id(
                    alarm_group_id
                )
            )
            if not alarm_group_details:
                return self._create_response(
                    [],
                    HTTPStatus.NOT_FOUND.value,
                    "Alarm group not found",
                )

            user_vision_tenant_ids = get_user_tenant_ids(
                user=user,
                include_vision_tenants=True,
                include_msp_tenants=False,
            )

            if alarm_group_details.tenant_id not in user_vision_tenant_ids:
                return self._create_response(
                    [],
                    HTTPStatus.FORBIDDEN.value,
                    "You are not authorized to access this alarm group",
                )

            # get alarm group updates for the given alarm group ID
            updates = (
                self._ctrl_map.alarm_group.get_updates_for_alarm_group_id(
                    alarm_group_id
                )
            )

        except Exception:
            logger.exception(
                "controller.get_updates_for_alarm_group_id failed"
            )
            return self._create_response(
                [],
                HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "internal server error",
            )

        # marshal response
        try:
            update_items = _marshal_alarm_group_updates_response(updates)
        except Exception:
            logger.exception("marshal response error")
            return self._create_response(
                [],
                HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "Error formatting response",
            )

        return self._create_response(
            update_items, HTTPStatus.OK.value, "success"
        )

    def _create_response(self, payload: Any, status: int, message: str):
        """
        Centralized method to create JSON responses.

        Args:
            payload: The response payload
            status: HTTP status code
            message: Response message

        Returns:
            Flask response tuple (jsonify_response, status_code)
        """
        return (
            jsonify(
                {
                    "payload": payload,
                    "status": status,
                    "message": message,
                }
            ),
            status,
        )


def _split_param(raw_value: Optional[str]) -> list[str]:
    """
    Split a raw value on '^^^', strip out empties.
    """
    if not raw_value or not raw_value.strip():
        return []
    return [s.strip() for s in raw_value.split(QUERY_SEPARATOR) if s.strip()]


def _unmarshal_get_alarm_groups_query(
    query_dict: Optional[Dict] = None,
) -> AlarmGroupFilters:
    """
    Parse & validate all query params, raise ValueError on bad input.
    Returns AlarmGroupFilters object suitable for passing straight into controller.get_alarm_groups.
    """
    # pagination
    try:
        page = int(query_dict.get("page", 0))
        page_size = int(query_dict.get("pageSize", 20))
    except ValueError:
        raise ValueError("`page` and `pageSize` must be integers")

    # list filters
    tenant_ids_raw = query_dict.get("tenantIds")
    camera_group_ids_raw = query_dict.get("cameraGroupIds")
    states_raw = query_dict.get("state")
    resolutions_raw = query_dict.get("resolution")

    tenant_ids = _split_param(tenant_ids_raw) if tenant_ids_raw else []
    camera_group_ids = (
        _split_param(camera_group_ids_raw) if camera_group_ids_raw else []
    )
    states = (
        [s.lower() for s in _split_param(states_raw)] if states_raw else []
    )
    resolutions = (
        [r.lower() for r in _split_param(resolutions_raw)]
        if resolutions_raw
        else []
    )

    # date filters
    date_from = query_dict.get("dateFrom")
    date_to = query_dict.get("dateTo")
    rel = query_dict.get("relativeTimeMinutes")

    dt_from = dt_to = None
    if date_from or date_to:
        try:
            if date_from:
                dt_from = parser.isoparse(date_from)
            if date_to:
                dt_to = parser.isoparse(date_to)
        except (ValueError, TypeError):
            raise ValueError(
                "`dateFrom`/`dateTo` must be valid ISO timestamps"
            )
    elif rel:
        try:
            mins = int(rel)
        except ValueError:
            raise ValueError("`relativeTimeMinutes` must be an integer")
        dt_from = datetime.now(timezone.utc) - timedelta(minutes=mins)

    return AlarmGroupFilters(
        tenant_ids=tenant_ids,
        camera_group_ids=camera_group_ids,
        states=states,
        resolutions=resolutions,
        date_from=dt_from,
        date_to=dt_to,
        page=page,
        page_size=page_size,
    )


def _marshal_alarm_group_to_dict(alarm_group: AlarmGroup) -> Dict[str, Any]:
    """
    Convert a single AlarmGroup object to a JSON-serializable dict.
    """
    return {
        "id": alarm_group.id,
        "severity": alarm_group.severity,
        "state": alarm_group.state,
        "resolution": alarm_group.resolution,
        "resolution_comment": alarm_group.resolution_comment,
        "start_time_utc": (
            alarm_group.start_time_utc.isoformat()
            if alarm_group.start_time_utc
            else None
        ),
        "end_time_utc": (
            alarm_group.end_time_utc.isoformat()
            if alarm_group.end_time_utc
            else None
        ),
        "created_at_utc": alarm_group.created_at_utc.isoformat(),
        "camera_group_id": alarm_group.camera_group_id,
        "tenant_id": alarm_group.tenant_id,
        "operator_id": alarm_group.operator_id,
    }


def _marshal_get_alarm_groups_response(rows: list[tuple]) -> list[dict]:
    """
    Turn [(AlarmGroup, event_count), …] into JSON-serializable dicts.
    """
    out: list[dict] = []
    for ag, cnt in rows:
        alarm_group_dict = _marshal_alarm_group_to_dict(ag)
        alarm_group_dict["events_count"] = cnt
        out.append(alarm_group_dict)
    return out


def modify_event_details(event_details: Dict[str, Any]) -> Dict[str, Any]:
    metadata = event_details.get("metadata")
    metadata_frames = metadata.get("frames") if metadata else []
    frames = []
    for frame in metadata_frames:
        frame_image_url = frame[0]
        s3_details = get_s3_path_details(frame_image_url)
        signed_url = get_signed_s3_url(
            s3_details[S3_BUCKET_NAME_KEY],
            s3_details[S3_FILE_NAME_KEY],
            expiration_secs=300,
        )
        frame_timestamp = frame[1]
        frames.append(
            {
                "image_url": signed_url,
                "timestamp": frame_timestamp,
            }
        )
    ret = {
        "camera_name": event_details.get("camera_name", ""),
        "device_id": event_details.get("device_id", ""),
        "frames": frames,
    }

    return ret


def _marshal_get_alarm_group_details_response(
    alarm_group: AlarmGroup, events: List[Event], event_count: int
) -> Dict[str, Any]:
    """
    Turn AlarmGroup and List[Event] into JSON-serializable dict.
    """
    # Format alarm group
    alarm_group_data = {
        "id": alarm_group.id,
        "severity": alarm_group.severity,
        "state": alarm_group.state,
        "resolution": alarm_group.resolution,
        "resolution_comment": alarm_group.resolution_comment,
        "start_time_utc": (
            alarm_group.start_time_utc.isoformat()
            if alarm_group.start_time_utc
            else None
        ),
        "end_time_utc": (
            alarm_group.end_time_utc.isoformat()
            if alarm_group.end_time_utc
            else None
        ),
        "created_at_utc": alarm_group.created_at_utc.isoformat(),
        "camera_group_id": alarm_group.camera_group_id,
        "tenant_id": alarm_group.tenant_id,
        "operator_id": alarm_group.operator_id,
        "events_count": event_count,
    }

    # Format events
    events_data = []
    for event in events:
        event_details = modify_event_details(event.event_details)
        event_data = {
            "id": event.id,
            "severity": event.severity,
            "event_time_utc": (
                event.event_time_utc.isoformat()
                if event.event_time_utc
                else None
            ),
            **event_details,
            "tenant_id": event.tenant_id,
            "camera_group_id": event.camera_group_id,
            "camera_id": event.camera_id,
            "alarm_group_id": event.alarm_group_id,
        }
        events_data.append(event_data)

    alarm_group_data["events"] = events_data

    return alarm_group_data


def _marshal_alarm_group_updates_response(
    updates: List[AlarmGroupUpdate],
) -> List[Dict[str, Any]]:
    """
    Convert a list of AlarmGroupUpdate objects to JSON-serializable dicts.
    """
    update_items = []
    for update in updates:
        update_data = {
            "id": update.id,
            "alarm_group_id": update.alarm_group_id,
            "event_type": update.event_type,
            "event_details": update.event_details,
            "created_at_utc": update.created_at_utc.isoformat(),
        }
        update_items.append(update_data)
    return update_items
