import time
from functools import wraps

import structlog
from flask import request

from common_utils_v1.vision_metrics_definitions import (
    VISION_HTTP_ERRORS_TOTAL,
    VISION_HTTP_REQUEST_DURATION,
    VISION_HTTP_REQUESTS_TOTAL,
)

logger = structlog.get_logger(
    "hakimo", module="http_server_event_processor_decorator"
)


def track_metrics(endpoint_name):
    """Decorator to track HTTP metrics for API endpoints"""

    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            method = request.method
            status_code = 200
            error_type = None

            try:
                result = f(*args, **kwargs)
                if isinstance(result, tuple):
                    response, status_code = result
                    return result
                else:
                    return result
            except Exception as e:
                status_code = 500
                error_type = type(e).__name__
                VISION_HTTP_ERRORS_TOTAL.labels(
                    method=method,
                    endpoint=endpoint_name,
                    error_type=error_type,
                ).inc()
                logger.error(f"Error in {endpoint_name}", error=str(e))
                raise
            finally:
                # Record request duration
                duration = time.time() - start_time
                VISION_HTTP_REQUEST_DURATION.labels(
                    method=method, endpoint=endpoint_name
                ).observe(duration)

                # Record request count
                VISION_HTTP_REQUESTS_TOTAL.labels(
                    method=method,
                    endpoint=endpoint_name,
                    status_code=str(status_code),
                ).inc()

        return wrapper

    return decorator
