import time
from http import HTTPStatus

import redis
import structlog
from flask import jsonify, request

from common_utils_v1.vision_metrics_definitions import (
    VISION_ALARM_GROUPS_RETRIEVED_TOTAL,
)
from config import backend_config as config
from db_controller import DBControllerV1
from models_rds.alarm_group import AlarmGroup
from vision.services.src.event_processor.endpoints.decorators import (
    track_metrics,
)
from vision.services.src.event_processor.handlers.event_types import (
    add_event_redis,
)
from vision.services.src.event_processor.models.events import (
    EventState,
    Resolution,
    Severity,
)
from vision.services.src.event_processor.publisher.sync_sqs_publisher import (
    SyncSQSPublisher,
)
from vision.services.src.event_processor.publisher_config import (
    PublisherConfig,
)

logger = structlog.get_logger(
    "hakimo", module="vision_http_server_vision_command_internal_endpoint"
)


class VisionCommandInternalEndpoint:
    def __init__(self, controller: DBControllerV1, redis_client: redis.Redis):
        self._ctrl_map = controller
        self._redis_client = redis_client
        self._sqs_queue_url = config.HAIE.SCAN_ALARM_TRANSACTION_QUEUE_URL
        self._sqs_publisher_config = PublisherConfig(
            queue_url=self._sqs_queue_url,
            aws_region="us-west-2",
            content_based_deduplication=True,
        )
        self._sqs_publisher = SyncSQSPublisher(self._sqs_publisher_config)

    @track_metrics("add_pending_alarms_to_set")
    def add_pending_alarms_to_set(self):
        """Add pending alarms to a set"""
        try:
            data = request.get_json()
            tenant_ids = data["tenant_ids"]
            limit = data.get("limit", 1)
            order = data.get("order", "asc")
            pipeline = self._redis_client.pipeline()
            alarm_group_controller = self._ctrl_map.alarm_group
            camera_group_ids_with_start_time = (
                alarm_group_controller.get_pending_alarm_groups_for_tenants(
                    tenant_ids, limit, order
                )
            )
            for (
                camera_group_id,
                start_time,
            ) in camera_group_ids_with_start_time:
                # Convert datetime to timestamp (float) for Redis score
                timestamp_score = (
                    start_time.timestamp() if start_time else time.time()
                )
                pipeline.zadd(
                    "pending_camera_groups_ordered",
                    {camera_group_id: timestamp_score},
                )
                # Track alarm groups added to set
                VISION_ALARM_GROUPS_RETRIEVED_TOTAL.labels(
                    camera_group_id=camera_group_id
                ).inc()
            pipeline.execute()
            return jsonify(
                {"status": "ok", "alarm_set": "pending_camera_groups_ordered"}
            )
        except Exception as e:
            logger.error("Error adding pending alarms to set", error=str(e))
            return (
                jsonify({"error": "Internal server error", "details": str(e)}),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    @track_metrics("add_pending_alarms_to_set_v1")
    def add_pending_alarms_to_set_v1(self):
        """Add pending alarms to a set"""
        try:
            data = request.get_json()
            tenant_ids = data["tenant_ids"]
            limit = data.get("limit", 1)
            order = data.get("order", "asc")
            pipeline = self._redis_client.pipeline()
            alarm_group_controller = self._ctrl_map.alarm_group
            camera_group_ids_with_start_time = (
                alarm_group_controller.get_pending_alarm_groups_for_tenants_v1(
                    tenant_ids, limit, order
                )
            )
            for (
                camera_group_id,
                start_time,
            ) in camera_group_ids_with_start_time:
                # Convert datetime to timestamp (float) for Redis score
                timestamp_score = (
                    start_time.timestamp() if start_time else time.time()
                )
                pipeline.zadd(
                    "pending_camera_groups_ordered",
                    {camera_group_id: timestamp_score},
                )
                # Track alarm groups added to set
                VISION_ALARM_GROUPS_RETRIEVED_TOTAL.labels(
                    camera_group_id=camera_group_id
                ).inc()
            pipeline.execute()
            return jsonify(
                {"status": "ok", "alarm_set": "pending_camera_groups_ordered"}
            )
        except Exception as e:
            logger.error("Error adding pending alarms to set", error=str(e))
            return (
                jsonify({"error": "Internal server error", "details": str(e)}),
                500,
            )

    @track_metrics("add_unallocated_alarms_to_set")
    def add_unallocated_alarms_to_set(self):
        """Add unallocated or orphan alarm groups to a sorted set"""
        try:
            data = request.get_json()
            tenant_ids = data["tenant_ids"]
            limit = data.get("limit", 1)
            order = data.get("order", "asc")
            recommendation = data.get("recommendation", None)
            if not tenant_ids or not isinstance(tenant_ids, list):
                return (
                    jsonify({"error": "tenant_ids must be a non-empty list"}),
                    400,
                )
            message_attributes = {
                "eventType": {
                    "DataType": "String",
                    "StringValue": "GetAlarmEvent",
                },
            }
            self._sqs_publisher.publish_message(
                {
                    "tenant_ids": tenant_ids,
                    "limit": limit,
                    "order": order,
                    "recommendation": recommendation,
                    "event_type": "GetAlarmEvent",
                    "site_alarm_id": "8f696caf-e427-421f-b44e-e39992b3a63f",
                    "group_id": "3238cf09-a4d0-46fd-8dba-920883c33137",
                    "timestamp_utc": time.time(),
                    "operator_id": "<EMAIL>",
                    "tenant_id": "eisley-test",
                },
                message_attributes=message_attributes,
                message_group_id="default",
            )
            queue_names = set()
            queue_names.add("sequential_alarm_group_queue")
            return jsonify({"status": "ok", "alarm_sets": list(queue_names)})

            # pipeline = self._redis_client.pipeline()
            # alarm_group_controller = self._ctrl_map.alarm_group
            # alarm_groups_data = (
            #     alarm_group_controller.get_unallocated_or_orphan_alarm_groups(
            #         tenant_ids, limit, order, recommendation
            #     )
            # )

            # queue_names = set()

            # for alarm_group in alarm_groups_data:
            #     # Convert datetime to timestamp (float) for Redis score
            #     timestamp_score = (
            #         alarm_group["start_timestamp_utc"].timestamp()
            #         if alarm_group["start_timestamp_utc"]
            #         else time.time()
            #     )

            #     # Serialize the full alarm group data for storage
            #     alarm_group_json = {
            #         "alarm_group_id": alarm_group["alarm_group_id"],
            #         "tenant_id": alarm_group["tenant_id"],
            #         "camera_group_id": alarm_group["camera_group_id"],
            #         "start_timestamp_utc": timestamp_score,  # Store as timestamp for consistency
            #     }
            #     queue_name = "single_alarm_group_queue"

            #     pipeline.zadd(
            #         queue_name,
            #         {json.dumps(alarm_group_json): timestamp_score},
            #     )
            #     # Track alarm groups added to set
            #     VISION_ALARM_GROUPS_RETRIEVED_TOTAL.labels(
            #         camera_group_id=alarm_group["camera_group_id"]
            #     ).inc()
            #     queue_names.add(queue_name)
            #     pipeline.sadd("alarm_group_sets", queue_name)

            # pipeline.execute()
            # return jsonify({"status": "ok", "alarm_sets": list(queue_names)})
        except Exception as e:
            logger.error(
                "Error adding unallocated alarms to set", error=str(e)
            )
            return (
                jsonify({"error": "Internal server error", "details": str(e)}),
                500,
            )

    @track_metrics("get_alarm_group_list_partial_allocation")
    def get_alarm_group_list_partial_allocation(self):
        """Get the list of alarm groups for partial allocation"""
        try:
            data = request.get_json()
            if not data or "operator_id" not in data:
                return (
                    jsonify(
                        {"error": "operator_id is required in request body"}
                    ),
                    400,
                )
            operator_id = data["operator_id"]
            if not isinstance(operator_id, str):
                return jsonify({"error": "operator_id must be a string"}), 400
            alarm_group_controller = self._ctrl_map.alarm_group
            event_datas = alarm_group_controller.get_unallocated_alarm_groups_for_operator_camera_groups(
                operator_id
            )
            return jsonify({"status": "ok", "event_datas": event_datas})
        except Exception as e:
            logger.error("Error getting alarm group list", error=str(e))
            return jsonify({"error": "Internal server error"}), 500

    def create_alarm_group(self, tenant_id, camera_group_id, severity):
        """Create an alarm group with the provided data"""
        try:
            data = request.get_json()

            # Validate required fields
            required_fields = ["timestamp_utc"]
            for field in required_fields:
                if field not in data:
                    return (
                        jsonify({"error": f"Missing required field: {field}"}),
                        HTTPStatus.BAD_REQUEST,
                    )

            key = f"alarm_group:{camera_group_id}:{tenant_id}:{severity}"

            # Fetch the alarm_group_id from Redis
            alarm_group_id = self._redis_client.hget(key, "alarm_group_id")

            logger.info(
                "Received create alarm group request",
                tenant_id=tenant_id,
                severity=severity,
                timestamp_utc=data["timestamp_utc"],
                camera_group_id=camera_group_id,
            )

            if alarm_group_id:
                logger.info(
                    "Retrieved alarm group ID",
                    tenant_id=tenant_id,
                    severity=severity,
                    camera_group_id=camera_group_id,
                    alarm_group_id=alarm_group_id,
                )
                return jsonify(
                    {"status": "ok", "alarm_group_id": alarm_group_id}
                )
            else:
                logger.warning(
                    "Alarm group ID not found in Redis",
                    tenant_id=tenant_id,
                    severity=severity,
                    camera_group_id=camera_group_id,
                )
                alarm_group_controller = self._ctrl_map.alarm_group

                # Convert timestamp from milliseconds to datetime
                from datetime import datetime

                try:
                    # Convert the timestamp (in milliseconds) to a datetime object
                    timestamp_ms = int(data["timestamp_utc"])
                    timestamp_datetime = datetime.fromtimestamp(
                        timestamp_ms / 1000
                    )
                except (ValueError, TypeError) as e:
                    logger.error(
                        "Failed to convert timestamp",
                        tenant_id=tenant_id,
                        severity=severity,
                        camera_group_id=camera_group_id,
                        timestamp=data["timestamp_utc"],
                        error=str(e),
                    )
                    return (
                        jsonify(
                            {
                                "error": f"Invalid timestamp format: {data['timestamp_utc']}"
                            }
                        ),
                        HTTPStatus.BAD_REQUEST,
                    )

                alarm_group = AlarmGroup(
                    id=alarm_group_id,
                    severity=severity,
                    camera_group_id=camera_group_id,
                    tenant_id=tenant_id,
                    start_time_utc=timestamp_datetime,
                    state=EventState.PENDING,
                    resolution=Resolution.OPEN,
                )
                try:
                    alarm_group_stored_id, created, severity = (
                        alarm_group_controller.create_alarm_group(alarm_group)
                    )
                    if severity == Severity.HIGH:
                        add_event_redis(
                            alarm_group_stored_id,
                            Severity.HIGH,
                            tenant_id,
                            camera_group_id,
                        )
                    else:
                        add_event_redis(
                            alarm_group_stored_id,
                            Severity.LOW,
                            tenant_id,
                            camera_group_id,
                        )
                    return (
                        jsonify(
                            {
                                "status": "ok",
                                "alarm_group_id": alarm_group_stored_id,
                            }
                        ),
                        HTTPStatus.OK,
                    )
                except Exception as e:
                    logger.error(
                        "Failed to create alarm group in database",
                        tenant_id=tenant_id,
                        severity=severity,
                        camera_group_id=camera_group_id,
                        error=str(e),
                    )
                    return (
                        jsonify(
                            {
                                "error": "Failed to create alarm group in database",
                                "details": str(e),
                            }
                        ),
                        HTTPStatus.INTERNAL_SERVER_ERROR,
                    )
        except redis.RedisError as e:
            logger.error(
                "Redis connection error",
                tenant_id=tenant_id,
                severity=severity,
                camera_group_id=camera_group_id,
                error=str(e),
            )
            return (
                jsonify(
                    {"error": "Redis connection error", "details": str(e)}
                ),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            logger.error(
                "Unexpected error in create_alarm_group",
                tenant_id=tenant_id,
                severity=severity,
                camera_group_id=camera_group_id,
                error=str(e),
            )
            return (
                jsonify({"error": "Internal server error", "details": str(e)}),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
