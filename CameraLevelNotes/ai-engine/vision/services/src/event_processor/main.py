from .config import ConsumerConfig
from .consumer.sqs import SQSConsumer
from .processor import EventProcessor


def main():
    config = ConsumerConfig(
        queue_url="https://sqs.us-west-2.amazonaws.com/695273141991/scan_get_alarm_transactions_staging.fifo",
        aws_region="us-west-2",
    )

    processor = EventProcessor()
    consumer = SQSConsumer(config, processor)

    try:
        consumer.start()
    except KeyboardInterrupt:
        consumer.stop()


if __name__ == "__main__":
    main()
