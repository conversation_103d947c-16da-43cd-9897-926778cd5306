import json
import time
from typing import Any, Dict, Optional

import boto3
import structlog

from ..models.events import EventType
from ..processor import EventProcessor
from ..sequential_config import SequentialConsumerConfig

logger = structlog.get_logger("hakimo", module="SQSConsumer")


class SQSConsumer:
    time_start = 0
    time_end = 0

    def __init__(
        self,
        config: SequentialConsumerConfig,
        processor: EventProcessor,
    ):
        self.config = config
        self.processor = processor
        self.running = False

        # Create boto3 session and client
        self.session = boto3.Session(
            aws_access_key_id=config.aws_access_key_id,
            aws_secret_access_key=config.aws_secret_access_key,
            aws_session_token=config.aws_session_token,
            region_name=config.aws_region,
        )
        self.sqs = self.session.client("sqs")

    def start(self) -> None:
        """Start consuming messages from SQS"""
        logger.info("starting_sqs_consumer", queue_url=self.config.queue_url)
        self.running = True

        try:
            while self.running:
                try:
                    self._process_message_batch()

                    if self.config.polling_interval > 0:
                        time.sleep(self.config.polling_interval)

                except Exception as e:
                    logger.error(
                        "error_processing_message_batch", error=str(e)
                    )
                    time.sleep(self.config.retry_delay)

        except Exception as e:
            logger.error("fatal_error_in_sqs_consumer", error=str(e))
            raise

    def _process_message_batch(self) -> None:
        """Process a batch of messages from SQS sequentially"""
        try:
            self.time_start = time.time()
            response = self.sqs.receive_message(
                QueueUrl=self.config.queue_url,
                MaxNumberOfMessages=self.config.batch_size,
                WaitTimeSeconds=self.config.wait_time_seconds,
                AttributeNames=["All"],
                MessageAttributeNames=["All"],
            )

            messages = response.get("Messages", [])
            if not messages:
                return

            # Process messages sequentially
            for message in messages:
                # if self._should_process_message(message):
                try:
                    self._process_single_message(message)
                except Exception as e:
                    logger.error("error_in_message_processing", error=str(e))
        # else:
        #     self._delete_message(message["ReceiptHandle"])

        except Exception as e:
            logger.error("error_receiving_messages", error=str(e))
            raise

    def _should_process_message(self, message: Dict[str, Any]) -> bool:
        """Determine if a message should be processed based on filters"""
        try:
            # Check event type filter
            event_type = (
                message.get("MessageAttributes", {})
                .get("eventType", {})
                .get("StringValue")
            )
            if (
                self.config.allowed_event_types
                and event_type not in self.config.allowed_event_types
            ):
                return False

            # For Detection events, check additional filters
            if event_type == EventType.DETECTION:
                body = json.loads(message["Body"])

                # Check camera ID filter
                if (
                    self.config.allowed_camera_ids
                    and body.get("camera_id")
                    not in self.config.allowed_camera_ids
                ):
                    return False

                # Check severity filter
                if (
                    self.config.allowed_severities
                    and body.get("severity")
                    not in self.config.allowed_severities
                ):
                    return False

            return True

        except Exception as e:
            logger.error("error_in_message_filter", error=str(e))
            return False

    def _process_single_message(self, message: Dict[str, Any]) -> None:
        """Process a single SQS message"""
        try:
            body = json.loads(message["Body"])
            attributes = message.get("MessageAttributes", {})

            # Extend visibility timeout for long-running processing
            # self._extend_visibility_timeout(message["ReceiptHandle"])

            # Process the message
            self.processor.process_event_sync(body, attributes)

            # Delete the message after successful processing
            self._delete_message(message["ReceiptHandle"])

        except Exception as e:
            logger.error(
                "error_processing_message",
                message_id=message.get("MessageId"),
                error=str(e),
            )
            self._handle_failed_message(message, str(e))

    def _extend_visibility_timeout(
        self, receipt_handle: str, timeout: Optional[int] = None
    ) -> None:
        """Extend the visibility timeout for a message"""
        try:
            self.sqs.change_message_visibility(
                QueueUrl=self.config.queue_url,
                ReceiptHandle=receipt_handle,
                VisibilityTimeout=timeout or self.config.visibility_timeout,
            )
        except Exception as e:
            logger.error("error_extending_message_visibility", error=str(e))

    def _delete_message(self, receipt_handle: str) -> None:
        """Delete a message from the queue"""
        try:
            self.time_end = time.time()
            total_time = self.time_end - self.time_start
            logger.info("time_to_delete_message", total_time=total_time)
            self.sqs.delete_message(
                QueueUrl=self.config.queue_url, ReceiptHandle=receipt_handle
            )
        except Exception as e:
            logger.error("error_deleting_message", error=str(e))

    def _handle_failed_message(
        self, message: Dict[str, Any], error: str
    ) -> None:
        """Handle a failed message processing attempt"""
        try:
            self._delete_message(message["ReceiptHandle"])

        except Exception as e:
            logger.error("error_handling_failed_message", error=str(e))

    def stop(self) -> None:
        """Stop the consumer gracefully"""
        logger.info("stopping_sqs_consumer")
        self.running = False
