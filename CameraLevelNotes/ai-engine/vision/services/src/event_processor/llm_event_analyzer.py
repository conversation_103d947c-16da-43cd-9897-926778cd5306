import ast
import asyncio
import base64
import json
import os
import re
import tempfile
import time
from collections import defaultdict as dd
from datetime import datetime, timezone, tzinfo
from io import BytesIO
from typing import Any, Dict, List, Optional, Tuple

import aioboto3
import aiohttp
import pytz
import structlog
from PIL import Image

import config.backend_config as config
from common_utils_v1.io_helpers import read_file
from common_utils_v1.vision_metrics_definitions import (
    VISION_LLM_ALARM_GROUP_ANALYSIS_TIME,
    VISION_LLM_ALARM_GROUP_RECOMMENDATION,
    VISION_LLM_API_RESPONSE,
    VISION_LLM_API_TIME,
    VISION_LLM_API_TOKEN_COUNT,
    VISION_LLM_EVENT_ANALYSIS_TIME,
    VISION_LLM_EVENT_RECOMMENDATION,
)
from db_controller import DBControllerV1
from db_model_rds.rds_client import RDSClient
from models_rds.event import Event

log = structlog.get_logger("hakimo", module="LLM Event Analyzer")

ERROR_STRING = "Unable to process event/alarm group. "

# Create global sessions
_s3_session = None
_http_session = None


async def get_s3_session():
    """Get or create an S3 session"""
    global _s3_session
    if _s3_session is None:
        _s3_session = aioboto3.Session()
    return _s3_session


async def get_http_session():
    """Get or create an HTTP session"""
    global _http_session
    if _http_session is None:
        _http_session = aiohttp.ClientSession()
    return _http_session


def get_s3_path_details(s3_filepath: str) -> Optional[Tuple[str, str]]:
    """
    Given S3 file path, extract bucket name and file name,
    to enable easy downloading

    Args:
        s3_filepath (str): Full path to file

    Returns:
        (bucket_name, file_name) or None if path is not a valid S3 path
    """
    if s3_filepath.startswith("https://"):
        bucket_info, file_name = s3_filepath.split("amazonaws.com/")
        bucket_name = bucket_info.split(".s3")[0][len("https://") :]
        return (bucket_name, file_name)
    if s3_filepath.startswith("s3://"):
        bucket_name, *file_parts = s3_filepath[len("s3://") :].split("/")
        return (bucket_name, "/".join(file_parts))
    return None


async def download_frame_from_s3(
    bucket_name: str, file_name: str, save_file_path: str
) -> None:
    """Utility to download files from s3, given the bucket name, file name and
    the local file path to which to download to"""
    session = await get_s3_session()
    # TODO: Use a pool of clients like s3_utils.py
    async with session.client(
        "s3",
        aws_access_key_id=read_file(config.HAIE.AWS_ACCESS_KEY_ID, missing=""),
        aws_secret_access_key=read_file(
            config.HAIE.AWS_SECRET_KEY, missing=""
        ),
        region_name=config.HAIE.AWS_S3_REGION,
    ) as s3:
        stime = time.time()
        await s3.download_file(bucket_name, file_name, save_file_path)
        log.debug(
            "Time taken to download frame from S3",
            download_time=f"{(time.time() - stime):.2f}",
            save_file_path=save_file_path,
        )


async def encode_image_to_base64(image_path: str) -> str:
    image = Image.open(image_path)
    buffered = BytesIO()
    image.save(buffered, format="WEBP")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    return img_str


async def download_and_encode_frame(s3_path: str, dir_to_save: str) -> str:
    """Check if the frame is present locally otherwise download from s3.
    After downloading convert frame to base64 and return that.

    Args:
        s3_path (str): Full s3 path to file
        dir_to_save (str): Directory to save image in. Can be a temp dir or a
            local dir where we expect to find the frame or where we want to
            save it. Local dir can be of the format {base_dir}/{ag.id}/{e.id}/

    Returns:
        encoded_image_str (str): Base64 encoding of the frame
    """
    s3_path_details = get_s3_path_details(s3_path)
    assert s3_path_details is not None, "Frame s3 path should be valid"
    bucket_name, file_name = s3_path_details
    save_file_name = os.path.basename(file_name)

    save_file_path = os.path.join(dir_to_save, save_file_name)

    if os.path.exists(save_file_path):
        log.debug("Frame found in local dir", save_file_path=save_file_path)
    else:
        log.debug(
            "Frame NOT found in local dir, downloading from s3",
            bucket_name=bucket_name,
            file_name=file_name,
            save_file_path=save_file_path,
        )
        await download_frame_from_s3(bucket_name, file_name, save_file_path)

    assert os.path.exists(save_file_path), (
        "Frame not available locally and failed to download from s3: "
        f"{bucket_name} {save_file_name} {dir_to_save}"
    )

    encoded_image_str = await encode_image_to_base64(save_file_path)
    return encoded_image_str


# async def get_camera_info(controller: DBControllerV1, camera_id: str):
#     # ) -> Optional[Cameras]:
#     try:
#         camera = controller.camera.get_camera_by_id(
#             camera_id, include_location=True
#         )
#         if not camera:
#             log.error("Camera not found", camera_id=camera_id)
#             return None
#         return camera
#     except Exception as e:
#         log.error("Error in getting camera info", camera_id=camera_id, error=e)
#         return None


# async def get_location_info(
#     controller: DBControllerV1, location_id: int
# ) -> Optional[Locations]:
#     try:
#         location = controller.locations.get_location_by_id(location_id)
#         if not location:
#             log.error("Location not found", location_id=location_id)
#             return None
#         return location
#     except Exception as e:
#         log.error(
#             "Error in getting location info", location_id=location_id, error=e
#         )
#         return None


async def get_sop(
    controller: DBControllerV1, location_id: int
) -> Dict[str, Any]:
    sop = controller.sop.get_sop(location_id=location_id)
    if not sop:
        log.error(
            "SOP not found",
            location_id=location_id,
        )
        return {}
    sop_dict = json.loads(sop.sop)

    # Local override for SOP
    # sop = '{"sop_workflow":{"exceptions":["Building 2 does not have any speakers; talkdown need not be conducted for cameras from this building.","Security officers will conduct routine patrols and may go to specific checkpoints while on duty."],"siteAddress":"The Edison at Gordon Square, 6060 Father Caruso Dr, Cleveland, OH 44102","siteGoogleMapLocation":"https://www.google.com/maps/place/The+Edison+at+Gordon+Square/@41.4896987,-81.7291364,856m/data=!3m2!1e3!4b1!4m6!3m5!1s0x8830f04c6ac1e05d:0x2c3af61c15a11557!8m2!3d41.4896987!4d-81.7291364!16s%2Fg%2F11bz0b337t?entry=ttu&g_ep=EgoyMDI1MDQwMi4xIKXMDSoASAFQAw%3D%3D","escalationPoints":["Your location and role (How do you know)","Description of the individual(s) / vehicles involved, if applicable","Situation assessment (e.g., trespassing, theft, etc.)","YOUR Reachable SOC Phone number for follow-ups (with Law Enforcement)"],"isTalkdownEnabled":true,"notes":["CONDUCT TALKDOWNS WHERE NECESSARY. ALL CAMERAS, EXCEPT THOSE IN BUILDING 2, ARE EQUIPPED WITH SPEAKERS."],"situations":[{"label":"Fighting/Violence","color":"red"},{"label":"Kicking down doors","color":"blue"},{"label":"Breaking windows","color":"blue"},{"label":"Vehicle break in","color":"blue"},{"label":"Person with a gun","color":"red"},{"label":"Fire starting","color":"red"},{"label":"Vandalism","color":"red"},{"label":"Gate forced entry","color":"blue"},{"label":"Gate jumping","color":"green"},{"label":"Bike area tampering","color":"green"},{"label":"Homeless or vagrant on property","color":"blue"},{"label":"Suspicious Loitering","color":"blue"},{"label":"Others","color":"blue"}],"escalationProtocol":["Call SPOC before calling PD. If it is an emergency, call PD immediately"],"quickResolveActions":["Authorized Person","Authorized Vehicle","False Positive","Others"],"talkdowns":[{"text":"Please leave the area as soon as possible","type":"static"}],"isZeroTolerance":true}}'
    # sop_dict = json.loads(sop)

    if "sop_workflow" not in sop_dict or not isinstance(
        sop_dict["sop_workflow"], Dict
    ):
        log.error("sop_workflow key not found in SOP", sop_dict=sop_dict)
        return {}

    return sop_dict["sop_workflow"]


def transform_relevant_sections_of_sop(sop_dict: Dict[str, Any]) -> str:
    """Converts a SOP dict to text for the prompt by extracting relevant
    sections. Each relevant section in the SOP dict must be a list of strings
    """
    # Relevant sections to be picked from the SOP with descriptions
    descriptions = {
        "emergencySituations": "Emergency Situations (important to escalate)",
        "nonEmergencySituations": "Non-Emergency Situations (still need to be escalated)",
        "exceptions": "Exceptions (situations describing expected behaviour)",
    }

    relevant_sop_sections: Dict[str, List] = dd(list)
    if "situations" in sop_dict:
        for situation in sop_dict["situations"]:
            # TODO: Replace colors with the right keys
            if (
                isinstance(situation, dict)
                and "label" in situation
                and "color" in situation
            ):
                if situation["color"] == "red":
                    relevant_sop_sections["emergencySituations"].append(
                        situation["label"]
                    )
                elif situation["color"] == "green":
                    relevant_sop_sections["nonEmergencySituations"].append(
                        situation["label"]
                    )
                # Ignoring blue labels
    if "exceptions" in sop_dict and isinstance(sop_dict["exceptions"], list):
        relevant_sop_sections["exceptions"] = sop_dict["exceptions"]

    sop_prompt = "\n".join(
        [
            f"{descriptions.get(k) or k}: {', '.join(v)}"
            for k, v in relevant_sop_sections.items()
        ]
    )

    # Local override for SOP
    # sop_prompt = "\n".join(
    #     [
    #         "Emergency Situations (important to escalate immediately): Fighting/Violence, Kicking down doors, Breaking windows, Vehicle break in, Person with a gun, Fire starting, Vandalism, people wearing ski-masks etc",
    #         "Non-Emergency Situations (still need to be escalated): Homeless or vagrant seen on property, police officers seen on site, smoking, etc.",
    #         "Exceptions (situations describing expected behaviour): Local security guards will conduct routine patrols and go to specific checkpoints while on duty.",
    #     ]
    # )

    return sop_prompt


def generate_event_analyzer_prompt_from_sop(
    transformed_sop: str,
    camera_name: str,
    local_time: datetime,
    local_timezone: tzinfo,
) -> str:
    weekday = local_time.strftime("%A")
    header = (
        "You are an advanced AI security analyst. These are image frames from the camera feed of a site monitored by a remote security operator. "
        "The frames are captured at 1 frame per second, whenever motion is detected. Each frame is provided with its timestamp in the local timezone. "
        "Analyze the sequence of frames and understand the activity that is happening. "
        "Track individuals across frames based on clothing, posture, and location continuity. "
        "Assume that a person in consecutive frames is the same unless clearly different. "
        "The footage is from an apartment complex where normal activities such as residents walking, entering/exiting buildings, and vehicles moving are expected. "
        "However, any activity that appears suspicious or deviates from normal behavior must be identified and escalated.\n"
        f"For this event, the camera name is {camera_name}. "
        f"The local timezone is {local_timezone} and the time when this event occurred is {local_time.strftime('%Y-%m-%d %H:%M:%S')}({weekday}).\n"
        "This is the Standard Operating Procedure (SOP) that the operator has to follow, which lists some common incidents that need to be escalated to local security or law enforcement. "
        "\nSOP START."
    )

    footer = (
        "SOP END.\n"
        "Do the following:\n"
        "1. Look for suspicious actions as defined in the SOP or any other clearly suspicious activity.\n"
        "2. If there are any suspicious persons or moving vehicles, describe their appearance, behavior, and any other relevant details. "
        "Describe each person in detail, including clothing, gender (if possible), approximate age group (e.g., young adult, middle-aged), build (e.g., slim, heavy), and any distinctive features (e.g., bag, hat, glasses).\n"
        "3. Highlight any anomalies or patterns that deviate from normal behavior. While normal movement of residents and vehicles is expected, anything suspicious must be escalated.\n"
        "4. Provide an overall summary and a recommendation based on all the events to the operator on whether to Resolve or Escalate the event. "
        "The recommendation should be Resolve if there is nothing suspicious and no further action is needed, "
        "or Escalate if the operator should take further action.\n"
        "Respond in json format with keys 'summary' and 'recommendation' where recommendation is either 'Resolve' or 'Escalate'. "
    )
    return f"{header}\n{transformed_sop}\n{footer}"


def llm_response_parser(response_data) -> Dict[str, str]:
    """We expect the LLM to respond in json format with the keys summary and
    recommendation. This function returns the dictionary in LLM's response"""

    input_tokens = response_data.get("usageMetadata", {}).get(
        "promptTokenCount", 0
    )
    output_tokens = response_data.get("usageMetadata", {}).get(
        "candidatesTokenCount", 0
    )
    VISION_LLM_API_TOKEN_COUNT.labels(type="input").inc(input_tokens)
    VISION_LLM_API_TOKEN_COUNT.labels(type="output").inc(output_tokens)

    output = [
        part["text"]
        for candidate in response_data.get("candidates", [])
        for part in candidate.get("content", {}).get("parts", [])
    ]
    full_output = "\n".join(output)
    # Strip leading/trailing spaces and remove markdown markers
    cleaned_output = re.sub(r"```json\n?|```", "", full_output).strip()
    # Escape newlines in quoted strings
    cleaned_output = re.sub(
        r"(['\"])(.*?)(\1)",
        lambda m: m.group(1) + m.group(2).replace("\n", "\\n") + m.group(1),
        cleaned_output,
        flags=re.DOTALL,
    )

    try:
        out_dict = ast.literal_eval(cleaned_output)
    except Exception as e:
        log.error(
            "Decoding of LLM output failed",
            error=str(e),
            cleaned_output=cleaned_output,
        )
        return {}
    return out_dict


async def send_request_to_llm(parts: List[Dict[str, Any]]) -> Dict[str, str]:
    # LLM specific parameters
    model_name = "gemini-2.0-flash"
    api_key = config.HAIE.GEMINI_EVENT_ANALYZER_KEY
    url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_name}:generateContent?key={api_key}"

    # Generate request header and payload
    headers = {"Content-Type": "application/json"}
    payload = {
        "generationConfig": {"temperature": 0.1},
        "contents": [{"parts": parts}],
    }
    MAX_RETRIES = 3
    TIMEOUT = 15  # seconds

    # HTTP status codes that should trigger a retry
    RETRY_STATUS_CODES = {429, 500, 502, 503, 504}

    for attempt in range(MAX_RETRIES):
        try:
            session = await get_http_session()
            start_time = time.time()
            async with session.post(
                url,
                headers=headers,
                json=payload,
                timeout=TIMEOUT,
            ) as response:
                time_taken = time.time() - start_time
                VISION_LLM_API_TIME.observe(time_taken)
                # Check if response is successful
                try:
                    response.raise_for_status()
                except aiohttp.ClientResponseError as e:
                    if e.status in RETRY_STATUS_CODES:
                        raise  # Re-raise to trigger retry
                    log.error(
                        "LLM request failed with non-retryable status code",
                        status=e.status,
                        response=response,
                        error=e,
                    )
                    VISION_LLM_API_RESPONSE.labels(
                        success=False,
                        response_code=e.status,
                    ).inc()
                    return {}

                VISION_LLM_API_RESPONSE.labels(
                    success=True,
                    response_code=response.status,
                ).inc()

                result = await response.json()

                # Parse response as a dict
                result_dict = llm_response_parser(result)
                if (
                    not result_dict
                    or "summary" not in result_dict
                    or "recommendation" not in result_dict
                ):
                    log.error("LLM response parsing failed", response=result)
                    return {}
                return result_dict

        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            if isinstance(e, aiohttp.ClientResponseError):
                response_code = e.status
            elif isinstance(e, aiohttp.ClientError):
                response_code = "client_error"
            elif isinstance(e, asyncio.TimeoutError):
                response_code = "timeout_error"
            else:
                response_code = "unknown_error"
            VISION_LLM_API_RESPONSE.labels(
                success=False,
                response_code=response_code,
            ).inc()
            if attempt == MAX_RETRIES - 1:  # Last attempt
                log.error("Max retries reached for LLM request", error=e)
                return {}
            log.warning(
                "Retrying LLM request",
                attempt=attempt + 1,
                max_retries=MAX_RETRIES,
                error=e,
            )

        except Exception as e:
            log.error("Unexpected error in LLM request", error=e)
            VISION_LLM_API_RESPONSE.labels(
                success=False,
                response_code="unknown_exception",
            ).inc()
            return {}

    # Should never reach here
    log.error("Failed to get LLM response after max retries")
    return {}


async def generate_summary_from_llm_responses(
    responses: List[Tuple[str, datetime, str]],
    transformed_sop: str,
    local_timezone: tzinfo,
) -> Tuple[str, str, str]:
    """Takes a list of responses (analysis, timestamp, camera_name) for events and the SOP dict.
    Returns the summary and recommendation for the entire alarm group"""
    content_parts = []
    content_parts.append(
        {
            "text": (
                "You are an advanced AI security analyst. These are events from the camera feed of a site monitored by a remote security operator. "
                "Following are the details of multiple video events detected by the cameras on the site with an explanation of what is happening in those events. "
                "Each video event is provided with its timestamp in the local timezone. "
                "Analyze the sequence of descriptions of video events and understand the activity that is happening. "
                "Identify if the same individual appears in multiple events based on matching descriptions (e.g., same clothing, actions, and sequence). Consider location and time continuity. "
                "The footage is from an apartment complex where normal activities such as residents walking, entering/exiting buildings, and vehicles moving are expected. "
                "However, any activity that appears suspicious or deviates from normal behavior must be identified and escalated. "
                f"The local timezone for the site is {local_timezone}. "
            )
        }
    )

    for analysis, local_timestamp, camera_name in responses:
        weekday = local_timestamp.strftime("%A")
        ts_str = local_timestamp.strftime("%Y-%m-%d %H:%M:%S")
        content_parts.append(
            {
                "text": f"\nCamera Name: {camera_name}\nEvent Timestamp: {ts_str}({weekday})\nEvent Summary: {analysis}\n"
            }
        )
    content_parts.append(
        {
            "text": (
                "This is the Standard Operating Procedure the operator has to follow:"
                f"\nSOP Start\n{transformed_sop}\nSOP End\n"
                "Do the following:\n"
                "1. Look for suspicious actions as defined in the SOP or any other clearly suspicious activity.\n"
                "2. If there are any suspicious persons or moving vehicles, describe their appearance, behavior, and any other relevant details.\n"
                "3. Highlight any anomalies or patterns that deviate from normal behavior. While normal movement of residents and vehicles is expected, anything suspicious must be escalated.\n"
                "4. Provide a detailed explanation, a brief summary and a recommendation based on all the events to the operator on whether to Resolve or Escalate the event. "
                "The explanation should be describe everything happening in the events, focusing on the suspicious activities. "
                "The summary should be concise so that the operator can get a quick overview of the important events. The summary should only include the suspicious activities which require escalation. "
                "The recommendation should be Resolve if there is nothing suspicious and no further action is needed, "
                "or Escalate if the operator should take further action. "
                "Respond in json format with keys 'explanation', 'summary' and 'recommendation' where recommendation is either Resolve or Escalate. "
            )
        }
    )

    prompt = "\n".join(part["text"] for part in content_parts)
    log.debug("Generated prompt to get alarm group summary", prompt=prompt)

    result_dict = await send_request_to_llm(content_parts)

    return (
        result_dict.get("summary", ""),
        result_dict.get("recommendation", ""),
        result_dict.get("explanation", ""),
    )


async def fetch_events_analysis_from_db(
    controller: DBControllerV1, event_id: str
) -> Tuple[str, str]:
    ai_output = controller.ai_outputs.get_latest_ai_output_for_event(event_id)
    if not ai_output:
        log.error("AI Output not found in DB for event", event_id=event_id)
        return ERROR_STRING, "AI Output not found for event"

    return str(ai_output.analysis), str(ai_output.recommendation)


async def analyze_event(
    controller: DBControllerV1,
    e: Event,
    prompt: str = "",
    save_to_db: bool = False,
    local_frames_dir: str = "",
) -> Tuple[str, str]:
    log.info(
        "Processing event", event_id=e.id, alarm_group_id=e.alarm_group_id
    )
    start_time = time.time()
    event_data = e.event_details
    if (
        "metadata" not in event_data
        or not isinstance(event_data["metadata"], dict)
        or "frames" not in event_data["metadata"]
        or not isinstance(event_data["metadata"]["frames"], list)
    ):
        log.error("Incorrect format of event_data in Event", event_object=e)
        return ERROR_STRING, "Incorrect format of event_data in Event"

    frames = event_data["metadata"]["frames"]

    if len(frames) == 0:
        log.debug("Detection event without frames, skipping", event_object=e)
        return ERROR_STRING, "Detection event without frames, skipping"

    camera_info = controller.camera.get_camera_by_id(
        e.camera_id, include_location=True
    )
    if camera_info is None:
        log.error(
            "Camera not found in DB for event",
            event_id=e.id,
            alarm_group_id=e.alarm_group_id,
            camera_id=e.camera_id,
            tenant_id=e.tenant_id,
        )
        return ERROR_STRING, "Camera not found in DB for event"

    # if camera_info.location is None:
    #     # TODO: Can try fetching again using location_id
    #     log.error(
    #         "Location not found for camera",
    #         event_id=e.id,
    #         alarm_group_id=e.alarm_group_id,
    #         camera_id=e.camera_id,
    #         tenant_id=e.tenant_id,
    #     )
    #     return ERROR_STRING + "Location not found for camera"

    local_timezone = pytz.timezone(camera_info.location_timezone)
    utc_time = e.event_time_utc.replace(tzinfo=timezone.utc)
    event_local_time = utc_time.astimezone(local_timezone)

    # Get SOP and prompt
    if not prompt:
        sop_dict = await get_sop(controller, camera_info.location_id)
        if not sop_dict:
            log.error(
                "SOP not found",
                location_id=camera_info.location_id,
                event_id=e.id,
                alarm_group_id=e.alarm_group_id,
            )
            return ERROR_STRING, "SOP not found"
        transformed_sop = transform_relevant_sections_of_sop(sop_dict)
        prompt = generate_event_analyzer_prompt_from_sop(
            transformed_sop,
            camera_info.camera_name,
            event_local_time,
            local_timezone,
        )
        log.debug(
            "Generated prompt for event analysis",
            event_id=e.id,
            alarm_group_id=e.alarm_group_id,
            prompt=prompt,
        )

    # parts for http request
    parts = []
    parts.append({"text": prompt})

    # Frames can be present in this local dir or downloaded there
    if local_frames_dir:
        event_dir = os.path.join(local_frames_dir, e.id)
        os.makedirs(event_dir, exist_ok=True)
    else:
        event_dir = ""

    # download frames from s3 and encode to base64
    with tempfile.TemporaryDirectory() as tmp_dir:
        processing_tasks = []
        dir_to_save = event_dir or tmp_dir
        for [s3_path, _] in frames:
            processing_tasks.append(
                asyncio.create_task(
                    download_and_encode_frame(
                        s3_path,
                        dir_to_save,
                    )
                )
            )
        encoded_images = await asyncio.gather(
            *processing_tasks, return_exceptions=True
        )

        # TODO: Can upload them independently in parallel and use file uri as well
        frame_counter = 1
        for [s3_path, frame_timestamp], img_str in zip(frames, encoded_images):
            if isinstance(img_str, BaseException):
                log.error(
                    "Error downloading and encoding frame",
                    error=str(img_str),
                    event_id=e.id,
                    s3_path=s3_path,
                )
                continue
            frame_local_time = datetime.fromtimestamp(
                float(frame_timestamp) / 1000,
                tz=local_timezone,
            )
            frame_local_time_str = frame_local_time.strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            frame_weekday = frame_local_time.strftime("%A")
            parts.append(
                {
                    "inline_data": {
                        "mime_type": "image/webp",
                        "data": img_str,
                    }
                }
            )
            parts.append(
                {
                    "text": f"Frame {frame_counter}: {frame_local_time_str} ({frame_weekday})\n"
                }
            )
            frame_counter += 1

    if len(parts) == 1:
        log.error(
            "Could not download any frames for event",
            event_id=e.id,
            alarm_group_id=e.alarm_group_id,
        )
        return ERROR_STRING, "Could not download any frames for event"

    result_dict = await send_request_to_llm(parts)
    summary, recommendation = (
        result_dict.get("summary", ""),
        result_dict.get("recommendation", ""),
    )

    if not summary or not recommendation:
        log.error(
            "LLM failed to analyze event",
            event_id=e.id,
            alarm_group_id=e.alarm_group_id,
        )
        return ERROR_STRING, "LLM failed to analyze event"

    # Save response to db
    if save_to_db:
        # Store event summary and recommendation in ai_outputs table
        entry_id = controller.ai_outputs.add_ai_output_for_event(
            e.id, e.tenant_id, summary, recommendation
        )
        log.debug("Saved event analysis to db", entry_id=entry_id)

    time_taken = time.time() - start_time
    log.info(
        "Analyzed Event",
        event_id=e.id,
        alarm_group_id=e.alarm_group_id,
        summary=summary,
        recommendation=recommendation,
        time_taken=f"{time_taken:.2f}",
    )

    VISION_LLM_EVENT_RECOMMENDATION.labels(
        tenant_id=e.tenant_id,
        camera_group_id=e.camera_group_id,
        camera_id=e.camera_id,
        recommendation=recommendation,
    ).inc()

    VISION_LLM_EVENT_ANALYSIS_TIME.labels(
        tenant_id=e.tenant_id,
        camera_group_id=e.camera_group_id,
        camera_id=e.camera_id,
    ).observe(time_taken)

    return summary, recommendation


async def analyze_alarm_group(
    ctrl_map: DBControllerV1,
    alarm_group_id: str,
    reeval_events: bool = False,
    save_to_db: bool = False,
    local_frames_dir: str = "",
    events_per_alarm_group_limit=50,
) -> Tuple[str, str, str, List[str]]:
    log.info("Processing alarm group", alarm_group_id=alarm_group_id)
    start_time = time.time()

    # Fetch all events from event db using alarm group filter, pick latest
    # events if limit is less than the number of events for an alarm group
    events = ctrl_map.event.get_all_events_for_alarm_group(
        alarm_group_id,
        limit=events_per_alarm_group_limit,
        order_by="desc",
    )
    # Reverse to process events in increasing order of timestamp
    events.reverse()

    alarm_group_event_ids = [e.id for e in events]
    log.info(
        "Collected events for alarm group",
        alarm_group_id=alarm_group_id,
        alarm_group_event_ids=alarm_group_event_ids,
    )
    if len(alarm_group_event_ids) == 0:
        return (
            ERROR_STRING,
            ERROR_STRING,
            "No events found for alarm group",
            [],
        )

    unique_camera_ids = set(
        [e.camera_id for e in events if e.camera_id is not None]
    )
    # Fetch camera info for all unique camera ids
    camera_infos: Dict[str, Any] = {}
    for camera_id in unique_camera_ids:
        camera_info = ctrl_map.camera.get_camera_by_id(
            camera_id, include_location=True
        )
        if camera_info:
            camera_infos[camera_id] = camera_info
        else:
            log.error(
                "Camera not found in DB for event",
                camera_id=camera_id,
                alarm_group_id=alarm_group_id,
            )

    if not camera_infos:
        log.error(
            "No camera found in DB for alarm group",
            alarm_group_id=alarm_group_id,
            camera_ids=unique_camera_ids,
            alarm_group_event_ids=alarm_group_event_ids,
        )
        return (
            ERROR_STRING,
            ERROR_STRING,
            "No camera found in DB for alarm group",
            [],
        )

    # Assume all cameras are from the same location in a camera group
    camera_info = list(camera_infos.values())[0]

    # if camera_info.location is None:
    #     # TODO: Can try fetching again using location_id
    #     log.error(
    #         "Location not found for camera",
    #         event_id=e.id,
    #         alarm_group_id=e.alarm_group_id,
    #         camera_id=e.camera_id,
    #         tenant_id=e.tenant_id,
    #     )
    #     return ERROR_STRING, "Location not found for camera"

    local_timezone = pytz.timezone(camera_info.location_timezone)

    sop_dict = await get_sop(ctrl_map, camera_info.location_id)
    if not sop_dict:
        log.error(
            "SOP not found",
            location_id=camera_info.location_id,
        )
        return (
            ERROR_STRING,
            ERROR_STRING,
            "SOP not found",
            [],
        )
    transformed_sop = transform_relevant_sections_of_sop(sop_dict)

    if local_frames_dir:
        alarm_group_dir = os.path.join(local_frames_dir, alarm_group_id)
        os.makedirs(alarm_group_dir, exist_ok=True)
    else:
        alarm_group_dir = ""

    event_local_times: Dict[str, datetime] = {}
    event_camera_names: Dict[str, str] = {}
    processing_tasks = []
    for e in events:
        # Make datetime object timezone aware, then convert to local
        utc_time = e.event_time_utc.replace(tzinfo=timezone.utc)
        event_local_time = utc_time.astimezone(local_timezone)
        event_local_times[e.id] = event_local_time
        event_camera_names[e.id] = (
            camera_infos[e.camera_id].camera_name
            if e.camera_id in camera_infos
            else ""
        )
        if reeval_events:
            prompt = generate_event_analyzer_prompt_from_sop(
                transformed_sop,
                event_camera_names[e.id],
                event_local_time,
                local_timezone,
            )
            # Pass the event object to the analyzer
            processing_tasks.append(
                asyncio.create_task(
                    analyze_event(
                        ctrl_map,
                        e,
                        prompt=prompt,
                        save_to_db=save_to_db,
                        local_frames_dir=alarm_group_dir,
                    )
                )
            )
        else:
            # Fetch event analysis from db
            processing_tasks.append(
                asyncio.create_task(
                    fetch_events_analysis_from_db(ctrl_map, str(e.id))
                )
            )

    results = await asyncio.gather(*processing_tasks, return_exceptions=True)
    log.debug(
        "Processed events in alarm group",
        alarm_group_id=alarm_group_id,
        alarm_group_event_ids=alarm_group_event_ids,
        results=results,
    )
    # List of tuples of (response, timestamp) for events with valid analysis
    filtered_results: List[Tuple[str, datetime, str]] = []
    events_with_valid_analysis: List[str] = []
    for event_id, result in zip(alarm_group_event_ids, results):
        if isinstance(result, BaseException) or result[0] == ERROR_STRING:
            log.error(
                "Error Analyzing event",
                event_id=event_id,
                alarm_group_id=alarm_group_id,
                error=str(result),
            )
        else:
            filtered_results.append(
                (
                    result[0],
                    event_local_times[event_id],
                    event_camera_names[event_id],
                )
            )
            events_with_valid_analysis.append(event_id)
    if len(events_with_valid_analysis) == 0:
        log.error(
            "No valid analysis found for any event in alarm group",
            alarm_group_id=alarm_group_id,
            alarm_group_event_ids=alarm_group_event_ids,
        )
        return (
            ERROR_STRING,
            ERROR_STRING,
            "No valid analysis found for any event in alarm group",
            [],
        )

    # Generate summary and recommendation from analysis of all events
    (
        summary,
        recommendation,
        explanation,
    ) = await generate_summary_from_llm_responses(
        filtered_results, transformed_sop, local_timezone
    )

    if not summary or not recommendation:
        log.error(
            "LLM failed to generate summary from alarm group events",
            alarm_group_id=alarm_group_id,
        )
        return (
            ERROR_STRING,
            ERROR_STRING,
            "LLM failed to generate summary from alarm group events",
            [],
        )

    # Save alarm group summary and recommendation to db
    if save_to_db:
        entry_id = ctrl_map.ai_outputs.add_or_update_ai_output_for_alarm_group(
            alarm_group_id,
            str(events[0].tenant_id),
            summary,
            recommendation,
            explanation,
        )
        log.debug(
            "Saved alarm group analysis to db",
            entry_id=entry_id,
        )

    time_taken = time.time() - start_time
    log.info(
        "Analyzed Alarm Group",
        alarm_group_id=alarm_group_id,
        summary=summary,
        recommendation=recommendation,
        explanation=explanation,
        time_taken=f"{time_taken:.2f}",
    )

    VISION_LLM_ALARM_GROUP_RECOMMENDATION.labels(
        tenant_id=events[0].tenant_id,
        camera_group_id=events[0].camera_group_id,
        recommendation=recommendation,
    ).inc()

    VISION_LLM_ALARM_GROUP_ANALYSIS_TIME.labels(
        tenant_id=events[0].tenant_id,
        camera_group_id=events[0].camera_group_id,
    ).observe(time_taken)

    return summary, recommendation, explanation, events_with_valid_analysis


async def main():
    rds = RDSClient()
    db = rds.db_adapter
    read_db = rds.read_db_adapter
    ctrl_map = DBControllerV1(db, read_db)

    # List of event ids to analyze
    event_ids = [
        # "0000013a-1df5-4956-9231-3d728818c758",  # staging
        # "00006c29-06ea-4bb3-a028-43e2949fdaa7",  # staging
    ]

    processing_event_tasks = []
    for event_id in event_ids:
        # Fetch Event object for this event id from the DB
        e = ctrl_map.event.get_event(event_id)
        if e:
            processing_event_tasks.append(
                asyncio.create_task(
                    analyze_event(
                        ctrl_map,
                        e,
                        save_to_db=False,
                        local_frames_dir="",
                    )
                )
            )
        else:
            log.error("Event not found", event_id=event_id)

    event_results = await asyncio.gather(
        *processing_event_tasks, return_exceptions=True
    )

    log.info("Processed events ", event_ids=event_ids, results=event_results)

    # List of alarm groups to analyze
    alarm_group_ids = [
        # "a2626596-1a52-4067-80f5-dd2de2a2aea5",  # prod incident
        # "db87d69a-0131-4cef-bf09-d5ad4747a360",  # prod incident
        # "000fb97f-82d5-42f7-9967-b8d782dde54e",  # prod
        # "00127499-fd4b-4c41-b78a-739d01c146b1",  # prod
        # "9ea461c2-9a5e-46c8-b9db-07acebac6be3",  # staging
        # "5b27a1c8-80ab-44f4-8e3e-d17850bfcdc9",  # staging
        "195a4d9b-a73a-4115-b963-49e62e6e8839",  # staging
        "09d30f5b-35c9-4411-9ab6-c70509209aa2",  # staging
    ]

    processing_alarm_group_tasks = []
    for alarm_group_id in alarm_group_ids:
        processing_alarm_group_tasks.append(
            asyncio.create_task(
                analyze_alarm_group(
                    ctrl_map,
                    alarm_group_id,
                    reeval_events=True,
                    save_to_db=False,
                    local_frames_dir="",
                    events_per_alarm_group_limit=200,
                )
            )
        )
    alarm_group_results = await asyncio.gather(
        *processing_alarm_group_tasks, return_exceptions=True
    )

    log.info(
        "Processed alarm groups",
        alarm_group_ids=alarm_group_ids,
        results=alarm_group_results,
    )

    count_escalated = 0
    count_resolved = 0
    count_other = 0
    for alarm_group_id, result in zip(alarm_group_ids, alarm_group_results):
        if isinstance(result, BaseException):
            log.error(
                "Error analyzing alarm group",
                alarm_group_id=alarm_group_id,
                error=str(result),
            )
            count_other += 1
        else:
            print(
                f"Alarm Group {alarm_group_id}\nSummary: {result[0]}\nRecommendation: {result[1]}\nExplanation: {result[2]}\n"
            )
            if result[1].lower() == "escalate":
                count_escalated += 1
            elif result[1].lower() == "resolve":
                count_resolved += 1
            else:
                count_other += 1
    print(
        f"Escalated: {count_escalated}, Resolved: {count_resolved}, Other: {count_other}, Total: {count_escalated + count_resolved + count_other}"
    )


if __name__ == "__main__":
    asyncio.run(main())
