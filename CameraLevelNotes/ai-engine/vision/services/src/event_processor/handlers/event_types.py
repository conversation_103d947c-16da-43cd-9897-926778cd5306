# from db_controller import DB<PERSON><PERSON>roll<PERSON>V1
import json
import time
import uuid
from datetime import datetime, timezone
from typing import Any, Dict

import redis
import structlog

from common_utils_v1.vision_metrics_definitions import (
    VISION_ALARM_ALLOCATION_TO_RESOLUTION_DURATION_SECONDS,
    VISION_ALARM_COUNT,
    VISION_ALARM_GROUPS_RETRIEVED_TOTAL,
    VISION_ALARM_QUEUE_LENGTH,
    VISION_ALARM_RESOLUTION_DURATION_SECONDS,
    VISION_ALARMS_AGING_MINUTES,
    VISION_ALARMS_ORPHANED_TOTAL,
    VISION_ALARMS_PENDING_ALLOCATION,
    VISION_LLM_ALARM_GROUP_AUTO_RESOLVED,
    VISION_LLM_ALARM_GROUP_FORCED_ESCALATION,
    VISION_LLM_ALARM_GROUPS_PROCESSED_TOTAL,
    VISION_LLM_EVENTS_PROCESSED_TOTAL,
    VISION_OPERATOR_ACTIVE_ALARMS,
    VISION_OPERATOR_ACTIVE_ESCALATIONS,
    VISION_OPERATOR_ALARMS_ALLOCATED_TOTAL,
    VISION_OPERATOR_ALARMS_RESOLVED_TOTAL,
    VISION_OPERATOR_ESCALATIONS_CLOSED_TOTAL,
    VISION_OPERATOR_ESCALATIONS_CREATED_TOTAL,
    VISION_OPERATOR_INVESTIGATION_EVENTS_TOTAL,
)
from config import backend_config as config

# import controller as ctrl
from db_controller import DBControllerV1
from db_model_rds.rds_client import RDSClient
from models_rds.alarm_group import AlarmGroup
from models_rds.alarm_group_update import AlarmGroupUpdate
from models_rds.event import Event
from vision.services.src.event_processor.publisher.sync_sqs_publisher import (
    SyncSQSPublisher,
    SyncSQSPublisherSingleton,
)
from vision.services.src.event_processor.publisher_config import (
    PublisherConfig,
)
from vision.services.src.event_processor.utils.safety_checker import (
    SafetyChecker,
)

from ..llm_event_analyzer import (
    ERROR_STRING,
    analyze_alarm_group,
    analyze_event,
)
from ..models.events import (
    AlarmGroupQueueState,
    AlarmGroupRecommendation,
    AlarmGroupState,
    AllocationEvent,
    AutoResolvedEvent,
    DetectionEvent,
    EscalationCloseEvent,
    EscalationOpenEvent,
    EventRecommendation,
    EventState,
    EventType,
    GetAlarmEvent,
    InvestigationEvent,
    LLMAlarmGroupEvent,
    LLMEvent,
    OrphanEvent,
    Resolution,
    SafeEvent,
    Severity,
    TalkDownEvent,
    TwilioCallEvent,
    TwilioMessageEvent,
    event_details_json,
)
from .base import EventHandler

logger = structlog.get_logger("hakimo", module="EventProcessor")

rds = RDSClient()
db = rds.db_adapter
read_db = rds.read_db_adapter
# ctrl_map = ctrl.ControllerMap(db)
ctrl_map = DBControllerV1(db, read_db)

# Initialize Redis connection
redis_client = redis.Redis(
    host=config.HAIE.redis[
        "visionServiceName"
    ],  # Replace with your Redis host
    port=config.HAIE.redis["port"],  # Replace with your Redis port
    db=0,  # Default database
    decode_responses=True,
)

safety_checker = SafetyChecker(redis_client)

# One-time reset flag to fix existing negatives after deployment
_reset_done = False


# Helper functions to safely handle gauge operations
def safe_gauge_inc(gauge_metric, labels_dict, amount=1):
    """Safely increment a gauge metric"""
    try:
        gauge = gauge_metric.labels(**labels_dict)
        gauge.inc(amount)
    except Exception as e:
        logger.warning(
            "Failed to increment gauge", error=str(e), labels=labels_dict
        )


def safe_gauge_dec(gauge_metric, labels_dict, amount=1):
    """Safely decrement a gauge metric (won't go below 0)"""
    try:
        gauge = gauge_metric.labels(**labels_dict)
        # Get current value using prometheus_client internals
        current_value = (
            gauge._value._value if hasattr(gauge._value, "_value") else 0
        )
        if current_value > 0:
            gauge.dec(amount)
        else:
            logger.debug(
                "Gauge already at 0, skipping decrement", labels=labels_dict
            )
    except Exception as e:
        logger.warning(
            "Failed to decrement gauge", error=str(e), labels=labels_dict
        )
        # Fallback: just decrement normally
        try:
            gauge_metric.labels(**labels_dict).dec(amount)
        except:
            pass


def safe_gauge_set(gauge_metric, labels_dict, value):
    """Safely set a gauge metric to a specific value (ensures non-negative)"""
    try:
        gauge = gauge_metric.labels(**labels_dict)
        gauge.set(max(0, value))
    except Exception as e:
        logger.warning(
            "Failed to set gauge",
            error=str(e),
            labels=labels_dict,
            value=value,
        )


def get_camera_info(camera_id: str) -> dict:
    """
    Get camera info from the redis or database
    """
    camera_info = get_camera_info_from_cache(camera_id)
    if camera_info:
        return camera_info
    camera_controller = ctrl_map.camera
    camera_info = camera_controller.get_camera_by_id_without_cache(camera_id)
    camera_dict = dict(camera_info._mapping)
    store_camera_info_in_cache(camera_id, camera_dict)
    return camera_dict


def get_camera_info_from_cache(camera_id: str) -> dict:
    """
    Get camera info from Redis cache
    """
    try:
        cache = redis_client.get(f"camera:{camera_id}")
        if cache:
            return json.loads(cache)
        return None
    except Exception as e:
        logger.error(
            "Error getting camera info from cache",
            error=str(e),
            camera_id=camera_id,
        )
        return None


def store_camera_info_in_cache(camera_id: str, camera_info: dict) -> None:
    """
    Store camera info in Redis cache with 1 hour expiry
    """
    try:
        # Convert datetime objects to ISO format strings
        def datetime_handler(obj):
            if hasattr(obj, "isoformat"):
                return obj.isoformat()
            raise TypeError(
                f"Object of type {type(obj)} is not JSON serializable"
            )

        with redis_client.pipeline() as pipe:
            pipe.set(
                f"camera:{camera_id}",
                json.dumps(camera_info, default=datetime_handler),
            )
            pipe.expire(f"camera:{camera_id}", 3600)  # 1 hour expiry
            pipe.execute()
    except Exception as e:
        logger.error(
            "Error storing camera info in cache",
            error=str(e),
            camera_id=camera_id,
        )


def broadcast_event(event: DetectionEvent, alarm_group_stored_id: str):
    # Serialize the entire event object to JSON (not just event_details_json)
    logger.info(
        "Broadcasting event",
        alarm_group_id=alarm_group_stored_id,
    )
    operator_key = f"alarm_operator:{alarm_group_stored_id}"
    operator_id = redis_client.get(operator_key)
    event.alarm_group_id = alarm_group_stored_id
    value = json.dumps(event_details_json(event))
    logger.info(
        "Serialized event",
        alarm_group_id=alarm_group_stored_id,
        event_details_json=value,
    )
    if operator_id:
        redis_client.publish("detection-event-broadcast", value)
        logger.info(
            "Broadcasted event to redis channel",
            alarm_group_id=alarm_group_stored_id,
            operator_id=operator_id,
            tenant_id=event.tenant_id,
            camera_group_id=event.group_id,
        )
    else:
        logger.warning(
            "No operator found for alarm group",
            alarm_group_id=alarm_group_stored_id,
        )


def add_alarm_group_to_redis(
    alarm_group_stored_id: str, tenant_id: str, camera_group_id: str
):
    # Serialize the full alarm group data for storage
    alarm_group_json = {
        "alarm_group_id": alarm_group_stored_id,
        "tenant_id": tenant_id,
        "camera_group_id": camera_group_id,
    }
    queue_name = "single_alarm_group_queue"

    # Add retry logic to prevent data loss
    redis_success = False
    max_retries = 3
    retry_count = 0

    while not redis_success and retry_count < max_retries:
        try:
            # Use Redis pipeline for atomic operations
            with redis_client.pipeline() as pipe:
                # Add to sorted set and set expiry atomically
                pipe.zadd(
                    queue_name,
                    {
                        json.dumps(alarm_group_json): datetime.now(
                            timezone.utc
                        ).timestamp()
                    },
                )
                pipe.expire(queue_name, config.HAIE.VISION_GLOBAL_TTL)
                # Execute all operations atomically
                results = pipe.execute()

            # Validate pipeline results
            zadd_result, expire_result = results
            if zadd_result == 0:
                logger.debug(
                    "Alarm group already exists in Redis queue (zadd returned 0)",
                    alarm_group_id=alarm_group_stored_id,
                    queue_name=queue_name,
                )
            if expire_result == 0:
                logger.warning(
                    "Failed to set expiry on Redis queue (key may not exist)",
                    alarm_group_id=alarm_group_stored_id,
                    queue_name=queue_name,
                )

            # Validity check: Verify data is actually stored in Redis
            try:
                # Check 1: Verify the queue exists and has data
                queue_length = redis_client.zcard(queue_name)
                if queue_length == 0:
                    raise ValueError("Queue is empty after ZADD operation")

                # Check 2: Verify our specific alarm group data exists
                alarm_group_data_str = json.dumps(alarm_group_json)
                score = redis_client.zscore(queue_name, alarm_group_data_str)
                if score is None:
                    raise ValueError(
                        "Alarm group data not found in queue after ZADD"
                    )

                # Check 3: Verify TTL is set correctly
                ttl = redis_client.ttl(queue_name)
                if ttl == -1:  # -1 means no expiry set
                    logger.warning(
                        "TTL not set on Redis queue",
                        queue_name=queue_name,
                        alarm_group_id=alarm_group_stored_id,
                    )
                elif ttl == -2:  # -2 means key doesn't exist
                    raise ValueError("Queue key does not exist after creation")

                # Check 4: Retrieve and validate a few recent entries
                recent_entries = redis_client.zrevrange(
                    queue_name, 0, 4, withscores=True
                )
                if not recent_entries:
                    raise ValueError("No entries found in queue after ZADD")

                # Log validation success with detailed info
                redis_success = True
                logger.info(
                    "✅ Redis validation SUCCESS - Alarm group confirmed in queue",
                    alarm_group_id=alarm_group_stored_id,
                    queue_name=queue_name,
                    queue_length=queue_length,
                    zadd_result=zadd_result,
                    expire_result=expire_result,
                    ttl_seconds=ttl,
                    our_score=score,
                    recent_entries_count=len(recent_entries),
                )

            except Exception as validation_error:
                # Validation failed - log error and retry
                logger.error(
                    "❌ Redis validation FAILED - Data not confirmed in queue",
                    alarm_group_id=alarm_group_stored_id,
                    queue_name=queue_name,
                    validation_error=str(validation_error),
                    zadd_result=zadd_result,
                    expire_result=expire_result,
                )
                redis_success = False
                retry_count += 1
                continue

        except redis.RedisError as redis_err:
            retry_count += 1
            logger.warning(
                "Redis operation failed during alarm group queue storage, retrying",
                attempt=retry_count,
                max_retries=max_retries,
                error=str(redis_err),
                alarm_group_id=alarm_group_stored_id,
                queue_name=queue_name,
            )
            if retry_count >= max_retries:
                logger.error(
                    "Failed to store alarm group in Redis queue after retries - DATA LOSS RISK",
                    alarm_group_id=alarm_group_stored_id,
                    queue_name=queue_name,
                    error=str(redis_err),
                    alarm_group_data=alarm_group_json,
                )
                raise  # Re-raise to ensure calling code is aware of the failure
        except Exception as e:
            retry_count += 1
            logger.warning(
                "Unexpected error during alarm group queue storage, retrying",
                attempt=retry_count,
                max_retries=max_retries,
                error=str(e),
                alarm_group_id=alarm_group_stored_id,
                queue_name=queue_name,
            )
            if retry_count >= max_retries:
                logger.error(
                    "Failed to store alarm group in Redis queue after retries - DATA LOSS RISK",
                    alarm_group_id=alarm_group_stored_id,
                    queue_name=queue_name,
                    error=str(e),
                    alarm_group_data=alarm_group_json,
                )
                raise  # Re-raise to ensure calling code is aware of the failure


def add_event_redis(
    alarm_group_stored_id: str,
    severity: Severity,
    tenant_id: str,
    camera_group_id: str,
):
    redis_success = False
    max_retries = 3
    retry_count = 0

    while not redis_success and retry_count < max_retries:
        try:
            with redis_client.pipeline() as pipe:
                severity_value = severity.value
                key = f"alarm_group:{camera_group_id}:{tenant_id}:{severity_value}"
                key_v1 = f"unique_alarm_group_id:{alarm_group_stored_id}"

                # Always use hset - it will create new or update existing
                pipe.hset(key, "alarm_group_id", alarm_group_stored_id)
                # Set expiry of 1 day
                pipe.expire(key, config.HAIE.VISION_GLOBAL_TTL)
                pipe.hset(key_v1, "alarm_group_id", alarm_group_stored_id)
                pipe.expire(key_v1, config.HAIE.VISION_GLOBAL_TTL)

                # store severity for alarm group_id
                key = f"alarm_group_severity:{alarm_group_stored_id}"
                pipe.hsetnx(key, "severity", severity_value)
                # Set expiry of 1 day
                pipe.expire(key, config.HAIE.VISION_GLOBAL_TTL)

                # Execute Redis pipeline
                pipe.execute()

                redis_success = True

        except redis.RedisError as redis_err:
            retry_count += 1
            logger.warning(
                "Redis operation failed, retrying",
                attempt=retry_count,
                max_retries=max_retries,
                error=str(redis_err),
            )
            if retry_count >= max_retries:
                logger.error(
                    "Redis operations failed after retries, MySQL changes remain",
                    error=str(redis_err),
                    alarm_group_id=alarm_group_stored_id,
                )


def store_alarm_group_mapping(
    camera_group_id: str,
    tenant_id: str,
    severity: Severity,
    alarm_group_id: str,
) -> bool:
    """
    Store alarm group mapping in Redis using HSETNX
    Returns True if successful (new key created), False if key exists or error occurs
    """
    try:
        key = f"alarm_group:{camera_group_id}:{tenant_id}:{severity.value}"

        # HSETNX returns 1 if field was set, 0 if field exists
        result = redis_client.hsetnx(key, "alarm_group_id", alarm_group_id)

        if not result:
            existing_value = redis_client.hget(key, "alarm_group_id")
            logger.warning(
                "Alarm group mapping already exists in Redis",
                key=key,
                existing_value=existing_value,
                attempted_value=alarm_group_id,
            )
            return False
        else:
            logger.debug(
                "Alarm group mapping stored in Redis",
                key=key,
                alarm_group_id=alarm_group_id,
            )
        return True
    except Exception as e:
        logger.error(
            "Failed to store alarm group mapping in Redis", error=str(e)
        )
        return False


def remove_alarm_group_mapping(
    group_id: str, severity: Severity, tenant_id: str
) -> None:
    key = f"alarm_group:{group_id}:{tenant_id}:{severity.value}"
    redis_client.hdel(key, "alarm_group_id")


def add_detection_event_to_redis(
    event: DetectionEvent, alarm_group_id: str
) -> None:
    # Create key with proper string formatting (no ${} syntax)
    key = f"alarm_events:{alarm_group_id}:{event.group_id}:{event.tenant_id}:{event.severity.value}"
    key_v1 = f"alarm_group_events:{alarm_group_id}:{event.group_id}:{event.tenant_id}"

    # Use event_timestamp_utc directly as score (without converting to timestamp)
    score = event.timestamp_utc.timestamp()  # Convert to Unix timestamp

    # Serialize the entire event object to JSON (not just event_details_json)
    value = json.dumps(event_details_json(event))

    # Add retry logic to prevent data loss
    redis_success = False
    max_retries = 3
    retry_count = 0

    while not redis_success and retry_count < max_retries:
        try:
            # Use Redis pipeline for atomic operations
            with redis_client.pipeline() as pipe:
                # Add to sorted set and set expiry atomically
                pipe.zadd(key, {value: score})
                pipe.expire(key, config.HAIE.VISION_GLOBAL_TTL)
                pipe.zadd(key_v1, {value: score})
                pipe.expire(key_v1, config.HAIE.VISION_GLOBAL_TTL)
                # Execute all operations atomically
                results = pipe.execute()

            # Validate pipeline results
            zadd_result, expire_result, zadd_result_v1, expire_result_v1 = (
                results
            )
            if zadd_result == 0:
                logger.debug(
                    "Detection event already exists in Redis (zadd returned 0)",
                    alarm_group_id=alarm_group_id,
                    event_id=event.event_id,
                    key=key,
                )
            if expire_result == 0:
                logger.warning(
                    "Failed to set expiry on Redis key (key may not exist)",
                    alarm_group_id=alarm_group_id,
                    event_id=event.event_id,
                    key=key,
                )
            if zadd_result_v1 == 0:
                logger.debug(
                    "Detection event already exists in Redis (zadd returned 0)",
                    alarm_group_id=alarm_group_id,
                    event_id=event.event_id,
                    key=key_v1,
                )
            if expire_result_v1 == 0:
                logger.warning(
                    "Failed to set expiry on Redis key (key may not exist)",
                    alarm_group_id=alarm_group_id,
                    event_id=event.event_id,
                    key=key_v1,
                )

            redis_success = True
            logger.debug(
                "Detection event added to Redis",
                alarm_group_id=alarm_group_id,
                event_id=event.event_id,
                key=key,
                zadd_result=zadd_result,
                expire_result=expire_result,
            )

        except redis.RedisError as redis_err:
            retry_count += 1
            logger.warning(
                "Redis operation failed during detection event storage, retrying",
                attempt=retry_count,
                max_retries=max_retries,
                error=str(redis_err),
                alarm_group_id=alarm_group_id,
                event_id=event.event_id,
            )
            if retry_count >= max_retries:
                logger.error(
                    "Failed to store detection event in Redis after retries - DATA LOSS RISK",
                    alarm_group_id=alarm_group_id,
                    event_id=event.event_id,
                    error=str(redis_err),
                    key=key,
                )
                raise  # Re-raise to ensure calling code is aware of the failure
        except Exception as e:
            retry_count += 1
            logger.warning(
                "Unexpected error during detection event storage, retrying",
                attempt=retry_count,
                max_retries=max_retries,
                error=str(e),
                alarm_group_id=alarm_group_id,
                event_id=event.event_id,
            )
            if retry_count >= max_retries:
                logger.error(
                    "Failed to store detection event in Redis after retries - DATA LOSS RISK",
                    alarm_group_id=alarm_group_id,
                    event_id=event.event_id,
                    error=str(e),
                    key=key,
                )
                raise  # Re-raise to ensure calling code is aware of the failure


def publish_to_queue(
    alarm_group_stored_id: str,
    severity: Severity,
    tenant_id: str,
    camera_group_id: str,
) -> None:
    """
    Publishes the event to the tenant-specific queue based on its severity.
    """
    logger.debug(
        "Publishing event to queue",
        event_data=camera_group_id,
        alarm_group_id=alarm_group_stored_id,
    )
    alarm_group_id = alarm_group_stored_id
    # Determine tenant-specific queue based on severity
    if severity == Severity.HIGH:
        queue_name = f"tenant_{tenant_id}_high_severity_queue"
    else:
        # For LOW, MODERATE, and NOT_KNOWN severities
        queue_name = f"tenant_{tenant_id}_low_severity_queue"

    # Prepare event data
    event_data = {
        "alarm_group_id": alarm_group_id,
        "tenant_id": tenant_id,
        "camera_group_id": camera_group_id,
        "severity": severity,
    }

    # Serialize event data to JSON
    event_data_json = json.dumps(event_data)

    # Add retry logic to prevent data loss
    redis_success = False
    max_retries = 3
    retry_count = 0

    while not redis_success and retry_count < max_retries:
        try:
            # Use Redis pipeline for atomic operations
            with redis_client.pipeline() as pipe:
                # Add the event to the tenant-specific queue
                pipe.lpush(queue_name, event_data_json)
                # Add queue name to the tracking set
                pipe.sadd("active_queues", queue_name)
                # Execute all operations atomically
                results = pipe.execute()

            # Validate pipeline results
            lpush_result, sadd_result = results
            queue_length = (
                lpush_result  # lpush returns the new length of the list
            )

            # Update metrics with the actual queue length from lpush result
            VISION_ALARM_QUEUE_LENGTH.labels(queue_name=queue_name).set(
                queue_length
            )

            if sadd_result == 0:
                logger.debug(
                    "Queue name already in active_queues set",
                    queue_name=queue_name,
                    alarm_group_id=alarm_group_id,
                )

            # Log the queue length
            logger.info(
                "Published to queue",
                alarm_group_id=alarm_group_id,
                queue_name=queue_name,
                queue_length=queue_length,
                sadd_result=sadd_result,
            )
            redis_success = True

        except redis.RedisError as redis_err:
            retry_count += 1
            logger.warning(
                "Redis operation failed, retrying",
                attempt=retry_count,
                max_retries=max_retries,
                error=str(redis_err),
                alarm_group_id=alarm_group_id,
                queue_name=queue_name,
            )
            if retry_count >= max_retries:
                logger.error(
                    "Failed to publish to queue after retries - DATA LOSS RISK",
                    alarm_group_id=alarm_group_id,
                    queue_name=queue_name,
                    error=str(redis_err),
                    event_data=event_data_json,
                )
                # Consider implementing a fallback mechanism here:
                # - Write to a local file queue
                # - Send to a dead letter queue
                # - Store in database for later retry
                raise  # Re-raise to ensure calling code is aware of the failure
        except Exception as e:
            retry_count += 1
            logger.warning(
                "Unexpected error during queue publishing, retrying",
                attempt=retry_count,
                max_retries=max_retries,
                error=str(e),
                alarm_group_id=alarm_group_id,
                queue_name=queue_name,
            )
            if retry_count >= max_retries:
                logger.error(
                    "Failed to publish to queue after retries - DATA LOSS RISK",
                    alarm_group_id=alarm_group_id,
                    queue_name=queue_name,
                    error=str(e),
                    event_data=event_data_json,
                )
                raise  # Re-raise to ensure calling code is aware of the failure


def add_escalation_id_to_tenant(tenant_id: str, event_data: dict) -> None:
    # Add the alarm_group_id to the tenant's escalation set
    set_key = f"tenant:{tenant_id}:events"
    alarm_group_id = event_data["alarm_group_id"]
    json_event_data = json.dumps(event_data)
    redis_client.hset(set_key, alarm_group_id, json_event_data)


def add_escalation_id_to_operator(operator_id: str, event_data: dict) -> None:
    # Add the alarm_group_id to the tenant's escalation set
    set_key = f"operator:{operator_id}:escalations"
    alarm_group_id = event_data["alarm_group_id"]
    json_event_data = json.dumps(event_data)
    redis_client.hset(set_key, alarm_group_id, json_event_data)


def remove_escalation_id_from_tenant(tenant_id: str, event_data: dict) -> None:
    # Remove the alarm_group_id from the tenant's escalation set
    set_key = f"tenant:{tenant_id}:escalations"
    # Iterate through the escalation set for the given tenant_id
    redis_client.hdel(set_key, event_data["alarm_group_id"])


def remove_escalation_id_from_operator(
    operator_id: str, event_data: dict
) -> None:
    # Remove the alarm_group_id from the operator's escalation set
    set_key = f"operator:{operator_id}:escalations"
    redis_client.hdel(set_key, event_data["alarm_group_id"])


def marshal_alarm_group(event: DetectionEvent) -> AlarmGroup:
    id = str(uuid.uuid4())
    severity = event.severity
    severity = Severity.NOT_KNOWN
    state = AlarmGroupState.PENDING
    resolution = Resolution.OPEN
    start_time_utc = event.timestamp_utc
    camera_group_id = event.group_id
    tenant_id = event.tenant_id
    recommendation = AlarmGroupRecommendation.ANALYZING
    latest_event_time_utc = event.timestamp_utc
    current_time_stamp_utc = datetime.now(timezone.utc)
    alarm_group = AlarmGroup(
        id=id,
        severity=severity,
        state=state,
        resolution=resolution,
        start_time_utc=start_time_utc,
        camera_group_id=camera_group_id,
        tenant_id=tenant_id,
        recommendation=recommendation,
        latest_event_time_utc=latest_event_time_utc,
        alarm_group_queue_state=AlarmGroupQueueState.PENDING,
        alarm_group_queue_state_updated_at_utc=current_time_stamp_utc,
        created_at_utc=current_time_stamp_utc,
    )
    return alarm_group


def marshal_alarm_group_update(
    event: DetectionEvent, created_group_id: str, event_id: str
) -> AlarmGroupUpdate:
    id = str(uuid.uuid4())
    event_type_enum = EventType.DETECTIONEVENT
    event_details = {
        "event_id": event_id,
    }
    return AlarmGroupUpdate(
        id=id,
        alarm_group_id=created_group_id,
        event_type=event_type_enum,
        event_details=event_details,
    )


def marshal_alarm_group_update_cutoff(
    created_group_id: str,
    cutoff_alarm_group_id: str,
    camera_group_id: str,
    tenant_id: str,
) -> AlarmGroupUpdate:
    id = str(uuid.uuid4())
    event_type_enum = EventType.ALARM_GROUP_CUTOFF
    event_details = {
        "created_group_id": created_group_id,
        "camera_group_id": camera_group_id,
        "tenant_id": tenant_id,
    }
    return AlarmGroupUpdate(
        id=id,
        alarm_group_id=cutoff_alarm_group_id,
        event_type=event_type_enum,
        event_details=event_details,
    )


def marshal_event(event: DetectionEvent, created_group_id: str) -> Event:
    id = str(event.event_id)
    severity = Severity.NOT_KNOWN
    event_time_utc = event.timestamp_utc
    event_details = event_details_json(event)
    state = EventState.UNPROCESSED
    recommendation = EventRecommendation.NOT_KNOWN
    return Event(
        id=id,
        alarm_group_id=created_group_id,
        camera_group_id=event.group_id,
        camera_id=event.camera_id,
        event_details=event_details,
        tenant_id=event.tenant_id,
        severity=severity,
        event_time_utc=event_time_utc,
        state=state,
        recommendation=recommendation,
    )


def marshal_alarm_group_update_safe(
    event: SafeEvent, alarm_group_id: str
) -> AlarmGroupUpdate:
    id = str(uuid.uuid4())
    event_type_enum = EventType.SAFE
    event_details = {
        "resolution_comment": event.resolution_comment,
        "operator_id": event.operator_id,
        "timestamp_utc": event.timestamp_utc.isoformat(),
    }
    return AlarmGroupUpdate(
        id=id,
        alarm_group_id=alarm_group_id,
        event_type=event_type_enum,
        event_details=event_details,
    )


def marshal_alarm_group_update_auto_resolved(
    event: AutoResolvedEvent, alarm_group_id: str
) -> AlarmGroupUpdate:
    event_type_enum = EventType.AUTO_RESOLVED
    event_details = {
        "resolution_comment": event.resolution_comment,
        "operator_id": event.operator_id,
        "timestamp_utc": event.timestamp_utc.isoformat(),
    }
    return AlarmGroupUpdate(
        id=str(uuid.uuid4()),
        alarm_group_id=alarm_group_id,
        event_type=event_type_enum,
        event_details=event_details,
    )


def marshal_alarm_group_safe(
    event: SafeEvent, alarm_group: AlarmGroup
) -> AlarmGroup:
    alarm_group.state = event.state
    return alarm_group


def marshal_alarm_group_escalation_open(
    event: EscalationOpenEvent, alarm_group: AlarmGroup
) -> AlarmGroup:
    alarm_group.state = event.state
    return alarm_group


def marshal_alarm_group_update_escalation_open(
    event: EscalationOpenEvent, alarm_group_id: str
) -> AlarmGroupUpdate:
    event_type_enum = EventType.ESCALATION_OPEN
    return AlarmGroupUpdate(
        id=str(uuid.uuid4()),
        alarm_group_id=alarm_group_id,
        event_type=event_type_enum,
        event_details={
            "operator_id": event.operator_id,
            "start_time_utc": event.timestamp_utc.isoformat(),
            "resolution_comment": event.resolution_comment,
        },
    )


def marshal_alarm_group_escalation_close(
    event: EscalationCloseEvent, alarm_group: AlarmGroup
) -> AlarmGroup:
    alarm_group.state = event.state
    alarm_group.resolution = event.resolution
    return alarm_group


def marshal_alarm_group_update_escalation_close(
    event: EscalationCloseEvent, alarm_group_id: str
) -> AlarmGroupUpdate:
    return AlarmGroupUpdate(
        id=str(uuid.uuid4()),
        alarm_group_id=alarm_group_id,
        event_type=EventType.ESCALATION_CLOSE,
        event_details={
            "operator_id": event.operator_id,
            "end_time_utc": event.timestamp_utc.isoformat(),
            "resolution_comment": event.resolution_comment,
        },
    )


def marshal_alarm_group_allocation(
    event: AllocationEvent, alarm_group: AlarmGroup
) -> AlarmGroup:
    alarm_group.operator_id = event.operator_id
    return alarm_group


def marshal_alarm_group_update_allocation(
    event: AllocationEvent, alarm_group_id: str
) -> AlarmGroupUpdate:
    event_json = event.to_dict()
    return AlarmGroupUpdate(
        alarm_group_id=alarm_group_id,
        event_type=EventType.ALLOCATION,
        event_details=event_json,
    )


def marshal_alarm_group_orphan(
    event: OrphanEvent, alarm_group: AlarmGroup
) -> AlarmGroup:
    alarm_group.state = event.state
    alarm_group.operator_id = None  # Remove operator assignment
    return alarm_group


def marshal_alarm_group_update_orphan(
    operator_id: str, alarm_group_id: str, timestamp_utc: datetime
) -> AlarmGroupUpdate:
    event_details = {
        "operator_id": operator_id,
        "timestamp_utc": timestamp_utc.isoformat(),
    }
    return AlarmGroupUpdate(
        id=str(uuid.uuid4()),
        alarm_group_id=alarm_group_id,
        event_type=EventType.ORPHAN,
        event_details=event_details,
    )


def marshal_alarm_group_update_investigation(
    event: InvestigationEvent, alarm_group_id: str
) -> AlarmGroupUpdate:
    event_type_enum = EventType.INVESTIGATION
    event_details = {
        "operator_id": event.operator_id,
        "timestamp_utc": event.timestamp_utc.isoformat(),
    }
    return AlarmGroupUpdate(
        id=str(uuid.uuid4()),
        alarm_group_id=alarm_group_id,
        event_type=event_type_enum,
        event_details=event_details,
    )


def marshal_alarm_group_update_llm_alarm_group(
    event: LLMAlarmGroupEvent,
    summary: str,
    recommendation: str,
) -> AlarmGroupUpdate:
    event_type_enum = EventType.LLMALARMGROUPEVENT
    return AlarmGroupUpdate(
        id=str(uuid.uuid4()),
        alarm_group_id=event.alarm_group_id,
        event_type=event_type_enum,
        event_details={
            "summary": summary,
            "recommendation": recommendation,
        },
    )


def marshal_alarm_group_update_llm(
    event: LLMEvent, summary: str, recommendation: str, severity: Severity
) -> AlarmGroupUpdate:
    event_type_enum = EventType.LLMEVENT
    return AlarmGroupUpdate(
        id=str(uuid.uuid4()),
        alarm_group_id=event.alarm_group_id,
        event_type=event_type_enum,
        event_details={
            "event_id": event.id,
            "summary": summary,
            "recommendation": recommendation,
            "severity": severity.value,
        },
    )


def marshal_event_from_llm(event: LLMEvent) -> Event:
    id = event.id
    severity = event.severity
    if severity is None:
        severity = Severity.LOW
    event_time_utc = event.event_time_utc
    return Event(
        id=id,
        alarm_group_id=event.alarm_group_id,
        camera_group_id=event.camera_group_id,
        camera_id=event.camera_id,
        event_details=event.event_details,
        tenant_id=event.tenant_id,
        severity=severity,
        event_time_utc=event_time_utc,
    )


def get_cutoff_time_for_alarm_group(tenant_id: str) -> dict:
    tenant_alarm_processing_config = (
        ctrl_map.tenant.get_tenant_alarm_processing_config_by_id(tenant_id)
    )
    if tenant_alarm_processing_config is None:
        logger.error(
            "Tenant alarm processing config not found", tenant_id=tenant_id
        )
        return {}
    cutoff_time = tenant_alarm_processing_config.get("cutoffTime", {})
    if not cutoff_time:
        logger.debug("Cutoff time not found", tenant_id=tenant_id)
        return {}
    return cutoff_time


def get_llm_event_analyzer_config(tenant_id: str) -> Dict[str, Any]:
    tenant_alarm_processing_config = (
        ctrl_map.tenant.get_tenant_alarm_processing_config_by_id(tenant_id)
    )
    if tenant_alarm_processing_config is None:
        logger.error(
            "Tenant alarm processing config not found", tenant_id=tenant_id
        )
        return {}
    llm_event_analyzer_config = tenant_alarm_processing_config.get(
        "llmEventAnalyzerConfig", {}
    )
    if not llm_event_analyzer_config:
        logger.debug(
            "LLM event analyzer config not found",
            tenant_id=tenant_id,
            tenant_alarm_processing_config=tenant_alarm_processing_config,
        )
        return {}
    logger.debug(
        "LLM event analyzer config",
        tenant_id=tenant_id,
        llm_event_analyzer_config=llm_event_analyzer_config,
    )
    return llm_event_analyzer_config


def is_llm_event_analysis_enabled(tenant_id: str) -> bool:
    llm_event_analyzer_config = get_llm_event_analyzer_config(tenant_id)
    return llm_event_analyzer_config.get("enabled", False)


def is_auto_resolution_enabled(tenant_id: str) -> bool:
    llm_event_analyzer_config = get_llm_event_analyzer_config(tenant_id)
    return llm_event_analyzer_config.get("resolveAlarmGroup", False)


def update_redis(alarm_group_id: str, event_id: str, severity: Severity):
    event = ctrl_map.event.get_event(event_id=event_id)
    if (
        event is None
        or event.event_details is None
        or not isinstance(event.event_details, dict)
    ):
        logger.error(
            "Could not update redis",
            event_id=event_id,
            alarm_group_id=alarm_group_id,
        )
        return
    event_details = event.event_details
    if "timestamp_utc" in event_details:
        event_details["timestamp_utc"] = (
            datetime.fromisoformat(event_details["timestamp_utc"]).timestamp()
            * 1000
        )
    if "event_timestamp_utc" in event_details:
        event_details["event_timestamp_utc"] = (
            datetime.fromisoformat(
                event_details["event_timestamp_utc"]
            ).timestamp()
            * 1000
        )
    detection_event = DetectionEvent.from_dict(event_details)
    detection_event.severity = severity
    add_detection_event_to_redis(detection_event, alarm_group_id)
    broadcast_event(detection_event, alarm_group_id)


def marshal_alarm_group_update_queue_state(
    alarm_group_id: str,
    alarm_group_queue_state: AlarmGroupQueueState,
    time_stamp_utc: datetime,
) -> AlarmGroupUpdate:
    return AlarmGroupUpdate(
        id=str(uuid.uuid4()),
        alarm_group_id=alarm_group_id,
        event_type=EventType.QUEUE_STATE_UPDATE,
        event_details={
            "alarm_group_queue_state": alarm_group_queue_state.value,
            "alarm_group_queue_state_updated_at_utc": time_stamp_utc.isoformat(),
        },
    )


class LLMEventHandler(EventHandler):
    def handle_sync(self, event: LLMEvent) -> None:
        pass

    async def handle(self, event: LLMEvent) -> None:
        logger.debug(
            "Processing LLM event",
            event_id=event.id,
            alarm_group_id=event.alarm_group_id,
            camera_group_id=event.camera_group_id,
            tenant_id=event.tenant_id,
            severity=event.severity,
            is_created=event.is_created,
        )
        event_request = marshal_event_from_llm(event)
        alarm_group_stored_id = event_request.alarm_group_id
        try:
            summary, recommendation = await analyze_event(
                ctrl_map,
                event_request,
                save_to_db=True,
            )
            severity = Severity.HIGH
            if recommendation.lower() == EventRecommendation.RESOLVE.lower():
                severity = Severity.LOW

            # Create alarm group update entry
            alarm_group_update_request = marshal_alarm_group_update_llm(
                event, summary, recommendation, severity
            )
            alarm_group_update_controller = ctrl_map.alarm_group_update
            alarm_group_update_controller.update_alarm_group(
                alarm_group_update_request
            )

            if summary == ERROR_STRING:
                logger.error(
                    "LLM Event Anaylsis Failed",
                    event_id=event_request.id,
                    alarm_group_id=alarm_group_stored_id,
                    error=recommendation,
                )
                VISION_LLM_EVENTS_PROCESSED_TOTAL.labels(
                    tenant_id=event_request.tenant_id,
                    camera_group_id=event_request.camera_group_id,
                    camera_id=event_request.camera_id,
                    success=False,
                    reason=recommendation,
                ).inc()
                # Update event state and recommendation in events table
                ctrl_map.event.update_event_state_severity_recommendation(
                    event_request.id,
                    EventState.ERROR,
                    EventRecommendation("error"),
                    severity,
                )
            else:
                VISION_LLM_EVENTS_PROCESSED_TOTAL.labels(
                    tenant_id=event_request.tenant_id,
                    camera_group_id=event_request.camera_group_id,
                    camera_id=event_request.camera_id,
                    success=True,
                    reason="",
                ).inc()
                # Update event state and recommendation in events table
                ctrl_map.event.update_event_state_severity_recommendation(
                    event_request.id,
                    EventState.PROCESSED,
                    EventRecommendation.from_str(recommendation),
                    severity,
                )
            # update_redis(alarm_group_stored_id, event.id, severity)
            try:
                # Publish to LLMAlarmGroupEvent SQS for alarm group analysis
                event_dict = event_to_llm_alarm_group_event_dict(
                    event_request, event.is_created
                )
                event_request_str = json.dumps(event_dict)
                publisher_config = PublisherConfig(
                    queue_url=config.HAIE.LLM_ALARM_GROUP_QUEUE_URL,
                    aws_region=config.HAIE.AWS_S3_REGION,
                )
                sync_publisher = SyncSQSPublisherSingleton.get_instance(
                    publisher_config
                )
                sync_publisher.publish_message(
                    event_request_str,
                    message_attributes={
                        "eventType": {
                            "DataType": "String",
                            "StringValue": "LLMAlarmGroupEvent",
                        }
                    },
                    message_group_id=alarm_group_stored_id,
                )
            except Exception as e:
                logger.error(
                    "Error publishing message to LLM Alarm Group Event Queue",
                    error=str(e),
                    event_request=str(event_request),
                    event_id=event_request.id,
                    alarm_group_id=alarm_group_stored_id,
                )
            if summary == ERROR_STRING:
                # Send to DLQ
                raise Exception(f"LLM Event Analysis Failed: {recommendation}")
        except Exception as e:
            logger.error(
                "Exception in LLM Event Analyzer",
                error=e,
                event_request=str(event_request),
                event_id=event_request.id,
                alarm_group_id=alarm_group_stored_id,
            )
            raise e


class LLMAlarmGroupEventHandler(EventHandler):
    def handle_sync(self, event: LLMAlarmGroupEvent) -> None:
        pass

    async def handle(self, event: LLMAlarmGroupEvent) -> None:
        logger.debug(
            "Processing LLM Alarm Group event",
            alarm_group_id=event.alarm_group_id,
            camera_group_id=event.camera_group_id,
            tenant_id=event.tenant_id,
            is_created=event.is_created,
        )
        alarm_group_stored_id = event.alarm_group_id
        try:
            (
                summary,
                recommendation,
                explanation,
                events_used_for_analysis,
            ) = await analyze_alarm_group(
                ctrl_map,
                alarm_group_stored_id,
                reeval_events=False,
                save_to_db=True,
                events_per_alarm_group_limit=200,
            )

            # Create alarm group update entry
            alarm_group_update_request = (
                marshal_alarm_group_update_llm_alarm_group(
                    event, summary, recommendation
                )
            )
            alarm_group_update_controller = ctrl_map.alarm_group_update
            alarm_group_update_controller.update_alarm_group(
                alarm_group_update_request
            )

            if summary == ERROR_STRING:
                logger.error(
                    "LLM Alarm Group Analysis Failed",
                    alarm_group_id=alarm_group_stored_id,
                    error=explanation,
                )
                VISION_LLM_ALARM_GROUPS_PROCESSED_TOTAL.labels(
                    tenant_id=event.tenant_id,
                    camera_group_id=event.camera_group_id,
                    success=False,
                    reason=explanation,
                ).inc()
                # TODO: Can force escalation here unless all events have recommendation as resolve
                raise Exception(
                    f"LLM Alarm Group Analysis Failed: {explanation}"
                )
            VISION_LLM_ALARM_GROUPS_PROCESSED_TOTAL.labels(
                tenant_id=event.tenant_id,
                camera_group_id=event.camera_group_id,
                success=True,
                reason="",
            ).inc()
            logger.debug(
                "LLM Analysis Completed",
                alarm_group_id=alarm_group_stored_id,
                summary=summary,
                recommendation=recommendation,
                explanation=explanation,
            )
            # Store recommendation in alarm group table
            alarm_group = ctrl_map.alarm_group.get_alarm_group_by_id(
                alarm_group_stored_id
            )
            assert alarm_group is not None
            new_ag_recommendation = AlarmGroupRecommendation.from_str(
                recommendation
            )

            if new_ag_recommendation != alarm_group.recommendation:
                # Don't update recommendation if it's already escalate or escalate_zero_tolerance
                if alarm_group.recommendation in [
                    AlarmGroupRecommendation.ESCALATE,
                    AlarmGroupRecommendation.ESCALATE_ZERO_TOLERANCE,
                ]:
                    # Force the new recommendation to be the same as the old recommendation
                    new_ag_recommendation = alarm_group.recommendation
                    VISION_LLM_ALARM_GROUP_FORCED_ESCALATION.labels(
                        tenant_id=event.tenant_id,
                        camera_group_id=event.camera_group_id,
                        alarm_group_id=alarm_group_stored_id,
                        reason=f"{alarm_group.recommendation} recommended earlier",
                    ).inc()
                else:
                    # Update the recommendation in the alarm group table
                    ctrl_map.alarm_group.update_alarm_group_recommendation(
                        alarm_group_stored_id, new_ag_recommendation
                    )

            if (
                new_ag_recommendation == AlarmGroupRecommendation.RESOLVE
                and alarm_group.state == AlarmGroupState.PENDING_CUTOFF
                and is_auto_resolution_enabled(event.tenant_id)
            ):
                # Resolve alarm group if all events have been processed

                # Verify that all events have been processed and used in this evaluation
                events = ctrl_map.event.get_all_events_for_alarm_group(
                    alarm_group_stored_id, limit=200, order_by="desc"
                )
                event_states = [event.state for event in events]
                all_events_processed = True
                if any(
                    event_state != EventState.PROCESSED
                    for event_state in event_states
                ):
                    # All events are not successfully processed
                    all_events_processed = False
                    if all(
                        event_state in [EventState.ERROR, EventState.PROCESSED]
                        for event_state in event_states
                    ):
                        # All events processed, but some events have errors in analysis
                        # We force escalation here
                        new_ag_recommendation = (
                            AlarmGroupRecommendation.ESCALATE
                        )
                        ctrl_map.alarm_group.update_alarm_group_recommendation(
                            alarm_group_stored_id,
                            new_ag_recommendation,
                        )
                        VISION_LLM_ALARM_GROUP_FORCED_ESCALATION.labels(
                            tenant_id=event.tenant_id,
                            camera_group_id=event.camera_group_id,
                            alarm_group_id=alarm_group_stored_id,
                            reason="All events processed, some have errors, escalating",
                        ).inc()

                all_events_used_in_analysis = set(
                    events_used_for_analysis
                ) == set([event.id for event in events])

                if all_events_processed and all_events_used_in_analysis:
                    # Publish AutoResolvedEvent to Event SQS
                    try:
                        # Publish to Event SQS for alarm group analysis
                        event_dict = create_auto_resolved_event_dict(
                            event_type=EventType.AUTO_RESOLVED,
                            alarm_group_id=alarm_group_stored_id,
                            camera_group_id=event.camera_group_id,
                            timestamp_utc=int(
                                datetime.now(tz=timezone.utc).timestamp()
                                * 1000
                            ),
                            tenant_id=event.tenant_id,
                            operator_id="Hakimo AI Operator",
                            resolution_comment=summary,
                        )
                        event_request_str = json.dumps(event_dict)
                        publisher_config = PublisherConfig(
                            queue_url=config.HAIE.EVENT_QUEUE_URL,
                            aws_region=config.HAIE.AWS_S3_REGION,
                        )
                        sync_publisher = (
                            SyncSQSPublisherSingleton.get_instance(
                                publisher_config
                            )
                        )
                        sync_publisher.publish_message(
                            event_request_str,
                            message_attributes={
                                "eventType": {
                                    "DataType": "String",
                                    "StringValue": "AutoResolvedEvent",
                                }
                            },
                            message_group_id=alarm_group_stored_id,
                        )
                    except Exception as e:
                        logger.error(
                            "Error publishing AutoResolvedEvent to Event Queue",
                            error=e,
                            event_request=event_dict,
                            alarm_group_id=alarm_group_stored_id,
                        )

            # Store summary and recommendation in Redis
            try:
                # Use Redis pipeline for atomic operations
                pipe = redis_client.pipeline()
                summary_and_recommendation_key = f"alarm_group_summary_and_recommendation:{alarm_group_stored_id}"
                data = {
                    "summary": summary,
                    "recommendation": new_ag_recommendation.value,
                }
                pipe.set(
                    summary_and_recommendation_key, json.dumps(data), ex=86400
                )
                pipe.execute()

                # Publish notification about LLM analysis completion
                notification_data = {
                    "alarm_group_id": alarm_group_stored_id,
                    "camera_group_id": event.camera_group_id,
                    "tenant_id": event.tenant_id,
                    "type": "llm-output-ready",
                    "timestamp": datetime.now(tz=timezone.utc).isoformat(),
                }
                redis_client.publish(
                    "llm-output-ready", json.dumps(notification_data)
                )

                logger.debug(
                    "LLM analysis results stored in Redis and notification published",
                    alarm_group_id=alarm_group_stored_id,
                )
            except redis.RedisError as redis_err:
                logger.error(
                    "Failed to store LLM analysis results in Redis",
                    error=str(redis_err),
                    alarm_group_id=alarm_group_stored_id,
                )
        except Exception as e:
            logger.error(
                "Exception in LLM Alarm Group Event Analyzer",
                error=e,
                event_object=str(event),
                alarm_group_id=alarm_group_stored_id,
            )
            raise e


class DetectionEventHandler(EventHandler):
    def handle_sync(self, event: DetectionEvent) -> None:
        pass

    async def handle(self, event: DetectionEvent) -> None:
        try:
            logger.debug(
                "Processing detection event",
                detection_event_group_id=event.group_id,
                tenant_id=event.tenant_id,
            )

            # One-time reset of negative gauges after deployment
            one_time_reset_negatives()

            # Check if event is marked as duplicate in objects
            is_duplicate = safety_checker.check_all_above_threshold(event)
            if is_duplicate:
                """
                1. Event: P1
                2. Threshold: 0
                3. AlarmGropu -> alarm_group_id
                4. AlarmGroupUpdate -> DetectionEvent
                5. Event -> event
                6. alarm_group_id -> safe
                7. Threshold: P1 -> 1
                8. Event: P1
                9. Step from 2 to 7
                10. Event: p1
                """
                # TODO: add buisness logic for dedup
                event.is_dedup = is_duplicate
                logger.info(
                    "Event is marked as duplicate in objects",
                    event_id=event.event_id,
                    group_id=event.group_id,
                )

            # Start MySQL transaction - use the correct transaction method
            try:
                # Step 1: MySQL database operations
                if event.group_id == event.camera_id or event.group_id is None:
                    # Sometimes appliance sends group_id as camera_id
                    logger.info(
                        "[RARE] Camera id is same as camera group id or group_id is missing",
                        camera_id=event.camera_id,
                        camera_group_id=event.group_id,
                        tenant_id=event.tenant_id,
                    )
                    camera_info = get_camera_info(event.camera_id)
                    camera_group_id = camera_info["camera_group_id"]
                    event.group_id = camera_group_id
                    logger.info(
                        "[RARE] Camera group id is updated",
                        camera_id=event.camera_id,
                        camera_group_id=camera_group_id,
                        tenant_id=event.tenant_id,
                    )
                alarm_group_controller = ctrl_map.alarm_group
                alarm_group = marshal_alarm_group(event)
                cutoff_time = get_cutoff_time_for_alarm_group(event.tenant_id)
                (
                    alarm_group_stored_id,
                    created,
                    pending_cutoff_alarm_group_id,
                ) = alarm_group_controller.create_alarm_group_v1(
                    alarm_group, cutoff_time
                )

                if not alarm_group_stored_id:
                    logger.error(
                        "Alarm group not created for detection event",
                        detection_event=event,
                    )
                    return
                # Step 2: Create event and alarm_group_update in MySQL
                event_controller = ctrl_map.event
                event_request = marshal_event(event, alarm_group_stored_id)
                event_id, _ = event_controller.create_event(event_request)

                alarm_group_update_controller = ctrl_map.alarm_group_update
                alarm_group_update_request = marshal_alarm_group_update(
                    event, alarm_group_stored_id, event_id
                )
                alarm_group_update_controller.update_alarm_group(
                    alarm_group_update_request
                )
                if pending_cutoff_alarm_group_id:
                    # audit the cutoff alarm group
                    alarm_group_update_request = (
                        marshal_alarm_group_update_cutoff(
                            alarm_group_stored_id,
                            pending_cutoff_alarm_group_id,
                            event.group_id,
                            event.tenant_id,
                        )
                    )
                    alarm_group_update_controller.update_alarm_group(
                        alarm_group_update_request
                    )

                add_detection_event_to_redis(event, alarm_group_stored_id)
                broadcast_event(event, alarm_group_stored_id)
                add_event_redis(
                    alarm_group_stored_id,
                    event.severity,
                    event.tenant_id,
                    event.group_id,
                )
                if created:
                    logger.info(
                        "created new alarm",
                        tenant_id=event.tenant_id,
                        camera_group_id=event.group_id,
                    )
                    VISION_ALARM_COUNT.labels(
                        tenant_id=event.tenant_id,
                        camera_group_id=event.group_id,
                    ).inc()

                    # Track pending allocation for new alarms
                    safe_gauge_inc(
                        VISION_ALARMS_PENDING_ALLOCATION,
                        {
                            "tenant_id": event.tenant_id,
                            "camera_group_id": event.group_id,
                        },
                    )

                    # Track alarm aging (starts at 0 when created)
                    VISION_ALARMS_AGING_MINUTES.labels(
                        tenant_id=event.tenant_id,
                        camera_group_id=event.group_id,
                        state="pending",
                    ).observe(0)

                try:
                    if is_llm_event_analysis_enabled(event_request.tenant_id):
                        event_dict = event_to_dict(event_request, created)
                        event_request_str = json.dumps(event_dict)
                        publisher_config = PublisherConfig(
                            queue_url=config.HAIE.LLM_EVENT_QUEUE_URL,
                            aws_region=config.HAIE.AWS_S3_REGION,
                        )
                        sync_publisher = (
                            SyncSQSPublisherSingleton.get_instance(
                                publisher_config
                            )
                        )
                        sync_publisher.publish_message(
                            event_request_str,
                            message_attributes={
                                "eventType": {
                                    "DataType": "String",
                                    "StringValue": "LLMEvent",
                                }
                            },
                            message_group_id=event_request.id,
                        )

                        # If auto resolution is enabled and an alarm group's state was updated to pending_cutoff,
                        # then publish to LLMAlarmGroupEvent SQS to trigger alarm group analysis and resolve if required
                        if (
                            is_auto_resolution_enabled(event_request.tenant_id)
                            and pending_cutoff_alarm_group_id
                        ):
                            ag_event_dict = (
                                event_to_llm_alarm_group_event_dict(
                                    event_request, is_created=created
                                )
                            )
                            ag_event_dict["alarm_group_id"] = (
                                pending_cutoff_alarm_group_id
                            )
                            ag_event_request_str = json.dumps(ag_event_dict)
                            logger.info(
                                "Publishing to LLMAlarmGroupEvent SQS from DetectionEventHandler due to cutoff",
                                event_id=event_request.id,
                                cutoff_alarm_group_id=pending_cutoff_alarm_group_id,
                                tenant_id=event_request.tenant_id,
                                new_alarm_group_id=alarm_group_stored_id,
                            )
                            ag_publisher_config = PublisherConfig(
                                queue_url=config.HAIE.LLM_ALARM_GROUP_QUEUE_URL,
                                aws_region=config.HAIE.AWS_S3_REGION,
                            )
                            ag_sync_publisher = SyncSQSPublisher(
                                ag_publisher_config
                            )
                            ag_sync_publisher.publish_message(
                                ag_event_request_str,
                                message_attributes={
                                    "eventType": {
                                        "DataType": "String",
                                        "StringValue": "LLMAlarmGroupEvent",
                                    }
                                },
                                message_group_id=pending_cutoff_alarm_group_id,
                            )
                except Exception as e:
                    logger.error(
                        "Error publishing message to LLM Queue",
                        error=e,
                        event_request=str(event_request),
                        event_id=event_request.id,
                        alarm_group_id=alarm_group_stored_id,
                        cutoff_alarm_group_id=pending_cutoff_alarm_group_id,
                    )
            except Exception as mysql_err:
                logger.error(
                    "Error during MySQL operations for detection event",
                    error=str(mysql_err),
                    detection_event=event,
                )
                raise

        except Exception as e:
            logger.error(
                "Unhandled exception in detection event handler",
                error=str(e),
                detection_event=event,
            )
            return


class AutoResolvedEventHandler(EventHandler):
    def handle_sync(self, event: AutoResolvedEvent) -> None:
        pass

    async def handle(self, event: AutoResolvedEvent) -> None:
        try:
            logger.debug(
                "Processing auto resolved event", auto_resolved_event=event
            )
            # TODO: implement the logic to AutoResolve
            alarm_group_controller = ctrl_map.alarm_group
            alarm_group_id = event.site_alarm_id
            if not alarm_group_id:
                logger.error(
                    "Alarm group not found for auto resolved event",
                    auto_resolved_event=event,
                )
                return
            alarm_group_controller.update_alarm_group_safe_event(
                alarm_group_id,
                Resolution.SAFE,
                event.resolution_comment,
                event.timestamp_utc,
                event.operator_id,
                AlarmGroupState.RESOLVED_BY_HAKIMO,
            )
            alarm_group_update_controller = ctrl_map.alarm_group_update
            alarm_group_update_request = (
                marshal_alarm_group_update_auto_resolved(event, alarm_group_id)
            )
            alarm_group_update_controller.update_alarm_group(
                alarm_group_update_request
            )
            VISION_LLM_ALARM_GROUP_AUTO_RESOLVED.labels(
                tenant_id=event.tenant_id,
                camera_group_id=event.group_id,
                alarm_group_id=alarm_group_id,
            ).inc()
        except Exception as e:
            logger.error(
                "Unhandled exception in auto resolved event handler",
                error=str(e),
                auto_resolved_event=event,
            )
            return


class SafeEventHandler(EventHandler):
    def handle_sync(self, event: SafeEvent) -> None:
        pass

    async def handle(self, event: SafeEvent) -> None:
        try:
            logger.debug("Processing safe event", safe_event=event)

            try:
                # Step 1: Get alarm group information
                alarm_group_controller = ctrl_map.alarm_group
                alarm_group_id = event.site_alarm_id

                if not alarm_group_id:
                    logger.error(
                        "Alarm group id cannot be None",
                        safe_event=event,
                    )
                    # TODO: add the logic to get the alarm group id from the db for the given camera_group_id and operator_id
                    return

                # Step 2: Update alarm group with safe event
                alarm_group_controller.update_alarm_group_safe_event(
                    alarm_group_id,
                    Resolution.SAFE,
                    event.resolution_comment,
                    event.timestamp_utc,
                    event.operator_id,
                    AlarmGroupState.RESOLVED_BY_OPERATOR,
                )

                # Step 3: Create alarm group update entry
                alarm_group_update_request = marshal_alarm_group_update_safe(
                    event, alarm_group_id
                )
                alarm_group_update_controller = ctrl_map.alarm_group_update
                alarm_group_update_controller.update_alarm_group(
                    alarm_group_update_request
                )

                # Emit operator management metrics
                VISION_OPERATOR_ALARMS_RESOLVED_TOTAL.labels(
                    operator_id=event.operator_id,
                    tenant_id=event.tenant_id,
                    resolution_type="safe",
                ).inc()

                # Decrease active alarms count for this operator (prevent negative)
                if event.operator_id:
                    # Use safe_gauge_dec helper to prevent negative values
                    safe_gauge_dec(
                        VISION_OPERATOR_ACTIVE_ALARMS,
                        {
                            "operator_id": event.operator_id,
                            "tenant_id": event.tenant_id,
                        },
                    )

                # Calculate and record resolution duration
                # Get alarm group to find start time
                start_time_utc = alarm_group_controller.get_start_time_utc(
                    alarm_group_id
                )
                if start_time_utc:
                    # Ensure both datetimes are timezone-aware for proper subtraction
                    if start_time_utc.tzinfo is None:
                        # Make start_time_utc timezone-aware (assuming it's UTC)
                        start_time_utc = start_time_utc.replace(
                            tzinfo=timezone.utc
                        )

                    resolution_duration = (
                        event.timestamp_utc - start_time_utc
                    ).total_seconds()
                    VISION_ALARM_RESOLUTION_DURATION_SECONDS.labels(
                        operator_id=event.operator_id,
                        tenant_id=event.tenant_id,
                        resolution_type="safe",
                    ).observe(resolution_duration)

                    # If there's allocation time available, calculate allocation-to-resolution duration
                    # Note: This would require storing allocation timestamp, which might need DB schema changes
                    # For now, we'll use the same duration as a placeholder
                    VISION_ALARM_ALLOCATION_TO_RESOLUTION_DURATION_SECONDS.labels(
                        operator_id=event.operator_id,
                        tenant_id=event.tenant_id,
                        resolution_type="safe",
                    ).observe(resolution_duration)

                # Decrease pending allocation count (prevent negative)
                safe_gauge_dec(
                    VISION_ALARMS_PENDING_ALLOCATION,
                    {
                        "tenant_id": event.tenant_id,
                        "camera_group_id": event.group_id,
                    },
                )
            except Exception as mysql_err:
                logger.error(
                    "Error during MySQL operations for safe event",
                    error=str(mysql_err),
                    safe_event=event,
                )
                raise

        except Exception as e:
            logger.error(
                "Unhandled exception in safe event handler",
                error=str(e),
                safe_event=event,
            )


class EscalationOpenHandler(EventHandler):
    def handle_sync(self, event: EscalationOpenEvent) -> None:
        pass

    async def handle(self, event: EscalationOpenEvent) -> None:
        try:
            logger.debug(
                "Processing escalation open event", escalation_open=event
            )

            try:
                # Step 1: Get alarm group information
                alarm_group_controller = ctrl_map.alarm_group
                alarm_group_id = event.site_alarm_id

                if not alarm_group_id:
                    logger.error(
                        "Alarm group id cannot be None",
                        escalation_open_event=event,
                    )
                    # TODO: add the logic to get the alarm group id from the db for the given camera_group_id and operator_id
                    return

                # Step 2: Update alarm group with escalation open event
                alarm_group_controller.update_alarm_group_escalation_open_event(
                    alarm_group_id, event.timestamp_utc, event.operator_id
                )

                # Step 3: Create alarm group update entry
                alarm_group_update_controller = ctrl_map.alarm_group_update
                alarm_group_update_request = (
                    marshal_alarm_group_update_escalation_open(
                        event, alarm_group_id
                    )
                )
                alarm_group_update_controller.update_alarm_group(
                    alarm_group_update_request
                )

                # Emit operator management metrics
                VISION_OPERATOR_ESCALATIONS_CREATED_TOTAL.labels(
                    operator_id=event.operator_id,
                    tenant_id=event.tenant_id,
                ).inc()

                # Increase active escalations count for this operator
                safe_gauge_inc(
                    VISION_OPERATOR_ACTIVE_ESCALATIONS,
                    {
                        "operator_id": event.operator_id,
                        "tenant_id": event.tenant_id,
                    },
                )
            except Exception as mysql_err:
                logger.error(
                    "Error during MySQL operations for escalation open event",
                    error=str(mysql_err),
                    escalation_open_event=event,
                )
                raise

        except Exception as e:
            logger.error(
                "Unhandled exception in escalation open handler",
                error=str(e),
                escalation_open_event=event,
            )


class EscalationCloseHandler(EventHandler):
    def handle_sync(self, event: EscalationCloseEvent) -> None:
        pass

    async def handle(self, event: EscalationCloseEvent) -> None:
        try:
            logger.debug(
                "Processing escalation close event",
                escalation_close_event=event,
            )

            try:
                # Step 1: Get alarm group information
                alarm_group_controller = ctrl_map.alarm_group
                alarm_group_id = event.site_alarm_id

                if not alarm_group_id:
                    logger.error(
                        "Alarm group id cannot be None",
                        escalation_close_event=event,
                    )
                    # TODO: add the logic to get the alarm group id from the db for the given camera_group_id and operator_id
                    return

                # Step 2: Update alarm group with escalation close event
                alarm_group_controller.update_alarm_group_escalation_close_event(
                    alarm_group_id,
                    event.timestamp_utc,
                    event.operator_id,
                    event.resolution_comment,
                    AlarmGroupState.RESOLVED_BY_OPERATOR,
                )

                # Step 3: Create alarm group update entry
                alarm_group_update_controller = ctrl_map.alarm_group_update
                alarm_group_update_request = (
                    marshal_alarm_group_update_escalation_close(
                        event, alarm_group_id
                    )
                )
                alarm_group_update_controller.update_alarm_group(
                    alarm_group_update_request
                )

                # Emit operator management metrics
                VISION_OPERATOR_ESCALATIONS_CLOSED_TOTAL.labels(
                    operator_id=event.operator_id,
                    tenant_id=event.tenant_id,
                ).inc()

                # Safely decrease active escalations count for this operator
                safe_gauge_dec(
                    VISION_OPERATOR_ACTIVE_ESCALATIONS,
                    {
                        "operator_id": event.operator_id,
                        "tenant_id": event.tenant_id,
                    },
                )

                # Record escalation resolution metrics if this also resolves the alarm
                VISION_OPERATOR_ALARMS_RESOLVED_TOTAL.labels(
                    operator_id=event.operator_id,
                    tenant_id=event.tenant_id,
                    resolution_type="escalation_close",
                ).inc()

                # Also safely decrease active alarms if this closes the alarm
                safe_gauge_dec(
                    VISION_OPERATOR_ACTIVE_ALARMS,
                    {
                        "operator_id": event.operator_id,
                        "tenant_id": event.tenant_id,
                    },
                )

                # Safely decrease pending allocation count
                safe_gauge_dec(
                    VISION_ALARMS_PENDING_ALLOCATION,
                    {
                        "tenant_id": event.tenant_id,
                        "camera_group_id": event.group_id,
                    },
                )

                # Calculate and record resolution duration
                # Get alarm group to find start time
                start_time_utc = alarm_group_controller.get_start_time_utc(
                    alarm_group_id
                )
                if start_time_utc:
                    # Ensure both datetimes are timezone-aware for proper subtraction
                    if start_time_utc.tzinfo is None:
                        # Make start_time_utc timezone-aware (assuming it's UTC)
                        start_time_utc = start_time_utc.replace(
                            tzinfo=timezone.utc
                        )

                    resolution_duration = (
                        event.timestamp_utc - start_time_utc
                    ).total_seconds()
                    VISION_ALARM_RESOLUTION_DURATION_SECONDS.labels(
                        operator_id=event.operator_id,
                        tenant_id=event.tenant_id,
                        resolution_type="escalation_close",
                    ).observe(resolution_duration)

                    # If there's allocation time available, calculate allocation-to-resolution duration
                    # Note: This would require storing allocation timestamp, which might need DB schema changes
                    # For now, we'll use the same duration as a placeholder
                    VISION_ALARM_ALLOCATION_TO_RESOLUTION_DURATION_SECONDS.labels(
                        operator_id=event.operator_id,
                        tenant_id=event.tenant_id,
                        resolution_type="escalation_close",
                    ).observe(resolution_duration)
            except Exception as mysql_err:
                logger.error(
                    "Error during MySQL operations for escalation close event",
                    error=str(mysql_err),
                    escalation_close_event=event,
                )
                raise

        except Exception as e:
            logger.error(
                "Unhandled exception in escalation close handler",
                error=str(e),
                escalation_close_event=event,
            )


class AllocationEventHandler(EventHandler):
    def handle_sync(self, event: AllocationEvent) -> None:
        pass

    async def handle(self, event: AllocationEvent) -> None:
        try:
            logger.debug("Processing allocation event", allocation_event=event)

            try:
                # Step 1: Get alarm group information
                alarm_group_controller = ctrl_map.alarm_group
                alarm_group_id = event.site_alarm_id

                if not alarm_group_id:
                    logger.error(
                        "Alarm group id cannot be None",
                        allocation_event=event,
                    )
                    return

                # Step 2: Update alarm group with operator ID
                alarm_group_controller.update_alarm_group_operator_id(
                    alarm_group_id, event.operator_id
                )
                time_stamp_utc = datetime.now(timezone.utc)
                alarm_group_controller.update_alarm_groups_queue_state(
                    [alarm_group_id],
                    AlarmGroupQueueState.ASSIGNED,
                    time_stamp_utc,
                )

                # Step 3: Create alarm group update entry
                alarm_group_update_controller = ctrl_map.alarm_group_update
                alarm_group_update_request = (
                    marshal_alarm_group_update_allocation(
                        event, alarm_group_id
                    )
                )
                alarm_group_update_controller.update_alarm_group(
                    alarm_group_update_request
                )
                alarm_group_update_queue_state_request = (
                    marshal_alarm_group_update_queue_state(
                        alarm_group_id,
                        AlarmGroupQueueState.ASSIGNED,
                        time_stamp_utc,
                    )
                )
                alarm_group_update_controller.update_alarm_group(
                    alarm_group_update_queue_state_request
                )

                # Emit operator management metrics
                VISION_OPERATOR_ALARMS_ALLOCATED_TOTAL.labels(
                    operator_id=event.operator_id,
                    tenant_id=event.tenant_id,
                ).inc()

                # Increase active alarms count for this operator
                safe_gauge_inc(
                    VISION_OPERATOR_ACTIVE_ALARMS,
                    {
                        "operator_id": event.operator_id,
                        "tenant_id": event.tenant_id,
                    },
                )

                # Decrease pending allocation count since alarm is now allocated
                safe_gauge_dec(
                    VISION_ALARMS_PENDING_ALLOCATION,
                    {
                        "tenant_id": event.tenant_id,
                        "camera_group_id": event.group_id,
                    },
                )
            except Exception as mysql_err:
                logger.error(
                    "Error during MySQL operations for allocation event",
                    error=str(mysql_err),
                    allocation_event=event,
                )
                raise

        except Exception as e:
            logger.error(
                "Unhandled exception in allocation event handler",
                error=str(e),
                allocation_event=event,
            )


class OrphanEventHandler(EventHandler):
    def handle_sync(self, event: OrphanEvent) -> None:
        pass

    async def handle(self, event: OrphanEvent) -> None:
        logger.debug("Processing orphan event", orphan_event=event)
        alarm_group_controller = ctrl_map.alarm_group
        operator_id = event.operator_id

        pending_alarm_group_tuples = (
            alarm_group_controller.get_pending_alarm_group_ids_for_operator(
                operator_id
            )
        )
        pending_alarm_group_ids = [
            ag_id for ag_id, _, _ in pending_alarm_group_tuples
        ]

        alarm_group_controller.mark_alarm_groups_orphaned(
            pending_alarm_group_ids
        )
        time_stamp_utc = datetime.now(timezone.utc)
        alarm_group_controller.update_alarm_groups_queue_state(
            pending_alarm_group_ids,
            AlarmGroupQueueState.PENDING,
            time_stamp_utc,
        )

        alarm_group_update_controller = ctrl_map.alarm_group_update
        alarm_groups_update_request = [
            marshal_alarm_group_update_orphan(
                alarm_group_id=ag_id,
                operator_id=operator_id,
                timestamp_utc=event.timestamp_utc,
            )
            for ag_id in pending_alarm_group_ids
        ]
        alarm_group_update_controller.update_alarm_groups(
            alarm_groups_update_request
        )
        alarm_group_update_queue_state_requests = [
            marshal_alarm_group_update_queue_state(
                alarm_group_id=ag_id,
                alarm_group_queue_state=AlarmGroupQueueState.PENDING,
                time_stamp_utc=time_stamp_utc,
            )
            for ag_id in pending_alarm_group_ids
        ]
        alarm_group_update_controller.update_alarm_groups(
            alarm_group_update_queue_state_requests
        )

        # Emit operator management metrics
        for ag_id in pending_alarm_group_ids:
            # Get alarm details for metrics
            camera_group_id, tenant_id = (
                alarm_group_controller.get_camera_group_id_and_tenant_id_of_alarm_group(
                    ag_id
                )
            )
            if camera_group_id and tenant_id:
                VISION_ALARMS_ORPHANED_TOTAL.labels(
                    tenant_id=tenant_id,
                    camera_group_id=camera_group_id,
                ).inc()

                # Safely decrease active alarms count for this operator since alarms are orphaned
                safe_gauge_dec(
                    VISION_OPERATOR_ACTIVE_ALARMS,
                    {"operator_id": operator_id, "tenant_id": tenant_id},
                )

                # Increase pending allocation count since orphaned alarms need reallocation
                safe_gauge_inc(
                    VISION_ALARMS_PENDING_ALLOCATION,
                    {
                        "tenant_id": tenant_id,
                        "camera_group_id": camera_group_id,
                    },
                )

        logger.info(
            "alarm groups marked orphan",
            alarm_group_ids=pending_alarm_group_ids,
            operator_id=operator_id,
        )


class TwilioMessageEventHandler(EventHandler):
    def handle_sync(self, event: TwilioMessageEvent) -> None:
        pass

    async def handle(self, event: TwilioMessageEvent) -> None:
        try:
            logger.debug(
                "Processing twilio message event", twilio_message_event=event
            )

            try:
                # Add your twilio message business logic here
                # This is a placeholder for any actual business logic required

                # Redis operations with retry logic if needed
                redis_success = False
                max_retries = 3
                retry_count = 0

                while not redis_success and retry_count < max_retries:
                    try:
                        # Add any required Redis operations here

                        redis_success = True

                    except redis.RedisError as redis_err:
                        retry_count += 1
                        logger.warning(
                            "Redis operation failed, retrying",
                            attempt=retry_count,
                            max_retries=max_retries,
                            error=str(redis_err),
                        )
                        if retry_count >= max_retries:
                            logger.error(
                                "Redis operations failed after retries, MySQL changes remain",
                                error=str(redis_err),
                            )

            except Exception as mysql_err:
                logger.error(
                    "Error during MySQL operations for twilio message event",
                    error=str(mysql_err),
                    twilio_message_event=event,
                )
                raise

        except Exception as e:
            logger.error(
                "Unhandled exception in twilio message event handler",
                error=str(e),
                twilio_message_event=event,
            )


class TwilioCallEventHandler(EventHandler):
    def handle_sync(self, event: TwilioCallEvent) -> None:
        pass

    async def handle(self, event: TwilioCallEvent) -> None:
        try:
            logger.debug(
                "Processing twilio call event", twilio_call_event=event
            )

            try:
                # Add your twilio call business logic here
                # This is a placeholder for any actual business logic required

                # Redis operations with retry logic if needed
                redis_success = False
                max_retries = 3
                retry_count = 0

                while not redis_success and retry_count < max_retries:
                    try:
                        # Add any required Redis operations here

                        redis_success = True

                    except redis.RedisError as redis_err:
                        retry_count += 1
                        logger.warning(
                            "Redis operation failed, retrying",
                            attempt=retry_count,
                            max_retries=max_retries,
                            error=str(redis_err),
                        )
                        if retry_count >= max_retries:
                            logger.error(
                                "Redis operations failed after retries, MySQL changes remain",
                                error=str(redis_err),
                            )

            except Exception as mysql_err:
                logger.error(
                    "Error during MySQL operations for twilio call event",
                    error=str(mysql_err),
                    twilio_call_event=event,
                )
                raise

        except Exception as e:
            logger.error(
                "Unhandled exception in twilio call event handler",
                error=str(e),
                twilio_call_event=event,
            )


class TalkDownEventHandler(EventHandler):
    def handle_sync(self, event: TalkDownEvent) -> None:
        pass

    async def handle(self, event: TalkDownEvent) -> None:
        try:
            logger.debug("Processing talk down event", talkdown_event=event)

            try:
                # Add your talk down business logic here
                # This is a placeholder for any actual business logic required

                # Redis operations with retry logic if needed
                redis_success = False
                max_retries = 3
                retry_count = 0

                while not redis_success and retry_count < max_retries:
                    try:
                        # Add any required Redis operations here

                        redis_success = True

                    except redis.RedisError as redis_err:
                        retry_count += 1
                        logger.warning(
                            "Redis operation failed, retrying",
                            attempt=retry_count,
                            max_retries=max_retries,
                            error=str(redis_err),
                        )
                        if retry_count >= max_retries:
                            logger.error(
                                "Redis operations failed after retries, MySQL changes remain",
                                error=str(redis_err),
                            )

            except Exception as mysql_err:
                logger.error(
                    "Error during MySQL operations for talk down event",
                    error=str(mysql_err),
                    talkdown_event=event,
                )
                raise

        except Exception as e:
            logger.error(
                "Unhandled exception in talk down event handler",
                error=str(e),
                talkdown_event=event,
            )


class InvestigationEventHandler(EventHandler):
    def handle_sync(self, event: InvestigationEvent) -> None:
        pass

    async def handle(self, event: InvestigationEvent) -> None:
        logger.debug(
            "Processing investigation event",
            investigation_event=event.group_id,
        )
        # Add your investigation business logic here
        alarm_group_controller = ctrl_map.alarm_group
        alarm_group_id, _ = alarm_group_controller.get_active_alarm_group(
            event.group_id
        )
        if not alarm_group_id:
            logger.error(
                "Alarm group not found for investigation event",
                investigation_event=event,
            )
            return
        alarm_group_update_controller = ctrl_map.alarm_group_update
        alarm_group_update_request = marshal_alarm_group_update_investigation(
            event, alarm_group_id
        )
        alarm_group_update_controller.update_alarm_group(
            alarm_group_update_request
        )

        # Emit operator management metrics
        VISION_OPERATOR_INVESTIGATION_EVENTS_TOTAL.labels(
            operator_id=event.operator_id,
            tenant_id=event.tenant_id,
        ).inc()


class GetAlarmEventHandler(EventHandler):
    async def handle(self, event: GetAlarmEvent) -> None:
        pass

    def handle_sync(self, event: GetAlarmEvent) -> None:
        logger.debug("Processing get alarm event", get_alarm_event=event)
        try:
            tenant_ids = event.tenant_ids
            limit = event.limit
            if limit < 100:
                limit = 100
            if limit > 200:
                limit = 200
            order = event.order
            recommendation = event.recommendation
            pipeline = redis_client.pipeline()
            alarm_group_controller = ctrl_map.alarm_group
            alarm_groups_data = (
                alarm_group_controller.get_unallocated_or_orphan_alarm_groups(
                    tenant_ids, limit, order, recommendation
                )
            )

            queue_names = set()
            ids = [
                alarm_group["alarm_group_id"]
                for alarm_group in alarm_groups_data
            ]
            logger.info(
                "[PUSH_TO_SET] alarm_groups_data",
                length=len(alarm_groups_data),
                alarm_group_ids=ids,
            )

            for alarm_group in alarm_groups_data:
                # Convert datetime to timestamp (float) for Redis score
                timestamp_score = (
                    alarm_group["start_timestamp_utc"].timestamp()
                    if alarm_group["start_timestamp_utc"]
                    else time.time()
                )

                # Serialize the full alarm group data for storage
                alarm_group_json = {
                    "alarm_group_id": alarm_group["alarm_group_id"],
                    "tenant_id": alarm_group["tenant_id"],
                    "camera_group_id": alarm_group["camera_group_id"],
                    "start_timestamp_utc": timestamp_score,  # Store as timestamp for consistency
                }
                queue_name = "sequential_alarm_group_queue"
                # alarm_group_controller.mark_alarm_group_as_ready_to_be_allocated(
                #     alarm_group["alarm_group_id"]
                # )
                pipeline.zadd(
                    queue_name,
                    {json.dumps(alarm_group_json): timestamp_score},
                )
                # Track alarm groups added to set
                VISION_ALARM_GROUPS_RETRIEVED_TOTAL.labels(
                    camera_group_id=alarm_group["camera_group_id"]
                ).inc()
                queue_names.add(queue_name)
                pipeline.sadd("alarm_group_sets", queue_name)
            pipeline.execute()
            time_stamp_utc = datetime.now(timezone.utc)
            alarm_group_controller.update_alarm_groups_queue_state(
                ids, AlarmGroupQueueState.QUEUED, time_stamp_utc
            )
            alarm_group_update_controller = ctrl_map.alarm_group_update
            alarm_group_update_request = [
                marshal_alarm_group_update_queue_state(
                    alarm_group_id=alarm_group["alarm_group_id"],
                    alarm_group_queue_state=AlarmGroupQueueState.QUEUED,
                    time_stamp_utc=time_stamp_utc,
                )
                for alarm_group in alarm_groups_data
            ]
            alarm_group_update_controller.update_alarm_groups(
                alarm_group_update_request
            )

        except Exception as e:
            logger.error(
                "Error adding unallocated alarms to set", error=str(e)
            )


def event_to_dict(event: Event, is_created: bool = False) -> Dict[str, Any]:
    event_dict = {
        "id": event.id,
        "severity": (
            event.severity.value
            if hasattr(event.severity, "value")
            else str(event.severity)
        ),
        "event_time_utc": (
            event.event_time_utc.isoformat() if event.event_time_utc else None
        ),
        "event_details": event.event_details,
        "tenant_id": event.tenant_id,
        "camera_group_id": event.camera_group_id,
        "camera_id": event.camera_id,
        "alarm_group_id": event.alarm_group_id,
        "is_created": is_created,
    }
    return event_dict


def event_to_llm_alarm_group_event_dict(
    event: Event, is_created: bool = False
) -> Dict[str, Any]:
    return {
        "alarm_group_id": event.alarm_group_id,
        "tenant_id": event.tenant_id,
        "camera_group_id": event.camera_group_id,
        "is_created": is_created,
    }


def create_auto_resolved_event_dict(
    event_type: EventType,
    alarm_group_id: str,
    camera_group_id: str,
    timestamp_utc: int,
    tenant_id: str,
    operator_id: str,
    resolution_comment: str,
) -> Dict[str, Any]:
    return {
        "event_type": event_type,
        "site_alarm_id": alarm_group_id,
        "group_id": camera_group_id,
        "timestamp_utc": timestamp_utc,
        "operator_id": operator_id,
        "tenant_id": tenant_id,
        "resolution_comment": resolution_comment,
    }


def one_time_reset_negatives():
    """Reset any negative gauge values to 0 - call this once after deployment"""
    global _reset_done
    if not _reset_done:
        logger.info("Performing one-time reset of negative gauge values")

        # Reset all gauge metrics to 0 to clear any existing negative values
        try:
            # Get all current metric samples to find existing label combinations
            from prometheus_client import REGISTRY

            reset_count = 0

            # Reset operator active alarms
            for metric_family in REGISTRY.collect():
                if metric_family.name == "vision_operator_active_alarms":
                    for sample in metric_family.samples:
                        if sample.value != 0:  # Only reset if not already 0
                            VISION_OPERATOR_ACTIVE_ALARMS.labels(
                                **sample.labels
                            ).set(0)
                            logger.info(
                                "Reset vision_operator_active_alarms to 0",
                                labels=sample.labels,
                            )
                            reset_count += 1

                # Reset operator active escalations
                elif (
                    metric_family.name == "vision_operator_active_escalations"
                ):
                    for sample in metric_family.samples:
                        if sample.value != 0:  # Only reset if not already 0
                            VISION_OPERATOR_ACTIVE_ESCALATIONS.labels(
                                **sample.labels
                            ).set(0)
                            logger.info(
                                "Reset vision_operator_active_escalations to 0",
                                labels=sample.labels,
                            )
                            reset_count += 1

                # Reset pending allocation
                elif metric_family.name == "vision_alarms_pending_allocation":
                    for sample in metric_family.samples:
                        if sample.value != 0:  # Only reset if not already 0
                            VISION_ALARMS_PENDING_ALLOCATION.labels(
                                **sample.labels
                            ).set(0)
                            logger.info(
                                "Reset vision_alarms_pending_allocation to 0",
                                labels=sample.labels,
                            )
                            reset_count += 1

            logger.info(
                f"One-time gauge reset completed - {reset_count} metrics reset to 0"
            )

        except Exception as e:
            logger.warning("Failed to reset some gauges", error=str(e))
            # Fallback: Reset common gauge metrics to 0 manually
            try:
                # Reset some common operator/tenant combinations that might exist
                common_tenants = ["trinity-test", "trinity", "test", "demo"]
                common_operators = ["operator1", "operator2", "op1", "op2"]

                for tenant in common_tenants:
                    for operator in common_operators:
                        try:
                            VISION_OPERATOR_ACTIVE_ALARMS.labels(
                                operator_id=operator, tenant_id=tenant
                            ).set(0)
                            VISION_OPERATOR_ACTIVE_ESCALATIONS.labels(
                                operator_id=operator, tenant_id=tenant
                            ).set(0)
                        except:
                            pass

                logger.info("Fallback gauge reset completed")
            except Exception as fallback_error:
                logger.warning(
                    "Fallback gauge reset also failed",
                    error=str(fallback_error),
                )

        _reset_done = True


def reset_negative_gauges():
    """Advanced function to scan and reset negative gauge values - call manually if needed"""
    logger.info("Scanning and resetting negative gauge values")

    # Get all metric families and reset negative values
    from prometheus_client import REGISTRY

    for metric_family in REGISTRY.collect():
        if metric_family.type == "gauge":
            for sample in metric_family.samples:
                if sample.value < 0:
                    # Find the corresponding metric and reset it
                    metric_name = sample.name
                    labels = sample.labels

                    # Reset specific operator metrics if negative
                    if "vision_operator_active_alarms" in metric_name:
                        VISION_OPERATOR_ACTIVE_ALARMS.labels(**labels).set(0)
                        logger.info(
                            f"Reset negative {metric_name} to 0", labels=labels
                        )
                    elif "vision_operator_active_escalations" in metric_name:
                        VISION_OPERATOR_ACTIVE_ESCALATIONS.labels(
                            **labels
                        ).set(0)
                        logger.info(
                            f"Reset negative {metric_name} to 0", labels=labels
                        )
                    elif "vision_alarms_pending_allocation" in metric_name:
                        VISION_ALARMS_PENDING_ALLOCATION.labels(**labels).set(
                            0
                        )
                        logger.info(
                            f"Reset negative {metric_name} to 0", labels=labels
                        )
