from abc import ABC, abstractmethod
from typing import Union

from ..models.events import (
    AllocationEvent,
    DetectionEvent,
    EscalationCloseEvent,
    EscalationOpenEvent,
    OrphanEvent,
    SafeEvent,
    TalkDownEvent,
    TwilioCallEvent,
    TwilioMessageEvent,
)

EventTypes = Union[
    DetectionEvent,
    SafeEvent,
    AllocationEvent,
    OrphanEvent,
    TalkDownEvent,
    TwilioCallEvent,
    TwilioMessageEvent,
    EscalationOpenEvent,
    EscalationCloseEvent,
]


class EventHandler(ABC):
    @abstractmethod
    async def handle(self, event: EventTypes) -> None:
        pass

    @abstractmethod
    def handle_sync(self, event: EventTypes) -> None:
        pass
