"""# pylint: disable=too-many-lines  # pylint: disable=too-many-lines
Classes to implement ORM for tables in RDS MySQL Database with SQLAlachemy.
"""

import typing
from typing import Any, Dict

import structlog

import db_controller as ctrl
from common_utils_v1.db_pool import db_adapter_pool, read_db_adapter_pool
from database.db_adapter import DBAdapter

log = structlog.get_logger("hakimo", module="RDS Client")


class RDSClient:  # pylint: disable=too-many-public-methods
    """
    Class containing all implementation to interface with RDS
    """

    def __init__(
        self,
        db_adapter: DBAdapter = None,
        read_db_adapter: typing.Optional[DBAdapter] = None,
    ):
        # Write db adapter instance as an instance variable
        self.db_adapter = db_adapter or db_adapter_pool

        # Read db adapter instance as an instance variable
        self.read_db_adapter = read_db_adapter or read_db_adapter_pool
        # TODO: this annotation can be Dict[str,Any] too.
        # But looks keys can be Enum too, hence keeping as [Any, Any]
        self.cache_alarm_types: Dict[Any, Any] = {}
        self.cache_alarm_type_names: Dict[str, Any] = {}
        self.regex_alarm_type_cache: Dict[str, Any] = {}
        self._ctrl = ctrl.DBControllerV1(
            self.db_adapter, read_db_adapter=self.read_db_adapter
        )
