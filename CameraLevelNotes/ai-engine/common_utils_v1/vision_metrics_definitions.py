import prometheus_client as prom

VISION_ALARM_COUNT = prom.Counter(
    "vision_alarm",
    "Vision alarm count as per camera group id",
    labelnames=["tenant_id", "camera_group_id"],
)
VISION_ALARM_QUEUE_LENGTH = prom.Gauge(
    "vision_alarm_queue_length",
    "Length of queue waiting for vision processor",
    labelnames=["queue_name"],
)

VISION_LLM_EVENTS_PROCESSED_TOTAL = prom.Counter(
    "vision_llm_events_processed_total",
    "Total number of LLM events processed by the llm consumer",
    labelnames=[
        "tenant_id",
        "camera_group_id",
        "camera_id",
        "success",
        "reason",
    ],
)

VISION_LLM_ALARM_GROUPS_PROCESSED_TOTAL = prom.Counter(
    "vision_llm_alarm_groups_processed_total",
    "Total number of alarm groups processed by the llm consumer",
    labelnames=[
        "tenant_id",
        "camera_group_id",
        "success",
        "reason",
    ],
)

VISION_LLM_EVENT_RECOMMENDATION = prom.Counter(
    "vision_llm_event_recommendation",
    "Recommendation for events by the llm consumer",
    labelnames=["tenant_id", "camera_group_id", "camera_id", "recommendation"],
)

VISION_LLM_ALARM_GROUP_RECOMMENDATION = prom.Counter(
    "vision_llm_alarm_group_recommendation",
    "Recommendation for alarm groups by the llm consumer",
    labelnames=["tenant_id", "camera_group_id", "recommendation"],
)

VISION_LLM_ALARM_GROUP_FORCED_ESCALATION = prom.Counter(
    "vision_llm_alarm_group_forced_escalation",
    "Number of alarm groups forced to escalate",
    labelnames=["tenant_id", "camera_group_id", "alarm_group_id", "reason"],
)

VISION_LLM_ALARM_GROUP_AUTO_RESOLVED = prom.Counter(
    "vision_llm_alarm_group_auto_resolved",
    "Number of alarm groups auto resolved",
    labelnames=["tenant_id", "camera_group_id", "alarm_group_id"],
)

VISION_LLM_EVENT_ANALYSIS_TIME = prom.Histogram(
    "vision_llm_event_analysis_time",
    "Total time taken to analyze events using LLM",
    labelnames=["tenant_id", "camera_group_id", "camera_id"],
    buckets=(
        0.5,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        12,
        14,
        16,
        18,
        20,
        25,
        30,
        35,
        40,
        45,
        50,
        55,
        60,
        70,
        80,
        90,
        120,
    ),
)

VISION_LLM_ALARM_GROUP_ANALYSIS_TIME = prom.Histogram(
    "vision_llm_alarm_group_analysis_time",
    "Total time taken to analyze alarm groups using LLM",
    labelnames=["tenant_id", "camera_group_id"],
    buckets=(
        0.5,
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        12,
        14,
        16,
        18,
        20,
        25,
        30,
        35,
        40,
        45,
        50,
        55,
        60,
        70,
        80,
        90,
        120,
    ),
)

VISION_LLM_API_TIME = prom.Histogram(
    "vision_llm_api_time",
    "Total time taken by LLM API",
    buckets=(
        0.1,
        0.2,
        0.5,
        1,
        1.5,
        2,
        2.5,
        3,
        3.5,
        4,
        4.5,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        20,
        25,
        30,
        60,
    ),
)

VISION_LLM_API_RESPONSE = prom.Counter(
    "vision_llm_api_response",
    "Response from LLM API",
    labelnames=["success", "response_code"],
)

VISION_LLM_API_TOKEN_COUNT = prom.Counter(
    "vision_llm_api_token_count",
    "Total number of tokens processed by the LLM API",
    labelnames=["type"],
)

# HTTP API Metrics
VISION_HTTP_REQUESTS_TOTAL = prom.Counter(
    "vision_http_requests_total",
    "Total number of HTTP requests by endpoint",
    labelnames=["method", "endpoint", "status_code"],
)

VISION_HTTP_REQUEST_DURATION = prom.Histogram(
    "vision_http_request_duration_seconds",
    "HTTP request duration in seconds",
    labelnames=["method", "endpoint"],
    buckets=(0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0),
)

VISION_HTTP_ERRORS_TOTAL = prom.Counter(
    "vision_http_errors_total",
    "Total number of HTTP errors by endpoint",
    labelnames=["method", "endpoint", "error_type"],
)

# Business Logic Metrics
VISION_CAMERA_LOOKUPS_TOTAL = prom.Counter(
    "vision_camera_lookups_total",
    "Total number of camera lookups",
    labelnames=["success"],
)

VISION_ALARM_GROUPS_RETRIEVED_TOTAL = prom.Counter(
    "vision_alarm_groups_retrieved_total",
    "Total number of alarm groups retrieved",
    labelnames=["camera_group_id"],
)

# Operator Management Metrics
VISION_OPERATOR_ALARMS_ALLOCATED_TOTAL = prom.Counter(
    "vision_operator_alarms_allocated_total",
    "Total alarms allocated to operators",
    labelnames=["operator_id", "tenant_id"],
)

VISION_OPERATOR_ALARMS_RESOLVED_TOTAL = prom.Counter(
    "vision_operator_alarms_resolved_total",
    "Total alarms resolved by operators",
    labelnames=["operator_id", "tenant_id", "resolution_type"],
)

VISION_OPERATOR_ESCALATIONS_CREATED_TOTAL = prom.Counter(
    "vision_operator_escalations_created_total",
    "Total escalations created by operators",
    labelnames=["operator_id", "tenant_id"],
)

VISION_OPERATOR_ESCALATIONS_CLOSED_TOTAL = prom.Counter(
    "vision_operator_escalations_closed_total",
    "Total escalations closed by operators",
    labelnames=["operator_id", "tenant_id"],
)

VISION_OPERATOR_ACTIVE_ALARMS = prom.Gauge(
    "vision_operator_active_alarms",
    "Current active alarms per operator",
    labelnames=["operator_id", "tenant_id"],
)

VISION_OPERATOR_ACTIVE_ESCALATIONS = prom.Gauge(
    "vision_operator_active_escalations",
    "Current active escalations per operator",
    labelnames=["operator_id", "tenant_id"],
)

VISION_ALARM_RESOLUTION_DURATION_SECONDS = prom.Histogram(
    "vision_alarm_resolution_duration_seconds",
    "Time taken to resolve alarms from creation to resolution",
    labelnames=["operator_id", "tenant_id", "resolution_type"],
    buckets=(
        30,  # 30 seconds
        60,  # 1 minute
        120,  # 2 minutes
        300,  # 5 minutes
        600,  # 10 minutes
        900,  # 15 minutes
        1800,  # 30 minutes
        3600,  # 1 hour
        7200,  # 2 hours
        14400,  # 4 hours
        28800,  # 8 hours
        86400,  # 24 hours
    ),
)

VISION_ALARM_ALLOCATION_TO_RESOLUTION_DURATION_SECONDS = prom.Histogram(
    "vision_alarm_allocation_to_resolution_duration_seconds",
    "Time from alarm allocation to resolution by operator",
    labelnames=["operator_id", "tenant_id", "resolution_type"],
    buckets=(
        30,  # 30 seconds
        60,  # 1 minute
        120,  # 2 minutes
        300,  # 5 minutes
        600,  # 10 minutes
        900,  # 15 minutes
        1800,  # 30 minutes
        3600,  # 1 hour
        7200,  # 2 hours
        14400,  # 4 hours
        28800,  # 8 hours
        86400,  # 24 hours
    ),
)

VISION_ALARMS_ORPHANED_TOTAL = prom.Counter(
    "vision_alarms_orphaned_total",
    "Total alarms that became orphaned",
    labelnames=["tenant_id", "camera_group_id"],
)

VISION_ALARMS_PENDING_ALLOCATION = prom.Gauge(
    "vision_alarms_pending_allocation",
    "Alarms waiting for operator allocation",
    labelnames=["tenant_id", "camera_group_id"],
)

VISION_ALARMS_AGING_MINUTES = prom.Histogram(
    "vision_alarms_aging_minutes",
    "Age of unresolved alarms in minutes",
    labelnames=["tenant_id", "camera_group_id", "state"],
    buckets=(
        5,  # 5 minutes
        10,  # 10 minutes
        15,  # 15 minutes
        30,  # 30 minutes
        60,  # 1 hour
        120,  # 2 hours
        240,  # 4 hours
        480,  # 8 hours
        720,  # 12 hours
        1440,  # 24 hours
        2880,  # 48 hours
        4320,  # 72 hours
    ),
)

VISION_OPERATOR_INVESTIGATION_EVENTS_TOTAL = prom.Counter(
    "vision_operator_investigation_events_total",
    "Total investigation events created by operators",
    labelnames=["operator_id", "tenant_id"],
)
