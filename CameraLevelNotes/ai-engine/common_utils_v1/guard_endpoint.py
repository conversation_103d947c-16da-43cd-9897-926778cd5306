from http import HTTPStatus
from typing import Callable, Optional, Sequence

import structlog

from common_utils_v1.auth.common import GuardEndpointOperatorEnum
from common_utils_v1.common import user_email
from common_utils_v1.db_pool import ctrl_pool
from errors.engine_error import AuthError

logger = structlog.get_logger(
    "hakimo.common_utils_v1", module="guard_endpoint"
)


def guard_endpoint(
    permissions: Sequence[str],
    operator: Optional[
        GuardEndpointOperatorEnum
    ] = GuardEndpointOperatorEnum.AND,
) -> Callable:
    def guard(f: Callable):
        def wrapper(*args, **kwargs):
            with ctrl_pool.get() as controller:
                # Need user_email to create a guard - get from auth token
                try:
                    email = user_email()
                except AuthError as exc:
                    logger.info("Bad token passed to orm")
                    if exc.status_code == 401:
                        return {
                            "message": "Invalid token",
                            "status": HTTPStatus.UNAUTHORIZED.value,
                        }, HTTPStatus.UNAUTHORIZED.value
                    if exc.status_code == 400:
                        return {
                            "message": "Token based authentication"
                            " not configured correctly",
                            "status": HTTPStatus.BAD_REQUEST.value,
                        }, HTTPStatus.BAD_REQUEST.value
                    return {
                        "message": "Error parsing token",
                        "status": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                    }, HTTPStatus.INTERNAL_SERVER_ERROR.value

                if email is None:
                    return {
                        "message": "No email in token",
                        "status": HTTPStatus.UNAUTHORIZED.value,
                    }, HTTPStatus.UNAUTHORIZED.value

                user = controller._user.user_by_email(email)
                if (
                    user is None
                    or user.role_id is None
                    or user.tenant_id is None
                    or not user.is_enabled
                ):
                    return {
                        "message": "Invalid user",
                        "status": HTTPStatus.UNAUTHORIZED.value,
                    }, HTTPStatus.UNAUTHORIZED.value
                role_capabilities = controller._role.get_capabilities_for_role(
                    user.role_id
                )
                if operator == GuardEndpointOperatorEnum.OR:
                    # checks if ANY of the user role_capabilities is there in passed permissions
                    if all(i not in role_capabilities for i in permissions):
                        return {
                            "message": "User is not authorized for this endpoint",
                            "status": HTTPStatus.FORBIDDEN.value,
                        }, HTTPStatus.FORBIDDEN.value
                else:
                    # checks if ALL user role_capabilities are there in passed permissions
                    if not all(i in role_capabilities for i in permissions):
                        return {
                            "message": "User is not authorized for this endpoint",
                            "status": HTTPStatus.FORBIDDEN.value,
                        }, HTTPStatus.FORBIDDEN.value
                return f(*args, **kwargs, user=user)

        return wrapper

    return guard
