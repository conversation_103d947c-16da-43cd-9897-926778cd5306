from typing import List

from common_utils_v1.auth.auth0_token import validate_token_auth
from models_rds.users import Users


def user_email():
    # return "<EMAIL>"
    # return "<EMAIL>"
    # return "<EMAIL>"
    # return "<EMAIL>"

    token_payload = validate_token_auth()

    return token_payload["http://hakimo.ai/email"]


def get_user_tenant_ids(
    user: Users,
    include_msp_tenants: bool = True,
    include_vision_tenants: bool = True,
) -> List[str]:
    tenant_ids = [user.tenant_id]
    if include_msp_tenants and user.msp_tenants:
        tenant_ids.extend(user.msp_tenants)
    if include_vision_tenants and user.vision_tenants:
        tenant_ids.extend(user.vision_tenants)
    tenant_set = set(tenant_ids)
    return list(tenant_set)
