import json
from functools import lru_cache
from urllib.request import urlopen

from jose import jwt

from common_utils_v1.auth.common import get_token_auth_header
from config import backend_config as config
from errors.engine_error import AuthError

try:
    endpoint_info = config.gateway().get("apiAuthentication")
except FileNotFoundError:
    endpoint_info = None

if endpoint_info is None:
    AUTH0_DOMAIN = API_AUDIENCE = None
else:
    AUTH0_DOMAIN = endpoint_info.get("auth0Domain")
    API_AUDIENCE = endpoint_info.get("auth0Audience")
ALGORITHMS = ["RS256"]


@lru_cache(maxsize=1)
def get_auth0_jwks():
    with urlopen(
        "https://" + AUTH0_DOMAIN + "/.well-known/jwks.json"
    ) as jsonurl:
        return json.loads(jsonurl.read())


def validate_token_auth():
    if AUTH0_DOMAIN is None or API_AUDIENCE is None:
        raise AuthError(
            {
                "code": "incorrect_api_configuration",
                "description": "API has not been configured with correct auth0 "
                "credentials. Check config.",
            },
            500,
        )
    token = get_token_auth_header()
    jwks = get_auth0_jwks()
    unverified_header = jwt.get_unverified_header(token)
    if "kid" not in unverified_header:
        raise AuthError(
            {
                "code": "invalid_header",
                "description": "Unable to parse authentication token.",
            },
            401,
        )
    rsa_key = {}
    for key in jwks["keys"]:
        if key["kid"] == unverified_header["kid"]:
            rsa_key = {
                "kty": key["kty"],
                "kid": key["kid"],
                "use": key["use"],
                "n": key["n"],
                "e": key["e"],
            }
    if rsa_key:
        try:
            payload = jwt.decode(
                token,
                rsa_key,
                algorithms=ALGORITHMS,
                audience=API_AUDIENCE,
                issuer="https://" + AUTH0_DOMAIN + "/",
            )
        except jwt.ExpiredSignatureError as exc:
            raise AuthError(
                {"code": "token_expired", "description": "token is expired"},
                401,
            ) from exc
        except jwt.JWTClaimsError as exc:
            raise AuthError(
                {
                    "code": "invalid_claims",
                    "description": "incorrect claims,"
                    "please check the audience and issuer",
                },
                401,
            ) from exc
        except Exception as exc:
            raise AuthError(
                {
                    "code": "invalid_header",
                    "description": "Unable to parse authentication token.",
                },
                401,
            ) from exc
        if "http://hakimo.ai/email" not in payload:
            raise AuthError(
                {
                    "code": "invalid_user",
                    "description": "Unable to get user email",
                },
                400,
            )
        return payload
    raise AuthError(
        {
            "code": "invalid_header",
            "description": "Unable to find appropriate key",
        },
        401,
    )
