from typing import Any, Optional

import boto3

from common_utils.threading_utils import ThreadSafeResource
from common_utils_v1.io_helpers import read_file
from config import backend_config as config

S3_BUCKET_NAME_KEY = "bucket_name"
S3_FILE_NAME_KEY = "file_name"


def get_s3_client(access_key=None, secret_key=None) -> Any:
    if access_key is None or secret_key is None:
        access_key = read_file(config.HAIE.AWS_ACCESS_KEY_ID, missing="")
        secret_key = read_file(config.HAIE.AWS_SECRET_KEY, missing="")
    s3 = boto3.client(
        "s3",
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
    )
    return s3


def get_s3_path_details(s3_filepath: str) -> Optional[dict]:
    """Given S3 file path, extract bucket name and file name,
    to enable easy downloading

    Args:
        s3_filepath ([str]): Full path to file

    Returns:
        [dict, None]: Info dict containing 'bucket_name' and 'file_name'. None
        if path is not a valid S3 path
    """
    if s3_filepath.startswith("https://"):
        bucket_info, file_name = s3_filepath.split("amazonaws.com/")
        bucket_name = bucket_info.split(".s3")[0][len("https://") :]
        return {S3_BUCKET_NAME_KEY: bucket_name, S3_FILE_NAME_KEY: file_name}
    if s3_filepath.startswith("s3://"):
        bucket_name, *file_parts = s3_filepath[len("s3://") :].split("/")
        return {
            S3_BUCKET_NAME_KEY: bucket_name,
            S3_FILE_NAME_KEY: "/".join(file_parts),
        }
    return None


# @trace_method
def get_signed_s3_url(
    bucket_name,
    file_name,
    expiration_secs=300,
):
    """
    Generates a signed S3 URL - a publicly accessible URL that can access the object
    for a fixed amount of time.
    See here:
    https://docs.aws.amazon.com/AmazonS3/latest/userguide/ShareObjectPreSignedURL.html
    Assumes that the file has been checked to exist. Will return a signed URL
    even if it does not exist!
    """
    with s3_pool.get() as s3:
        return s3.generate_presigned_url(
            ClientMethod="get_object",
            Params={"Bucket": bucket_name, "Key": file_name},
            ExpiresIn=expiration_secs,
        )


s3_pool = ThreadSafeResource(get_s3_client)
