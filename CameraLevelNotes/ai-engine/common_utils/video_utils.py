"""Module with various helper utilities to deal with videos"""

import base64
import math
import os.path as osp
import typing
from datetime import datetime
from typing import List, Tuple

import cv2
import numpy as np
import structlog

from common_utils.ffmpeg_helpers import (
    get_frame_ts,
    get_video_duration_ffprobe,
)
from common_utils.image_utils import clahe
from config import backend_config as config

try:
    _ = cv2.cudacodec.createVideoReader
    cv2_cuda_support = True
except AttributeError:
    cv2_cuda_support = False
# from decord import DECORDError, VideoReader, gpu


log = structlog.get_logger("hakimo", module="Video Utils")
ALLOWED_VIDEO_FORMATS = [".mp4", ".avi", ".mkv", ".asf"]


def check_if_video(filename):
    """Given a filename, check if it is a valid video file

    Args:
        filename ([str]): Name of file / path to file

    Returns:
        [bool]: True if file is a video file, False if not
    """
    if not filename:
        return False
    if osp.splitext(filename)[1] in ALLOWED_VIDEO_FORMATS:
        return True
    return False


class VideoReader:
    def __init__(
        self,
        video_path: str,
        use_gpu: bool = False,
        vid_cap: typing.Optional[cv2.VideoCapture] = None,
    ):
        self.video_path = video_path
        self.use_gpu = use_gpu
        if self.use_gpu:
            self.reader = cv2.cudacodec.createVideoReader(video_path)
        else:
            self.reader = vid_cap or cv2.VideoCapture(video_path)
        self.get_details(vid_cap)

    def get_details(self, vcap: typing.Optional[cv2.VideoCapture] = None):
        if self.use_gpu:
            vid_cap = vcap or cv2.VideoCapture(self.video_path)
        else:
            vid_cap = self.reader
        self.fps: float = vid_cap.get(cv2.CAP_PROP_FPS)
        self.width = int(vid_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.height = int(vid_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        self.end_frame = int(vid_cap.get(cv2.CAP_PROP_FRAME_COUNT))

    def read(self) -> typing.Tuple[bool, np.ndarray]:
        if self.use_gpu:
            ret, mat = self.reader.nextFrame()
            if ret:
                mat = cv2.cuda.cvtColor(mat, cv2.COLOR_BGRA2BGR)
                mat = mat.download()[: self.height, : self.width]
            return ret, mat
        return self.reader.read()

    def nextFrame(self) -> typing.Tuple[bool, cv2.cuda_GpuMat]:
        assert self.use_gpu
        ret, img = self.reader.nextFrame()
        if ret:
            img = cv2.cuda.cvtColor(img, cv2.COLOR_BGRA2RGB)
        return ret, img

    def grab(self):
        self.reader.grab()


def get_video_reader(
    video_path: str,
    logger=None,
    use_gpu: bool = False,
    vcap: typing.Optional[cv2.VideoCapture] = None,
) -> typing.Optional[VideoReader]:
    """Reads a video given the path

    Args:
        video_path ([str]): Full path to the video
        logger ([logging.Logger], optional):
            Logger which will log any errors/warnings. Defaults to None.

    Returns:
        [None, cv2.VideoCapture]: Video capture object
            if file exists and is a video file, or None
    """
    if not video_path:
        return None

    if not isinstance(video_path, str):
        if logger is not None:
            logger.info("No video for alarm!", video_path=video_path)
        return None
    if not osp.splitext(video_path)[1] in ALLOWED_VIDEO_FORMATS:
        if logger is not None:
            logger.warning("Invalid video for alarm", video_path=video_path)
        return None
    if not osp.exists(video_path):
        if logger is not None:
            logger.info("No video for alarm!", video_path=video_path)
        return None
    vid: VideoReader = VideoReader(video_path, use_gpu, vid_cap=vcap)
    return vid


def get_batch_from_video_cv2(
    vid_reader: VideoReader,
    processing_fps=config.HAIE.PROCESSING_FPS,
    batch_size: int = 5,
    offset: bool = False,
    pad_batch: bool = True,
):
    fps = vid_reader.fps
    end_frame = vid_reader.end_frame
    start_frame = 0
    frame_idx = start_frame
    frame_nums = []
    imgs = []
    prev_batch_last_image = None
    while True:
        if (
            frame_idx % max(1, math.floor(fps / processing_fps)) == 0
            and frame_idx >= start_frame
        ):
            ret, img = vid_reader.nextFrame()
        else:
            vid_reader.grab()
            frame_idx += 1
            continue
        if frame_idx > end_frame:
            break
        if not ret:
            frame_idx += 1
            continue
        frame_nums.append(frame_idx)
        imgs.append(img)
        if len(frame_nums) == batch_size + 1 * offset:
            if offset:
                yield frame_nums[:-1], imgs[:-1], frame_nums[1:], imgs[1:]
                # Save last processed image of this batch
                prev_batch_last_image = (frame_nums[-2], imgs[-2])
                # Setup the last offset frame as the first image for the next batch
                frame_nums, imgs = [frame_nums[-1]], [imgs[-1]]
            else:
                yield frame_nums, imgs, None, None
                frame_nums, imgs = [], []
        frame_idx += 1
    if len(frame_nums) > 0:
        batch_size_missing = batch_size - len(imgs)
        if offset:
            if len(frame_nums) == 1:
                multiplier = batch_size if pad_batch else 1
                if prev_batch_last_image is not None:
                    yield (
                        frame_nums,
                        [imgs[0]] * multiplier,
                        [prev_batch_last_image[0]],
                        [prev_batch_last_image[1]] * multiplier,
                    )
                else:
                    # Single frame selected in entire video, will have to use same img as offset
                    imgs = [imgs[0]] * multiplier
                    yield frame_nums, imgs, frame_nums, imgs
            else:
                # First set correct offset img for valid frames
                multiplier = batch_size_missing if pad_batch else 0
                offset_imgs = imgs[1:] + imgs[-2:-1]
                offset_frame_nums = frame_nums[1:] + frame_nums[-2:-1]
                if batch_size_missing > 0:
                    # Duplicate last frame to fill up batch
                    offset_imgs = offset_imgs + [offset_imgs[-1]] * multiplier
                    imgs = imgs + [imgs[-1]] * multiplier
                yield (
                    frame_nums,
                    imgs,
                    offset_frame_nums,
                    offset_imgs,
                )
        else:
            if batch_size_missing > 0:
                multiplier = batch_size_missing if pad_batch else 0
                imgs = imgs + [imgs[0]] * multiplier
            yield frame_nums, imgs, None, None


def get_frames_from_video_cv2(
    vid_reader: VideoReader,
    start_frame: int,
    end_frame: int,
    process_fps=config.HAIE.PROCESSING_FPS,
    num_frames: typing.Optional[int] = None,
    convert_to_rgb: bool = False,
    use_clahe: bool = False,
) -> typing.Tuple[typing.Sequence[int], typing.Sequence[np.ndarray], int]:
    """Assumes that video capture object has the position set to start_frame
    Returns the frame_idx that needs to be passed in as start_frame on
    next call if more frames are to be fetched"""
    fps = vid_reader.fps
    frame_idx = start_frame
    imgs = []
    frame_nums: typing.List[int] = []
    while num_frames is None or len(frame_nums) < num_frames:
        if (
            frame_idx % max(1, math.floor(fps / process_fps)) == 0
            and frame_idx >= start_frame
        ):
            ret, img = vid_reader.read()
        else:
            vid_reader.grab()
            frame_idx += 1
            continue
        if frame_idx > end_frame:
            break
        if not ret:
            frame_idx += 1
            continue
        frame_nums.append(frame_idx)
        if use_clahe:
            img = clahe(img)
        imgs.append(img)
        frame_idx += 1

    if convert_to_rgb:
        imgs = [cv2.cvtColor(i, cv2.COLOR_BGR2RGB) for i in imgs]

    return frame_nums, imgs, frame_idx


# Keeping commented here in case we switch to using decorder


# def get_frames_from_video_decorder(
#     video_path: str,
#     start_frame: int,
#     end_frame: int,
#     process_fps=config.HAIE.PROCESSING_FPS,
#     num_frames: typing.Optional[int] = None,
#     convert_to_rgb: bool = False,
# ):
#     vid_reader = VideoReader(video_path, ctx=gpu())
#     fps = vid_reader.get_avg_fps()
#     frame_interval = max(1, math.floor(fps / process_fps))
#     # If not a multiple, round up to next closest multiple
#     if start_frame % frame_interval:
#         start = (start_frame // frame_interval + 1) * frame_interval
#     else:
#         start = start_frame
#     if end_frame % frame_interval == 0:
#         end = end_frame + frame_interval
#     else:
#         end = end_frame
#     frame_idx = np.arange(start, end, frame_interval)
#     if num_frames:
#         frame_idx = frame_idx[:num_frames]

#     img_batch = vid_reader.get_batch(frame_idx).asnumpy()
#     if not convert_to_rgb:
#         img_batch = [cv2.cvtColor(img, cv2.COLOR_RGB2BGR) for img in img_batch]
#     return (
#         frame_idx,
#         img_batch,
#         # increment is needed for next call
#         frame_idx[-1] + 1,
#     )


def get_video_duration(
    video_path_or_cap: typing.Union[str, cv2.VideoCapture],
) -> float:
    """Function to take a local video path and return its duration in
    seconds"""
    if isinstance(video_path_or_cap, str):
        res = get_video_duration_ffprobe(video_path_or_cap)
        if res["error"] is None:
            return float(res["duration"])
    elif isinstance(video_path_or_cap, cv2.VideoCapture):
        fps = float(video_path_or_cap.get(cv2.CAP_PROP_FPS))
        frame_count = float(video_path_or_cap.get(cv2.CAP_PROP_FRAME_COUNT))
        return frame_count / fps
    return 0.0


def get_video_fps(video_path: str) -> typing.Union[float, int]:
    """
    It opens a video file, gets the FPS, and closes the video file

    Args:
      video_path (str): The path to the video file.

    Returns:
      the fps of the video.
    """
    vid = cv2.VideoCapture(video_path)
    fps = vid.get(cv2.CAP_PROP_FPS)
    vid.release()
    return fps


def check_video_frames(
    video_path: typing.Optional[str], processing_fps: int
) -> bool:
    """Function to take a local video path or cv2 VideoCapture object and check
    if all the frames are correctly read
    """
    if video_path is None or not osp.exists(video_path):
        return False
    video_check_pass = True
    try:
        frame_ts = get_frame_ts(video_path)
    except Exception as e:  # pylint: disable=broad-except
        log.warning("Video check failed due to unreadable video", exc_info=e)
        return False
    if not frame_ts:
        log.warning(
            "Video check failed due to unreadable video", frame_ts=frame_ts
        )
        return False
    diffs = [t - s for s, t in zip(frame_ts, frame_ts[1:])]
    if not diffs:
        log.warning(
            "Video check failed due to unreadable video", frame_ts=frame_ts
        )
        return False
    max_diff = np.amax(diffs)
    if max_diff > config.HAIE.videoProps["maxGapMultiplier"]:
        log.info("Video check failed due to large gap", gap=max_diff)
        return False
    fps = 1 / np.mean(diffs)
    if round(fps) < processing_fps:
        log.info("Video check failed due to low frame rate")
        return False
    return video_check_pass


def get_sample_frame_from_video(video_path: str) -> np.ndarray:
    """
    Given a video path, or RTSP stream, it returns a frame from the video
        at 75% of the total frames

    Args:
      video_path (str): the path to the video file

    Returns:
      A numpy array of the frame.
    """
    vid = cv2.VideoCapture(video_path)
    total_frames = vid.get(cv2.CAP_PROP_FRAME_COUNT)
    target_frame = int(0.75 * total_frames)
    vid.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
    ret, image = vid.read()
    if not ret:
        raise Exception("Can't read video")
    vid.release()
    return image


def encode_frame_to_webp_base64(frame: np.ndarray) -> str:
    _, buf = cv2.imencode(".webp", frame)
    return base64.b64encode(buf).decode("utf-8")


def extract_and_encode_frames(
    video_path: str, video_times: Tuple[datetime, datetime], fps: int = 1
) -> List[str]:
    """
    Extracts frames from a video at a given FPS and encodes each as a WebP base64 string
    Args:
        video_path (str): Full path to the video
        video_times (Tuple[datetime, datetime]): Time range of the video
        fps (int, optional): Frames per second defaults to 1
    Returns:
        List[str]: List of base64-encoded WebP strings representing extracted frames
    """
    vid_reader = get_video_reader(video_path, logger=log)
    if vid_reader is None:
        return []
    _, frames, _ = get_frames_from_video_cv2(
        vid_reader,
        start_frame=0,
        end_frame=vid_reader.end_frame,
        process_fps=fps,
        convert_to_rgb=True,
    )
    return [encode_frame_to_webp_base64(f) for f in frames]
