"""Module with various helper utilities to deal with images"""

import base64
import os.path as osp
import typing
from collections import defaultdict
from io import Bytes<PERSON>
from pathlib import Path

import cv2
import numpy as np
import structlog
from PIL import Image
from werkzeug.datastructures import FileStorage

from common_utils.typing_helpers import IMAGE, POINT

log = structlog.get_logger("hakimo", module="Image Utils")
ALLOWED_IMG_FORMATS = [".png", ".jpg", ".jpeg", ".TIFF"]


class Defisheye:
    def __init__(self, infile, **kwargs):
        vkwargs = {
            "fov": 180,
            "pfov": 120,
            "xcenter": None,
            "ycenter": None,
            "radius": None,
            "pad": 0,
            "angle": 0,
            "dtype": "equalarea",
            "format": "fullframe",
        }
        self._start_att(vkwargs, kwargs)
        if isinstance(infile, str):
            _image = cv2.imread(infile)
        elif isinstance(infile, np.ndarray):
            _image = infile
        else:
            raise Exception("Image format not recognized")
        if self._pad > 0:  # pylint: disable=no-member
            _image = cv2.copyMakeBorder(
                _image,
                self._pad,  # pylint: disable=no-member
                self._pad,  # pylint: disable=no-member
                self._pad,  # pylint: disable=no-member
                self._pad,  # pylint: disable=no-member
                cv2.BORDER_CONSTANT,
            )
        width = _image.shape[1]
        height = _image.shape[0]
        xcenter = width // 2
        ycenter = height // 2
        dim = min(width, height)
        x0 = xcenter - dim // 2
        xf = xcenter + dim // 2
        y0 = ycenter - dim // 2
        yf = ycenter + dim // 2
        self._image = _image[y0:yf, x0:xf, :]
        self._width = self._image.shape[1]
        self._height = self._image.shape[0]
        self._xcenter = self._xcenter or (self._width - 1) // 2
        self._ycenter = self._ycenter or (self._height - 1) // 2

    def _map(self, i, j, ofocinv, dim):
        xd = i - self._xcenter
        yd = j - self._ycenter
        rd = np.hypot(xd, yd)
        phiang = np.arctan(ofocinv * rd)
        if self._dtype == "linear":  # pylint: disable=no-member
            ifoc = dim * 180 / (self._fov * np.pi)  # pylint: disable=no-member
            rr = ifoc * phiang
        elif self._dtype == "equalarea":  # pylint: disable=no-member
            ifoc = dim / (2.0 * np.sin(self._fov * np.pi / 720))  # pylint: disable=no-member
            rr = ifoc * np.sin(phiang / 2)
        elif self._dtype == "orthographic":  # pylint: disable=no-member
            ifoc = dim / (2.0 * np.sin(self._fov * np.pi / 360))  # pylint: disable=no-member
            rr = ifoc * np.sin(phiang)
        elif self._dtype == "stereographic":  # pylint: disable=no-member
            ifoc = dim / (2.0 * np.tan(self._fov * np.pi / 720))  # pylint: disable=no-member
            rr = ifoc * np.tan(phiang / 2)
        rdmask = rd != 0
        xs = xd.astype(np.float32).copy()
        ys = yd.astype(np.float32).copy()
        xs[rdmask] = (rr[rdmask] / rd[rdmask]) * xd[rdmask] + self._xcenter
        ys[rdmask] = (rr[rdmask] / rd[rdmask]) * yd[rdmask] + self._ycenter
        xs[~rdmask] = 0
        ys[~rdmask] = 0
        return xs, ys

    def convert(self, outfile=None):
        if self._format == "circular":  # pylint: disable=no-member
            dim = min(self._width, self._height)
        elif self._format == "fullframe":  # pylint: disable=no-member
            dim = np.sqrt(self._width**2.0 + self._height**2.0)
        if self._radius is not None:  # pylint: disable=no-member
            dim = 2 * self._radius  # pylint: disable=no-member
        ofoc = dim / (2 * np.tan(self._pfov * np.pi / 360))  # pylint: disable=no-member
        ofocinv = 1.0 / ofoc
        i = np.arange(self._width)
        j = np.arange(self._height)
        i, j = np.meshgrid(i, j)

        (
            xs,
            ys,
        ) = self._map(i, j, ofocinv, dim)
        img = cv2.remap(self._image, xs, ys, cv2.INTER_LINEAR)
        if outfile is not None:
            cv2.imwrite(outfile, img)
        return img

    def _start_att(self, vkwargs, kwargs):
        pin = []
        for key, value in kwargs.items():
            if key not in vkwargs:
                raise NameError("Invalid key {}".format(key))
            pin.append(key)
            setattr(self, "_{}".format(key), value)
        pin = set(pin)
        rkeys = set(vkwargs.keys()) - pin
        for key in rkeys:
            setattr(self, "_{}".format(key), vkwargs[key])


def add_text_to_image(image: np.ndarray, text: str):
    height, _, _ = image.shape
    font_scale = height * 0.05 / 30  # Assuming font height of 30 for scaling
    font = cv2.FONT_HERSHEY_SIMPLEX
    color = (0, 0, 255)  # Red color in BGR
    thickness = 3
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    text_x = 10  # x-coordinate with some margin
    text_y = 10 + text_size[1]  # y-coordinate with some margin
    cv2.putText(
        image, text, (text_x, text_y), font, font_scale, color, thickness
    )
    return image


def read_wh(image_path: str) -> typing.Tuple[int, int]:
    img = cv2.imread(image_path)
    assert img.shape[2] == 3
    return img.shape[1], img.shape[0]


def check_if_image(filename):
    """Given a filename, check if it is a valid image file

    Args:
        filename ([str]): Name of file / path to file

    Returns:
        [bool]: True if file is a image file, False if not
    """
    if osp.splitext(filename)[1] in ALLOWED_IMG_FORMATS:
        return True
    return False


def scale_points(
    points: typing.Sequence[POINT],
    new_resolution: POINT,
    old_resolution: POINT,
) -> typing.Sequence[POINT]:
    scale_x = new_resolution[0] / old_resolution[0]
    scale_y = new_resolution[1] / old_resolution[1]
    ret = []
    for point in points:
        ret.append([point[0] * scale_x, point[1] * scale_y])
    return ret


def match_image_files(
    dir_path_1: str, dir_path_2: str, recurse=False
) -> typing.List[typing.List[str]]:
    """
    Given two directory filepaths, return all matches
    between two directories. Raise assertion error
    if there is not a 1:1 match for all files.

    Args:
        dirpath_1 (str): relative or absolute directory
            path as a string
        dirpath_2 (str): relative or absolute directory
            paths as a string
        recurse (bool): If True, recursively search subdirectories
            for candidate matches. Default is False.


    Returns:
        typing.List[(file_path_1:str, file_path_2:str)...] A list of
            absolute filepath tuple matches, with the files from
            dir_path_1 coming first and files from dir_path_2 coming
            second.
    """

    assert dir_path_1 != dir_path_2

    out = defaultdict(list)

    files_1 = get_image_paths(dir_path_1, recurse=recurse)
    files_2 = get_image_paths(dir_path_2, recurse=recurse)

    for filepath in files_1:
        filename = osp.basename(filepath)
        out[filename].append(filepath)

    for filepath in files_2:
        filename = osp.basename(filepath)
        out[filename].append(filepath)

    return sorted([x for x in out.values() if len(x) == 2])


def get_image_paths(dir_path: str, recurse=False) -> typing.List[str]:
    """
    Given a directory filepath, return a list
    of all absolute image filepaths which are
    within the directory or child directories.


    Args:
        dir_path (str): relative or absolute directory
            path as a string

        recurse (bool): If True, recursively search
            subdirectories for files. Default is False.

    Returns:
        typing.List[str]: list of absolute filepaths which have
            a valid image extension.
    """
    paths = []
    if recurse:
        all_paths = Path(dir_path).rglob("*")
    else:
        all_paths = Path(dir_path).glob("*")
    for path in all_paths:
        if check_if_image(path.name):
            paths.append(str(path.absolute()))
    return paths


def resize_to_largest(
    *images: IMAGE,
) -> typing.List[IMAGE]:
    """Function to take a sequence of images and resize the smaller images to
    the shape of the largest image and return all in same order
    """
    largest_image_index, largest_image = sorted(
        enumerate(images), key=lambda x: x[1].size
    )[-1]
    resize_shape = (largest_image.shape[1], largest_image.shape[0])
    resized_images = []
    for i, image in enumerate(images):
        if i == largest_image_index:
            resized_images.append(image)
            continue
        resized_images.append(cv2.resize(image, resize_shape))
    return resized_images


def clahe(img: np.ndarray) -> np.ndarray:
    """
    We convert the image to the LAB color space,
    split the image into its three channels, apply CLAHE to
    the L channel, and then merge the channels back together
    and convert it to the BGR color space
    Args:
      img (np.ndarray): The image to be processed.
    Returns:
      A numpy array of the image
    """
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    lab_planes = list(cv2.split(lab))
    clahe_obj = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(10, 10))
    lab_planes[0] = clahe_obj.apply(lab_planes[0])
    lab = cv2.merge(lab_planes)
    del clahe_obj
    return cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)


def check_image_valid(file: FileStorage) -> bool:
    try:
        file_content = file.read()
        with Image.open(BytesIO(file_content)):
            file.seek(0)
            return True
    except (IOError, SyntaxError):
        return False


def encode_image_from_array(
    image_array: np.ndarray, return_byte_arr=False
) -> typing.Union[str, bytes]:
    """Convert numpy array to base64 encoded string"""
    image = Image.fromarray(image_array)
    img_buffer = BytesIO()
    image.save(img_buffer, format="JPEG")
    img_byte_arr = img_buffer.getvalue()
    if return_byte_arr:
        return img_byte_arr
    return base64.b64encode(img_byte_arr).decode("utf-8")
