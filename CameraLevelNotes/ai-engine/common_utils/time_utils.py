"""
File containing various functions for time related manipulation
"""

import argparse
import datetime
import typing
from datetime import time, timezone

import dateutil
import pytz
import structlog
from dateutil import parser, tz

log = structlog.get_logger("hakimo", module="Time Utils")


def timedelta_to_str(time_delta: datetime.timedelta) -> str:
    total_seconds = int(time_delta.total_seconds())
    if total_seconds < 0:
        return "00:00:00"
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{hours:02}:{minutes:02}:{seconds:02}"


def get_today_midnight() -> datetime.datetime:
    """Get EOD today"""
    today = datetime.datetime.today()
    return datetime.datetime(today.year, today.month, today.day, 23, 59, 59)


def parse_millisecs_to_utc(milliseconds_ts):
    utc_time = (
        datetime.datetime.fromtimestamp(milliseconds_ts // 1000, tz=tz.tzutc())
        .replace(tzinfo=None)
        .isoformat(timespec="milliseconds")
    )
    return utc_time


def parse_utc_timestamp_str(
    timestamp_ms: typing.Optional[str],
) -> typing.Optional[datetime.datetime]:
    if timestamp_ms:
        return datetime.datetime.utcfromtimestamp(float(timestamp_ms) / 1000)
    return None


def convert_utc_to_local_timestamp(utc_dt):
    """
    Converts a datetime UTC object into a local time in milliseconds
    """

    datetime_utc = utc_dt.replace(tzinfo=tz.tzutc())
    datetime_local = datetime_utc.astimezone(tz.tzlocal())
    timestamp_local = datetime_local.timestamp()
    return timestamp_local * 1000


def convert_local_timestamp_to_utc_datetime(local_dt):
    """
    Converts a local time timestamp in milliseconds
    into a UTC datetime object
    """

    datetime_local = datetime.datetime.fromtimestamp(local_dt / 1000).replace(
        tzinfo=tz.tzlocal()
    )
    datetime_utc = datetime_local.astimezone(tz.tzutc())
    return datetime_utc


def convert_utc_to_tz(
    utc_datetime: datetime.datetime, timezone: str
) -> datetime.datetime:
    """Convert a given UTC timestamp into a timezone denoted by a standard string

    Args:
        utc_datetime (datetime): UTC datetime object
        timezone (str): Name of timezone

    Returns:
        datetime: datetime object in timezone timezone
    """
    utc_datetime = utc_datetime.replace(tzinfo=tz.tzutc())
    return utc_datetime.astimezone(pytz.timezone(timezone))


def valid_date(s):
    """Given a string, check if it is a valid date in
        %Y-%m-%d %H:%M:%S format

    Args:
        s ([str]): date string

    Raises:
        argparse.ArgumentTypeError: Raised error if string is not of
            expected format

    Returns:
        [datetime.datetime]: Datetime object corresponding to string
    """
    try:
        return datetime.datetime.strptime(s, "%Y-%m-%d %H:%M:%S")
    except ValueError as err:
        msg = "Not a valid date: '{0}'.".format(s)
        raise argparse.ArgumentTypeError(msg) from err


def convert_datetime_to_string(dt: typing.Union[str, datetime.datetime]):
    """Given a str or datetime object, convert to a string representation

    Args:
        dt (typing.Union[str, datetime]): [description]
    """
    if isinstance(dt, str):
        return dt
    if isinstance(dt, datetime.datetime):
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    raise TypeError(f"Unexpected type {type(dt)}")


def date_iterator(start_date: str, end_date: str):
    """Returns dates from start_date to end_date without
    leading zeros"""

    def remove_leading(date):
        return "-".join(list(map(lambda x: str(int(x)), date.split("-"))))

    start_date = remove_leading(start_date)
    end_date = end_date or start_date
    end_date = remove_leading(end_date)
    year, month, day = list(map(int, start_date.split("-")))
    date = datetime.datetime(year=year, month=month, day=day)
    year, month, day = list(map(int, end_date.split("-")))
    end_datetime = datetime.datetime(year=year, month=month, day=day)
    while date <= end_datetime:
        date_string = date.strftime("%Y-%m-%d")
        date_string = remove_leading(date_string)
        yield date_string
        date += datetime.timedelta(days=1)


def time2str(start_time: float, round_millis=True) -> str:
    """Takes in the time in milliseconds and returns the UTC Date time string.
    Date string format: YYYY-MM-ddTHH:MM:ss

    Args:
        start_time (float): timestamp in milliseconds
        round_millis (bool, optional): [Whether to round the milliseconds in time]. Defaults to True.

    Returns:
        str: Date String in YYYY-MM-ddTHH:MM:ss format
    """
    if (start_time % 1000) and round_millis:
        t = (start_time + 1000) // 1000
    else:
        t = start_time // 1000 if round_millis else start_time / 1000
    stream_start = (
        datetime.datetime.fromtimestamp(t, tz=tz.tzutc())
        .replace(tzinfo=None)
        .isoformat(timespec="milliseconds")
    )
    return stream_start


def str2epoch(t: str) -> float:
    """Returns the date time string to milliseconds timestamp

    Args:
        t (str): [datetime string, no fixed format]

    Returns:
        [float]: timestamp in milliseconds
    """
    return parser.parse(t).replace(tzinfo=tz.tzutc()).timestamp() * 1000


def format2twodigitmilli(t):
    """
    Formats the given datetime object to the nearest 1000th millisecond place
    """
    if t.microsecond % 1000 >= 500:  # check if there will be rounding up
        t = t + datetime.timedelta(milliseconds=1)  # manually round up
    return t.strftime("%Y-%m-%d %H:%M:%S.%f")[:-4]


def gettimeinseconds(t):
    """
    Returns a string of datetime with seconds precision
    """
    return t.strftime("%Y-%m-%d %H:%M:%S")


def get_date_list(
    start_day: str, end_day: str
) -> typing.List[datetime.datetime]:
    """Function to take two dates in 'Y-M-D' format and return as a list
    all dates from start_day to end_day including those days
    """
    start_datetime = datetime.datetime.strptime(start_day, "%Y-%m-%d")
    end_datetime = datetime.datetime.strptime(end_day, "%Y-%m-%d")
    return_datetimes = []
    curr_datetime = start_datetime
    while curr_datetime <= end_datetime:
        return_datetimes.append(curr_datetime)
        curr_datetime += datetime.timedelta(days=1)
    return return_datetimes


def read_time_delta(time_str: str) -> datetime.timedelta:
    """Function to take a time in 'H:M:S' format and return the
    timedelta object
    """
    t = datetime.datetime.strptime(time_str, "%H:%M:%S")
    return datetime.timedelta(hours=t.hour, minutes=t.minute, seconds=t.second)


def clip_time(
    t: datetime.datetime,
    min_time: typing.Optional[datetime.datetime] = None,
    max_time: typing.Optional[datetime.datetime] = None,
) -> datetime.datetime:
    """
    Clips a time to be within an interval [min_time, max_time]
    """
    if min_time and t < min_time:
        return min_time
    if max_time and t > max_time:
        return max_time
    return t


def get_nearest_time_rounded_off_in_seconds(
    date_time: datetime.datetime,
    time_window_in_milliseconds: int,
) -> str:
    """
    Rounds off a datetime object's minutes, seconds to
    nearest second such that it is a multiple of time_window_in_milliseconds
    """
    partial_timestamp = date_time.minute * 60 + date_time.second
    # 1: converting partial_timestamp to milliseconds
    # 2: Get remainder in milliseconds and convert it to seconds
    # 3: subtract the remainder from partial timestamp
    partial_timestamp = (
        partial_timestamp
        - ((partial_timestamp * 1000) % time_window_in_milliseconds) // 1000
    )
    return str(partial_timestamp)


def get_start_and_end_times_for_delta_time(
    date_time: datetime.datetime, time_delta_in_milliseconds: int
) -> typing.Tuple[
    typing.Optional[datetime.datetime], typing.Optional[datetime.datetime]
]:
    """
    Returns a tuple of datetime objects that are -,+ (respectively) of timedelta (in milliseconds)
    """
    try:
        return (
            date_time
            - datetime.timedelta(milliseconds=time_delta_in_milliseconds),
            date_time
            + datetime.timedelta(milliseconds=time_delta_in_milliseconds),
        )

    except Exception as e:  # pylint: disable=broad-except
        log.exception(
            "Failed to convert time into datetime!",
            exc_info=e,
            time_to_convert=str(date_time),
        )
        return None, None


def normalize_datetime_with_format(
    value: datetime.datetime, format_: str = "%Y-%m-%dT%H:%M:%S.%f"
) -> str:
    """
    Transform datetime to string in the format %Y-%m-%dT%H:%M:%S.
    """
    return value.strftime(format_) + "Z"


def get_time_overlap(
    time_range_1: typing.Tuple[datetime.time, datetime.time],
    time_range_2: typing.Tuple[datetime.time, datetime.time],
) -> float:
    """return time overlap value between two 'Time' intervals"""
    today = datetime.date.today()
    return max(
        0,
        (
            datetime.datetime.combine(
                today, min(time_range_1[1], time_range_2[1])
            )
            - datetime.datetime.combine(
                today, max(time_range_1[0], time_range_2[0])
            )
        ).total_seconds(),
    )


def get_time_from_time_str(val: str):
    """
    Returns the time value from HH:mm:ss string passed to the function
    """
    return dateutil.parser.parse(val).time()


def check_same_date_utc(epoch_ts1: float, epoch_ts2: float) -> bool:
    """Return True if two given epoch timestamps fall on same date (UTC) else
    False."""
    date_1 = datetime.datetime.utcfromtimestamp(epoch_ts1)
    date_2 = datetime.datetime.utcfromtimestamp(epoch_ts2)
    return (
        (date_1.year == date_2.year)
        and (date_1.month == date_2.month)
        and (date_1.day == date_2.day)
    )


def convert_timezoned_timestr_to_utc(
    original_datetime_str: str,
    tzone: str,
    day_of_week: str = None,
) -> datetime.datetime:
    """
    Returns the UTC datetime object based on timezone scoped time_str(hh:mm:ss) and timezone passed.
    For instance, 14:30 IST is 09:00 UTC.
    If the day of week is specified, the conversion to UTC is done for that specified day.

    """
    tz_obj = pytz.timezone(tzone)
    dt_localised = tz_obj.localize(
        dateutil.parser.parse(original_datetime_str)
    )
    if day_of_week is None:
        return dt_localised.astimezone(pytz.UTC)

    curr_day_of_week = dt_localised.strftime("%A").upper()
    while curr_day_of_week != day_of_week:
        dt_localised = dt_localised - datetime.timedelta(days=1)
        curr_day_of_week = dt_localised.strftime("%A").upper()
    return dt_localised.astimezone(pytz.UTC)


def convert_utc_timestr_to_localized_timestr(
    utc_datetime_str: str, tzone: str, day_of_week: str = None
):
    """
    Returns the timezone aware datetime object from the UTC datetime string and timezone passed
    """
    tz_obj = pytz.timezone(tzone)
    dt_utc = dateutil.parser.parse(utc_datetime_str).replace(tzinfo=pytz.utc)

    if day_of_week is None:
        return dt_utc.astimezone(tz_obj)

    curr_day_of_week = dt_utc.strftime("%A").upper()
    while curr_day_of_week != day_of_week:
        dt_utc = dt_utc + datetime.timedelta(days=1)
        curr_day_of_week = dt_utc.strftime("%A").upper()
    return dt_utc.astimezone(tz_obj)


def utc_to_milliseconds(utc_datetime: datetime.datetime):
    # Ensure the datetime is in UTC
    if utc_datetime.tzinfo is None:
        # If the datetime is naive, set it to UTC
        utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
    elif utc_datetime.tzinfo != timezone.utc:
        # If the datetime is timezone-aware but not in UTC, convert it to UTC
        utc_datetime = utc_datetime.astimezone(timezone.utc)

    # Calculate milliseconds since epoch
    epoch = datetime.datetime(1970, 1, 1, tzinfo=timezone.utc)
    milliseconds = int((utc_datetime - epoch).total_seconds() * 1000)
    return milliseconds


def convert_str_to_time(time_str: str, format="%H:%M:%S"):
    return datetime.datetime.strptime(time_str, format).time()


def get_all_days_of_week():
    return [
        "SUNDAY",
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
    ]


def get_next_day_of_week(current_day: str):
    days_of_week = get_all_days_of_week()
    current_day_upper = current_day.upper()
    if current_day_upper not in days_of_week:
        raise ValueError(f"'{current_day}' is not a valid day of the week.")

    current_index = days_of_week.index(current_day_upper)
    next_index = (current_index + 1) % len(days_of_week)
    return days_of_week[next_index]


def get_today_midnight_in_utc_for_tz(time_zone_str):
    try:
        local_tz = pytz.timezone(time_zone_str)
        now = datetime.datetime.now(local_tz)
        local_midnight = datetime.datetime.combine(now.date(), time(0, 0, 0))
        local_midnight = local_tz.localize(local_midnight)
        return local_midnight.astimezone(pytz.utc)
    except pytz.UnknownTimeZoneError:
        return None


def format_utc_to_local_with_weekday(
    utc_dt: datetime.datetime, tz_name: str
) -> str:
    """
    Convert a UTC datetime (naive or tz-aware) to the given timezone,
    and return a string like "YYYY-MM-DD HH:MM:SS (Weekday) TZ".
    """
    if utc_dt.tzinfo is None:
        utc_dt = utc_dt.replace(tzinfo=timezone.utc)
    else:
        utc_dt = utc_dt.astimezone(timezone.utc)

    local_dt = convert_utc_to_tz(utc_dt, tz_name)
    weekday = local_dt.strftime("%A")
    return f"{local_dt.strftime('%Y-%m-%d %H:%M:%S')} ({weekday}) {tz_name}"
