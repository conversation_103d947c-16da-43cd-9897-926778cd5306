import dataclasses
from typing import Optional

import structlog

from common_utils.bridge.msg_sender import Msg<PERSON>ender
from common_utils.s3_utils import get_entity_metadata
from config import backend_config as config
from models_rds.audio_devices import AudioDevices

SUPPORTED_AUDIO_DEVICE_TYPES = (
    "AXIS",
    "ONVIF",
    "ACS",
    "AXIS_SIREN",
    "SIP",
    "EAGLE_EYE",
    "GO2RTC_ONVIF",
    "VDS",
    "UNV",
)

log = structlog.get_logger("hakimo", module="Audio Helper")


class AudioPayload:
    """Class representing payload posted to HIP for generating audio alerts in HIP."""

    @dataclasses.dataclass
    class _AudioPayload:  # pylint: disable=too-many-instance-attributes
        camera_client_id: str
        client_audio_device_id: str
        type: str
        url: str
        talkdown_type: str
        username: Optional[str] = None
        clip_name: Optional[str] = None
        s3_file_path: Optional[str] = None
        password_file_path: Optional[str] = None

    def __init__(  # pylint: disable=too-many-arguments,too-many-locals
        self,
        message_sender: MsgSender,
        audio_device: AudioDevices,
        client_camera_id: str,
        talkdown_type: str = "automated",
        s3_file_path: Optional[str] = None,
    ):
        self._msg_sender = message_sender
        audio_payload = self._AudioPayload(
            camera_client_id=client_camera_id,
            client_audio_device_id=audio_device.client_audio_device_id,
            type=audio_device.device_type,
            url=audio_device.url,
            talkdown_type=talkdown_type,
            username=audio_device.user_name,
            clip_name=audio_device.default_audio_clip_name,
            password_file_path=audio_device.password_file_path,
            s3_file_path=s3_file_path,
        )
        self._payload = dataclasses.asdict(audio_payload)
        if s3_file_path is None:
            self._payload.pop("s3_file_path")

        if audio_device.default_audio_clip_name is None:
            self._payload.pop("clip_name")

        if audio_device.password_file_path is None:
            self._payload.pop("password_file_path")

    def _preflight_check(self, tenant_id: str) -> bool:
        """Preflight check before posting audio payload to the message bus."""

        device_type = self._payload["type"]
        clip_name = self._payload.get("clip_name")
        s3_file_path = self._payload.get("s3_file_path")

        if device_type not in SUPPORTED_AUDIO_DEVICE_TYPES:
            log.error(
                "Unsupported audio device type.",
                device_type=device_type,
                supported_types=SUPPORTED_AUDIO_DEVICE_TYPES,
            )
            return False

        if s3_file_path is None and clip_name is None:
            log.error(
                "Payload should have either of fields s3_file_path or clip_name specified.",
            )
            return False

        if (
            device_type in ["ONVIF", "ACS", "SIP", "EAGLE_EYE"]
            and s3_file_path is None
            and clip_name is not None
        ):
            # check in tenant S3 bucket the clipname is present in
            # case we are playing an automated clip.  For ONVIF
            # cameras we upload the clip in tenant S3 bucket which is
            # synced via s3fs to HIP.
            cloudfront_config = config.gateway().get("cloudfront", {})
            s3_bucket = cloudfront_config["origin_s3_bucket"]
            clip_path = f"{tenant_id}/{self._payload['clip_name']}"
            s3_metadata = get_entity_metadata(
                bucket_name=s3_bucket, path=clip_path
            )
            if s3_metadata is None:
                log.error(
                    "Audio clip not present in s3 bucket.",
                    tenant_id=tenant_id,
                    bucket_name=s3_bucket,
                    clip_path=clip_path,
                    device_type=device_type,
                )
                return False

        return True

    def post_message(
        self,
        tenant_id: str,
        source_system: str,
        alarm_id: Optional[str] = None,
    ) -> bool:
        """Post a message to the message bus to play audio in appliance."""
        if not self._preflight_check(tenant_id):
            return False

        if alarm_id is not None:
            self._payload["alarm_id"] = alarm_id

        return self._msg_sender.send(
            tenant_id,
            f"hip/{source_system}/play_audio",
            self._payload,
        )
