"""Tests for video utils functions"""

import os
import os.path as osp
from datetime import datetime, timedelta

import cv2
import numpy as np
import pytest

from common_utils.video_utils import (
    check_video_frames,
    encode_frame_to_webp_base64,
    extract_and_encode_frames,
    get_batch_from_video_cv2,
    get_frame_ts,
    get_video_duration,
    get_video_reader,
)


class MockVideoReader:
    def __init__(self, video_path, duration, frame_rate):
        os.system(
            f"ffmpeg -t {duration} -s 640x480 -f rawvideo -pix_fmt rgb24 -r {frame_rate} -i /dev/zero {video_path}"
        )
        self.timestamps = get_frame_ts(video_path)
        self.fps = frame_rate
        self.width = 480
        self.height = 640
        self.end_frame = len(self.timestamps)
        self.current_frame = 0

    def read(self):
        if self.current_frame < self.end_frame:
            value = self.timestamps[self.current_frame]
            self.current_frame += 1
            return True, value
        else:
            return False, None

    def nextFrame(self):
        return self.read()

    def grab(self):
        if self.current_frame < self.end_frame:
            self.current_frame += 1


class TestVideoUtils:
    def test_file_not_found(self):
        """
        Test to see if file does not exist causes None to be returned
        """
        assert get_video_reader("hokie_pokie.mp4") is None

    def test_file_not_video(self):
        """
        Test to see if ValueError is returned if file is not a video file
        """
        assert get_video_reader("hokie_pokie.mp3") is None

    def test_file_read(self, tmpdir):
        """
        Test to see if video is correctly read and properties are correct
        """
        outfile = osp.join(tmpdir, "empty.mp4")
        os.system(
            f"ffmpeg -t 5 -s 640x480 -f rawvideo -pix_fmt rgb24 -r 25 -i /dev/zero {outfile}"
        )
        vid_cap = get_video_reader(outfile)
        assert vid_cap.reader.get(cv2.CAP_PROP_FPS) == 25
        assert vid_cap.reader.get(cv2.CAP_PROP_FRAME_WIDTH) == 640
        assert vid_cap.reader.get(cv2.CAP_PROP_FRAME_HEIGHT) == 480
        assert vid_cap.reader.get(cv2.CAP_PROP_POS_FRAMES) == 0

    def test_check_video_frames(self, tmpdir):
        outfile = osp.join(tmpdir, "empty.mp4")
        os.system(
            f"ffmpeg -t 5 -s 640x480 -f rawvideo -pix_fmt rgb24 -r 25 -i /dev/zero {outfile}"
        )
        assert check_video_frames(outfile, 10)
        assert not check_video_frames("hokie_pokie.mp4", 10)

    def test_get_video_duration(self, tmpdir):
        outfile = osp.join(tmpdir, "empty.mp4")
        os.system(
            f"ffmpeg -t 5 -s 640x480 -f rawvideo -pix_fmt rgb24 -r 25 -i /dev/zero {outfile}"
        )
        assert get_video_duration(outfile) == 5.0
        vid_cap = cv2.VideoCapture(outfile)
        assert get_video_duration(vid_cap) == 5.0
        vid_cap.release()

    @pytest.mark.parametrize(
        "frame_rate, duration, batch_size, processing_rate, offset, n_expected_batches, pad_batch",
        [
            (30, 5, 5, 1, True, 1, True),
            (1.2, 5, 5, 1, True, 2, True),
            (1, 6, 5, 1, True, 2, True),
            (4.2, 5, 5, 1, True, 2, True),
            (2, 7, 5, 1, True, 2, True),
            (1.1, 10, 5, 1, True, 3, True),
            (4.2, 5, 6, 1, True, 1, True),
            (1.2, 5, 6, 1, True, 1, True),
            (1, 5, 5, 1, True, 1, True),
            (1, 5, 6, 1, True, 1, True),
            (30.2, 5, 5, 1, True, 2, True),
            (30.2, 5, 6, 1, True, 1, True),
            (2.4, 5, 5, 1, True, 2, True),
            (2.4, 5, 6, 1, True, 1, True),
            (1.6, 5, 5, 1, True, 2, True),
            (1.6, 5, 7, 1, True, 2, True),
            (1.6, 5, 8, 1, True, 1, True),
            (3.8, 5, 5, 1, True, 2, True),
            (3.8, 5, 6, 1, True, 2, True),
            (3.8, 5, 8, 1, True, 1, True),
            (3.8, 5, 10, 1, True, 1, True),
            (3.8, 5, 5, 1, False, 2, True),
            (3.8, 5, 5, 1, False, 2, False),
        ],
    )
    def test_get_batch_from_video_cv2(
        self,
        tmpdir,
        frame_rate,
        duration,
        batch_size,
        processing_rate,
        offset,
        n_expected_batches,
        pad_batch,
    ):
        video_path = osp.join(tmpdir, "empty.mp4")
        vid_reader = MockVideoReader(video_path, duration, frame_rate)
        n_batches = 0
        for (
            frames,
            img_batch,
            _,
            offset_img_batch,
        ) in get_batch_from_video_cv2(
            vid_reader,
            processing_rate,
            batch_size,
            offset,
            pad_batch,
        ):
            assert len(img_batch) == (batch_size if pad_batch else len(frames))
            if offset:
                assert len(offset_img_batch) == (
                    batch_size if pad_batch else len(frames)
                )
            n_batches += 1
        assert n_batches == n_expected_batches


def create_dummy_video(video_path, frame_count=10, fps=1):
    height, width = 64, 64
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
    for _ in range(frame_count):
        frame = np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)
        out.write(frame)
    out.release()


class TestFrameEncodingUtils:
    def test_encode_frame_to_webp_base64(self):
        dummy_frame = np.random.randint(0, 256, (64, 64, 3), dtype=np.uint8)
        result = encode_frame_to_webp_base64(dummy_frame)
        assert isinstance(result, str)
        assert result.startswith("UklGR")  # WebP magic number in base64

    def test_extract_and_encode_frames(self, tmpdir):
        video_path = osp.join(tmpdir, "test_video.mp4")
        create_dummy_video(video_path, frame_count=5, fps=1)

        start_time = datetime.now()
        end_time = start_time + timedelta(seconds=5)

        frames_b64 = extract_and_encode_frames(
            video_path, (start_time, end_time), fps=1
        )
        assert isinstance(frames_b64, list)
        assert all(isinstance(f, str) for f in frames_b64)
        assert len(frames_b64) > 0
