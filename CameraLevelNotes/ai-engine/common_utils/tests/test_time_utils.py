import datetime as dt
from datetime import datetime, time, timedelta, timezone

import dateutil
import pytest
import pytz

from common_utils.time_utils import (
    check_same_date_utc,
    clip_time,
    convert_timezoned_timestr_to_utc,
    convert_utc_timestr_to_localized_timestr,
    convert_utc_to_tz,
    format_utc_to_local_with_weekday,
    get_start_and_end_times_for_delta_time,
    get_time_from_time_str,
    get_today_midnight_in_utc_for_tz,
    utc_to_milliseconds,
)


def test_get_start_and_end_times_for_delta_time():
    test_time_1 = datetime(2022, 3, 18, 2, 40, 26, 193515)
    (
        time_delta_start_1,
        time_delta_end_1,
    ) = get_start_and_end_times_for_delta_time(
        test_time_1, time_delta_in_milliseconds=1000
    )
    assert (test_time_1 - time_delta_start_1).seconds == 1
    assert (time_delta_end_1 - test_time_1).seconds == 1

    test_time_2 = datetime(2022, 3, 18, 2, 40, 26)
    (
        time_delta_start_2,
        time_delta_end_2,
    ) = get_start_and_end_times_for_delta_time(
        test_time_2, time_delta_in_milliseconds=2000
    )
    assert (test_time_2 - time_delta_start_2).seconds == 2
    assert (time_delta_end_2 - test_time_2).seconds == 2

    test_time_3 = dateutil.parser.parse("2022-06-15 06:03:08.82193")
    (
        time_delta_start_3,
        time_delta_end_3,
    ) = get_start_and_end_times_for_delta_time(
        test_time_3, time_delta_in_milliseconds=1000
    )
    assert (test_time_3 - time_delta_start_3).seconds == 1
    assert (time_delta_end_3 - test_time_3).seconds == 1


@pytest.mark.parametrize(
    "input_time,timezone,output_time",
    [
        (
            datetime(2022, 1, 2, 19, tzinfo=pytz.utc),
            "Pacific/Honolulu",
            datetime(2022, 1, 2, 9),
        ),
        (
            datetime(2022, 9, 1, 19, tzinfo=pytz.utc),
            "America/Phoenix",
            datetime(2022, 9, 1, 12),
        ),
        (
            datetime(2022, 12, 1, 19, tzinfo=pytz.utc),
            "America/New_York",
            datetime(2022, 12, 1, 14),
        ),
    ],
)
def test_timezone_conversion(input_time, timezone, output_time):
    assert convert_utc_to_tz(input_time, timezone).strftime(
        "%Y-%m-%d %H:%M:%S"
    ) == output_time.strftime("%Y-%m-%d %H:%M:%S")


@pytest.mark.parametrize(
    "t,min_time,max_time,return_t",
    [
        (
            datetime(2022, 1, 1, 10, 0, 10),
            datetime(2022, 1, 1, 10, 0, 0),
            datetime(2022, 1, 1, 10, 0, 20),
            datetime(2022, 1, 1, 10, 0, 10),
        ),
        (
            datetime(2022, 1, 1, 9, 59, 50),
            datetime(2022, 1, 1, 10, 0, 0),
            datetime(2022, 1, 1, 10, 0, 20),
            datetime(2022, 1, 1, 10, 0, 0),
        ),
        (
            datetime(2022, 1, 1, 10, 0, 30),
            datetime(2022, 1, 1, 10, 0, 0),
            datetime(2022, 1, 1, 10, 0, 20),
            datetime(2022, 1, 1, 10, 0, 20),
        ),
        (
            datetime(2022, 1, 1, 10, 0, 10),
            None,
            datetime(2022, 1, 1, 10, 0, 20),
            datetime(2022, 1, 1, 10, 0, 10),
        ),
        (
            datetime(2022, 1, 1, 10, 0, 10),
            datetime(2022, 1, 1, 10, 0, 0),
            None,
            datetime(2022, 1, 1, 10, 0, 10),
        ),
        (
            datetime(2022, 1, 1, 10, 0, 30),
            None,
            datetime(2022, 1, 1, 10, 0, 20),
            datetime(2022, 1, 1, 10, 0, 20),
        ),
        (
            datetime(2022, 1, 1, 9, 59, 40),
            datetime(2022, 1, 1, 10, 0, 0),
            None,
            datetime(2022, 1, 1, 10, 0, 0),
        ),
    ],
)
def test_clip_time(t, min_time, max_time, return_t):
    assert return_t == clip_time(t, min_time, max_time)


@pytest.mark.parametrize(
    "time_str,response",
    [
        ("13:00:00", dt.time(hour=13, minute=0, second=0)),
        ("00:00:00", dt.time(hour=0, minute=0, second=0)),
        ("13:00", dt.time(hour=13, minute=0, second=0)),
    ],
)
def test_get_time_from_ts_str(time_str, response):
    resp = get_time_from_time_str(time_str)
    assert resp == response


@pytest.mark.parametrize(
    "ts1,ts2,expected",
    [
        # 1689324746: 2023-07-14 08:52:26 UTC
        (1689324746, 1689324746, True),
        (1689324746, 1689324746 - (8 * 60 * 60 + 52 * 60), True),
        (1689324746, 1689324746 - (8 * 60 * 60 + 52 * 60 + 26), True),
        (1689324746, 1689324746 - (8 * 60 * 60 + 52 * 60 + 27), False),
        (1689324746, 1689324746 - 86400, False),
    ],
)
def test_check_same_date_utc(ts1, ts2, expected):
    if expected:
        assert check_same_date_utc(ts1, ts2)
    else:
        assert not check_same_date_utc(ts1, ts2)


@pytest.mark.parametrize(
    "original_timestr, original_tzone, original_day_of_week, expected_utc_timestr, expected_day_of_week",
    [
        ("14:54:00", "Asia/Kolkata", None, "09:24:00", None),
        ("14:54:00", "UTC", None, "14:54:00", None),
        ("02:00:00", "Asia/Kolkata", "SUNDAY", "20:30:00", "SATURDAY"),
        ("14:54:00", "UTC", "MONDAY", "14:54:00", "MONDAY"),
        # @TODO: Do this in a DST independent way
        # ("02:00:00", "America/Los_Angeles", "FRIDAY", "09:00:00", "FRIDAY"),
        # ("18:00:00", "America/Los_Angeles", "SUNDAY", "01:00:00", "MONDAY"),
        # ("02:00:00", "US/Eastern", "FRIDAY", "06:00:00", "FRIDAY"),
        # ("23:00:00", "US/Eastern", "FRIDAY", "03:00:00", "SATURDAY"),
        # ("02:20:00", "America/Los_Angeles", None, "09:20:00", None),
    ],
)
def test_convert_timezoned_timestr_to_utc(
    original_timestr,
    original_tzone,
    original_day_of_week,
    expected_utc_timestr,
    expected_day_of_week,
):
    resp_dt = convert_timezoned_timestr_to_utc(
        original_timestr, original_tzone, day_of_week=original_day_of_week
    )
    assert resp_dt.strftime("%H:%M:%S") == expected_utc_timestr
    assert (
        resp_dt.strftime("%A").upper() == expected_day_of_week
        if expected_day_of_week is not None
        else True
    )


@pytest.mark.parametrize(
    "original_utc_timestr,utc_day_of_week,tzone,expected_localized_timestr,expected_localized_day_of_week",
    [
        ("09:24:00", None, "Asia/Kolkata", "14:54:00", None),
        ("20:00:00", "SUNDAY", "Asia/Kolkata", "01:30:00", "MONDAY"),
        ("14:54:00", "MONDAY", "UTC", "14:54:00", "MONDAY"),
        # TODO: Support this this in a DST independent way.
        # ("02:00:00", None, "US/Eastern", "22:00:00", None),
        # (
        #             "03:00:00",
        #             "THURSDAY",
        #             "America/Los_Angeles",
        #             "20:00:00",
        #             "WEDNESDAY",
        #         ),
        # ("09:20:00", None, "America/Los_Angeles", "02:20:00", None),
        # ("06:00:00", None, "US/Eastern", "02:00:00", None),
        ("01:00:00", "SUNDAY", "Canada/Eastern", "21:00:00", "SATURDAY"),
        ("20:00:00", "SUNDAY", "Canada/Eastern", "16:00:00", "SUNDAY"),
    ],
)
def test_convert_utc_timestr_to_localized_timestr(
    original_utc_timestr,
    utc_day_of_week,
    tzone,
    expected_localized_timestr,
    expected_localized_day_of_week,
):
    resp = convert_utc_timestr_to_localized_timestr(
        original_utc_timestr, tzone, utc_day_of_week
    )
    assert resp.strftime("%H:%M:%S") == expected_localized_timestr
    assert (
        resp.strftime("%A").upper() == expected_localized_day_of_week
        if expected_localized_day_of_week is not None
        else True
    )


@pytest.mark.parametrize(
    "input_datetime, expected",
    [
        (datetime(2020, 1, 1, 12), 1577880000000),  # Naive datetime
        (
            datetime(2020, 1, 1, 12, tzinfo=timezone.utc),
            1577880000000,
        ),  # UTC datetime
        (
            datetime(2020, 1, 1, 12, tzinfo=timezone(timedelta(hours=-5))),
            1577898000000,
        ),  # Non-UTC datetime
        (datetime(1970, 1, 1, tzinfo=timezone.utc), 0),  # Epoch
        (
            datetime(2050, 1, 1, 12, tzinfo=timezone.utc),
            2524651200000,
        ),  # Future date
        (
            datetime(1960, 1, 1, 12, tzinfo=timezone.utc),
            -315576000000,
        ),  # Past date
    ],
)
def test_utc_to_milliseconds(input_datetime, expected):
    result = utc_to_milliseconds(input_datetime)
    assert result == expected


@pytest.mark.parametrize(
    "site_tz, expected_midnight_datatime",
    [
        (
            "Asia/Kolkata",
            pytz.utc.localize(
                datetime.combine(
                    datetime.now(pytz.timezone("Asia/Kolkata")), time(0, 0, 0)
                )
                - timedelta(hours=5, minutes=30)
            ),
        ),
        (
            "Asia/Shanghai",
            pytz.utc.localize(
                datetime.combine(
                    datetime.now(pytz.timezone("Asia/Shanghai")), time(0, 0, 0)
                )
                - timedelta(hours=8, minutes=0)
            ),
        ),
        (
            "UTC",
            pytz.utc.localize(
                datetime.combine(
                    datetime.now(pytz.timezone("UTC")), time(0, 0, 0)
                )
                - timedelta(hours=0, minutes=0)
            ),
        ),
        ("US/Colorado", None),
    ],
)
def test_convert_timezoned_timestr_to_utc(
    site_tz,
    expected_midnight_datatime,
):
    today_midnight_in_utc_for_tz = get_today_midnight_in_utc_for_tz(site_tz)
    assert today_midnight_in_utc_for_tz == expected_midnight_datatime


@pytest.mark.parametrize(
    "utc_dt,tz_name,expected_prefix,expected_weekday",
    [
        # naive UTC → US/Eastern (EDT)
        (
            datetime(2025, 6, 11, 8, 43, 18),
            "US/Eastern",
            "2025-06-11 04:43:18",
            "Wednesday",
        ),
        # UTC‐aware → US/Pacific (PDT)
        (
            datetime(2025, 12, 25, 18, 0, 0, tzinfo=timezone.utc),
            "US/Pacific",
            "2025-12-25 10:00:00",
            "Thursday",
        ),
        # crossing date boundary: late UTC to Asia/Kolkata next day
        (
            datetime(2025, 6, 11, 20, 30, 0, tzinfo=timezone.utc),
            "Asia/Kolkata",
            "2025-06-12 02:00:00",
            "Thursday",
        ),
    ],
)
def test_format_utc_to_local_with_weekday(
    utc_dt, tz_name, expected_prefix, expected_weekday
):
    out = format_utc_to_local_with_weekday(utc_dt, tz_name)
    # "<YYYY-MM-DD HH:MM:SS> (Weekday) TZ"
    assert out.startswith(expected_prefix)
    assert f"({expected_weekday})" in out
    assert out.endswith(tz_name)
