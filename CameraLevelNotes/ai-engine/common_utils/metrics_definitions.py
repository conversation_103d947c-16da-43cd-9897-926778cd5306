"""Contains definitions of the metrics emitted."""

from typing import Final

import prometheus_client as prom
from prometheus_client import CollectorRegistry

from interfaces.camera_details import CameraStatus

mysql_read_latency_metric: Final[str] = "hakimo_mysql_read_latency"
mysql_write_latency_metric: Final[str] = "hakimo_mysql_write_latency"

# Keep name of any metrics emitted by appliance starting from
# "hakimo_". Examples: "hakimo_acs_api_summary_metric",
# "hakimo_audio_playback_counter". The prometheus configuration that
# scrapes metrics from appliance has a pattern of metrics names to
# scrape and pattern specified to scrape HIP appliaction metrics is
# "hakimo_*".
# See: <ai-engine-repo-root>/infra/prometheus-configuration/prometheus-config.py


ALARM_STATE_PROM = prom.Summary(
    "alarm_state_timer",
    "Measures how much time an alarm spends in a given state",
    labelnames=("alarm_state", "tenant_id"),
)

TOTAL_ALARM_PROCESSING_TIME = prom.Summary(
    "total_alarm_processing_time",
    documentation="Time Summary for total alarm processing including ML Service call",
    labelnames=["tenant"],
)

ML_ALARM_PROCESSING_TIME = prom.Summary(
    "ml_alarm_processing_time",
    documentation="Time Summary for only ML Inference alarm processing",
    labelnames=["tenant"],
)

ALARM_PIPELINE_DELAY = prom.Summary(
    "alarm_pipeline_delay",
    documentation="time taken for the alarm to be processed via the Hakimo Pipeline from the time of alarm creation",
    labelnames=["tenant"],
)

VIDEO_COUNT = prom.Counter(
    "video_count",
    "Number of videos",
    labelnames=["tenant", "corrupt"],
)
ALARM_QUEUE_LENGTH = prom.Gauge(
    "alarm_queue_length",
    "Length of queue waiting for ml processor",
    labelnames=["tenant"],
)
ALARM_COUNT = prom.Counter(
    "alarm_processing",
    "Alarm processing count",
    labelnames=["tenant", "result"],
)
HAKIMO_API_TIMINGS = prom.Summary(
    "hakimo_gateway_responses",
    documentation="Time taken and status code for responses "
    "from gateway per endpoint",
    labelnames=["api_path", "status_code"],
)
PENDING_MESSAGES_MSG_BUS = prom.Gauge(
    "pending_messages_msg_bus",
    documentation="Number of pending messages in message bus",
    labelnames=["tenant_id", "topic"],
)

ALARM_DELAY_SUMMARY = prom.Summary(
    "alarm_add_delay",
    documentation="Time taken for an alarm to be added to DB",
    labelnames=[
        "tenant",
        "source_system",
        "alarm_type",
        "alarm_type_internal",
        "video_required",
    ],
)


VIDEO_REQUEST_COUNTER = prom.Counter(
    "video_request",
    documentation="Count of video requests",
    labelnames=["tenant", "request_type"],
)


LIVESTREAM_HEARTBEAT_STATUS = prom.Enum(
    "livestream_heartbeat_status",
    documentation="Status of livestream heartbeat",
    states=["failure", "success"],
    labelnames=["tenant"],
)

CAMERA_CONNECTION_STATUS = prom.Enum(
    "camera_connection_status",
    documentation="Status of Camera Connection with Hakimo",
    states=[s.name for s in CameraStatus],
    labelnames=["tenant", "camera_name"],
)

CAMERA_RTSP_STREAM_STATUS = prom.Enum(
    "camera_rtsp_stream_status",
    documentation="Status of Camera RTSP Stream with Hakimo",
    states=[s.name for s in CameraStatus],
    labelnames=["tenant", "camera_name"],
)

LIVESTREAM_HEARTBEAT_DELAY_SUMMARY = prom.Summary(
    "livestream_heartbeat_delay",
    documentation="Time difference between the last time a heartbeat was received and the current time",
    labelnames=["tenant"],
)


VIDEO_MISSING_COUNTER = prom.Counter(
    "video_missing",
    documentation="Video requests with no video.",
    labelnames=["tenant", "source_system"],
)

VIDEO_FILE_LENGTH_SUMMARY = prom.Summary(
    "video_file_length",
    documentation="Length of video file",
    labelnames=["tenant"],
)

VIDEO_FILE_SIZE_SUMMARY = prom.Summary(
    "video_file_size",
    documentation="Size of video file",
    labelnames=["tenant"],
)

ADD_ALARM_COUNTER = prom.Counter(
    "add_raw_alarm_requests",
    documentation="Count of add_raw_alarm requests",
    labelnames=["tenant", "alarm_type"],
)

LOCATION_ALARM_COUNTER = prom.Counter(
    "location_alarm",
    documentation="Count of location alarms",
    labelnames=["tenant"],
)

ADD_ALARM_VIDEO_COUNTER = prom.Counter(
    "add_alarm_video_requests",
    documentation="Count of add_alarm_video requests",
    labelnames=["tenant"],
)

ALARM_VIDEO_DELAY = prom.Summary(
    "alarm_video_delay",
    documentation="Time taken for video to come in for a given alarm",
    labelnames=["tenant"],
)

TALKDOWN_EVENT_LATENCY = prom.Summary(
    "talkdown_event_latency",
    documentation="Time taken for talkdown event since alarm timestamp",
    labelnames=["tenant", "talkdown_type"],
)

HAKIMO_TALKDOWN_SPEAKER_LATENCY = prom.Summary(
    "hakimo_talkdown_speaker_latency",
    documentation="Time taken for talkdown to speaker",
    labelnames=["tenant", "talkdown_type", "device_type", "is_success"],
)

HAKIMO_TALKDOWN_SPEAKER_E2E_LATENCY = prom.Summary(
    "hakimo_talkdown_speaker_e2e_latency",
    documentation="Time taken for talkdown to speaker from end to end",
    labelnames=["tenant", "talkdown_type", "device_type", "is_success"],
)

# Latency metric for autotalkdown alarm time and talkdown time
HAKIMO_ALARM_AUDIO_TIME_DIFFERENCE = prom.Summary(
    "hakimo_alarm_audio_time_difference",
    documentation="Time Difference between raw alarm and auto or manual talkdown",
    labelnames=[
        "tenant",
        "device_type",
        "is_success",
    ],
)

HAKIMO_TOTAL_TIME_BETWEEN_ALARM_TALKDOWN = prom.Summary(
    "hakimo_total_time_between_alarm_talkdown",
    documentation="Total Time time taken between generation of raw alarm and talkdown completion",
    labelnames=[
        "tenant",
        "device_type",
        "is_success",
        "talkdown_type",
        "device",
    ],
)

ALARM_STATUS_CHANGE_COUNTER = prom.Counter(
    "alarm_status_change",
    documentation="Alarm status changes",
    labelnames=["tenant", "old_status", "new_status"],
)

TAILGATING_ALARM_LATENCY = prom.Summary(
    "tailgating_alarm_latency",
    documentation="Time taken for tailgating alarm to be generated",
    labelnames=["tenant"],
)

# Definitions of metrics emitted from HIP
HAKIMO_HIP_EVENT_COUNTER = prom.Counter(
    "hakimo_hip_event_count",
    documentation="Count of events registered at HIP",
    labelnames=["source_system", "is_backfill"],
)

# MySQL read and write latency
MYSQL_READ_SUMMARY = prom.Summary(
    mysql_read_latency_metric,
    documentation="Latency of read query",
    labelnames=["tenant", "query", "table_name"],
)

MYSQL_WRITE_SUMMARY = prom.Summary(
    mysql_write_latency_metric,
    documentation="Latency of write query",
    labelnames=["tenant", "query", "table_name"],
)

# ACS Integrations API call time summary
HIP_CUSTOM_REGISTRY = CollectorRegistry()
ACS_API_CALL_SUMMARY = prom.Summary(
    "hakimo_acs_api_summary_metric",
    documentation="Latency of ACS API call from the appliance",
    labelnames=[
        "integration",
        "call_type",
        "api_path",
        "status_code",
    ],
    registry=HIP_CUSTOM_REGISTRY,
)

HAKIMO_VIDEO_REQUEST_COUNTER = prom.Counter(
    "hakimo_video_request_counter",
    documentation="Count of video requests at appliance",
    labelnames=["is_success", "error_type", "tenantName"],
    registry=HIP_CUSTOM_REGISTRY,
)

# Audio playback metrics (from appliance)
HAKIMO_AUDIO_PLAYBACK_COUNTER = prom.Counter(
    "hakimo_audio_playback_counter",
    documentation="Count of audio playbacks at appliance.",
    labelnames=[
        "device_type",  # ACS, AXIS, ONVIF
        "playback_type",  # automated, manual
        "is_success",  # True, False
    ],
    registry=HIP_CUSTOM_REGISTRY,
)

# Audio talkdown metrics (from cloud)
HAKIMO_AUDIO_TALKDOWN_COUNTER = prom.Counter(
    "hakimo_audio_talkdown_counter",
    documentation="Count of audio talk-down on speakers",
    labelnames=[
        "tenant",
        "device_type",  # ACS, AXIS, ONVIF
        "playback_type",  # automated, manual
        "is_success",  # True, False
        "http_status",  # 200, 400, 500
    ],
)

HAKIMO_STROBE_LIGHT_COUNTER = prom.Counter(
    "hakimo_strobe_light_counter",
    documentation="Count of audio talk-down on speakers",
    labelnames=[
        "tenant",
        "device_type",  # ACS, AXIS, ONVIF
        "playback_type",  # automated, manual
        "is_success",  # True, False
        "http_status",  #  200, 400, 500
    ],
)

HAKIMO_QUEUE_LATENCY = prom.Summary(
    "hakimo_queue_latency",
    documentation="Latency of message queue",
    labelnames=["tenant", "queue_type", "queue_name"],
)

HAKIMO_QUEUE_PROCESS_COUNTER = prom.Counter(
    "hakimo_queue_process_counter",
    documentation="Count of messages processed from queue for talkdown",
    labelnames=["tenant", "queue_type", "queue_name"],
)

HAKIMO_HLS_SYNC_LATENCY_FROM_MSG_BUS = prom.Summary(
    "hakimo_hls_sync_latency_from_msg_bus",
    documentation="Latency for HLS sync from the time message is posted to appliance. ",
    labelnames=["tenant", "s3_bucket"],
)

HAKIMO_HLS_SYNC_LATENCY_E2E = prom.Summary(
    "hakimo_hls_sync_latency_e2e",
    documentation="End to end latency for HLS sync from the time sync. was requested from UI. ",
    labelnames=["tenant", "s3_bucket"],
)

HAKIMO_HLS_REQUEST_COUNTER = prom.Counter(
    "hakimo_hls_request",
    documentation="Count of HLS requests.",
    labelnames=["tenant", "s3_bucket", "is_success"],
)

HAKIMO_VIDEO_PLAYBACK_REQUEST_COUNTER = prom.Counter(
    "hakimo_video_playback_request",
    documentation="Count of video playback requests.",
    labelnames=["tenant", "is_success"],
)

HAKIMO_VIDEO_PLAYBACK_SYNC_LATENCY_E2E = prom.Summary(
    "hakimo_video_playback_sync_latency_e2e",
    documentation="End to end latency for video playback from the time segment was requested from UI. ",
    labelnames=["tenant", "sync_requested", "is_playlist_request"],
)

# Redis Queue length Custom Metrics
REDIS_QUEUE_LENGTH = prom.Gauge(
    "hakimo_redis_queue_length",
    documentation="Number of tasks from the redis key representing the celery queue",
    labelnames=["queue"],
)

MOTION_EVENT_COUNTER = prom.Counter(
    "hakimo_motion_event",
    documentation="Count of motion events",
    labelnames=["camera_id", "camera_name", "event_type"],
)

SPEAKER_CONNECTION_STATUS = prom.Counter(
    "hakimo_speaker_connection_status",
    documentation="Status of Speaker Connection with Hakimo",
    labelnames=[
        "tenant",
        "url",
        "device_type",
        "speaker_status",
        "auto_talkdown",
    ],
)
MESSAGE_ACKNOWLEDGEMENTS = prom.Counter(
    "message_acknowledgement",
    documentation="Count of acknowledgement messages in different component",
    labelnames=["component", "message_type", "ack_status"],
)

MOTION_RAW_ALARM_LOCATION_COUNTER = prom.Counter(
    "hakimo_motion_raw_alarm_location_volume",
    documentation="Count of high TAP motion raw alarms added to location alarms",
    labelnames=["tenant", "location_id", "action_type"],
)

MOTION_LOCATION_ALARM_COUNTER = prom.Counter(
    "hakimo_motion_location_alarm_volume",
    documentation="Count of new motion location alarms created",
    labelnames=["tenant", "location_id"],
)

LOCATION_ALARM_RESOLUTION_TIME = prom.Summary(
    "hakimo_location_alarm_resolution_time",
    documentation="Time taken to resolve a location alarm from pick time to resolution time",
    labelnames=["tenant", "operator_id"],
)

LOCATION_ALARM_RESOLUTION_TIME_HISTOGRAM = prom.Histogram(
    "hakimo_location_alarm_resolution_time_histogram",
    documentation="Histogram of time taken to resolve a location alarm from pick time to resolution time",
    labelnames=["tenant", "operator_id"],
    buckets=[
        30,
        60,
        120,
        300,
        600,
        1800,
        3600,
        7200,
    ],  # 30s, 1m, 2m, 5m, 10m, 30m, 1h, 2h
)

LATEST_LOCATION_ALARM_RESOLUTION_TIME = prom.Gauge(
    "hakimo_latest_location_alarm_resolution_time_seconds",
    documentation="Latest location alarm resolution time for each operator in seconds",
    labelnames=["tenant", "operator_id"],
)

RG_LLM_RAW_ALARMS_PROCESSED = prom.Counter(
    "rg_llm_raw_alarms_processed",
    documentation="Count of RG LLM raw alarms processed, by success/failure and failure reason",
    labelnames=["tenant", "success", "reason", "recommendation"],
)

RG_LLM_LOCATION_ALARMS_PROCESSED = prom.Counter(
    "rg_llm_location_alarms_processed",
    documentation="Count of RG LLM location alarms processed, by success/failure and failure reason",
    labelnames=["tenant", "success", "reason", "recommendation"],
)

RG_LLM_RAW_ALARM_PROCESSING_SECONDS = prom.Histogram(
    "rg_llm_raw_alarm_processing_seconds",
    documentation="Histogram of processing duration (s) for RG raw alarms",
    labelnames=[
        "tenant",
        "camera",
        "input_type",
    ],  # input_type = "video" or "frames"
    buckets=(
        0.1,
        0.25,
        0.5,
        1,
        2,
        3,
        4,
        5,
        7,
        10,
        15,
        20,
        30,
        45,
        60,
    ),
)

RG_LLM_LOCATION_ALARM_PROCESSING_SECONDS = prom.Histogram(
    "rg_llm_location_alarm_processing_seconds",
    documentation="Histogram of processing duration (s) for RG location alarms",
    labelnames=["tenant", "location"],
    buckets=(
        0.5,
        1,
        2,
        3,
        5,
        10,
        20,
        30,
        45,
        60,
        90,
        120,
        180,
        300,
    ),
)

RG_LLM_RAW_ALARM_TOKENS = prom.Counter(
    "rg_llm_raw_alarm_tokens",
    documentation="Count of input/output tokens for RG raw alarms",
    labelnames=["tenant", "camera", "type"],  # type = "input" or "output"
)

RG_LLM_LOCATION_ALARM_TOKENS = prom.Counter(
    "rg_llm_location_alarm_tokens",
    documentation="Count of input/output tokens for RG location alarms",
    labelnames=["tenant", "location", "type"],
)

RG_LLM_API_RESPONSES = prom.Counter(
    "rg_llm_api_responses",
    documentation="Count of Gemini API responses for RG, by phase and HTTP status",
    labelnames=[
        "tenant",
        "alarm_phase",
        "status_code",
    ],  # alarm_phase = "raw" or "location"
)

RG_LLM_API_REQUEST_DURATION_SECONDS = prom.Histogram(
    "rg_llm_api_request_duration_seconds",
    documentation="Histogram of Gemini LLM API request latency (s) for RG",
    labelnames=["tenant", "alarm_phase"],
    buckets=(
        0.05,
        0.1,
        0.2,
        0.5,
        1,
        2,
        3,
        5,
        7,
        10,
        15,
        20,
        30,
    ),
)

RG_LLM_API_RETRIES = prom.Counter(
    "rg_llm_api_retries",
    documentation="Count of retry attempts when calling Gemini API for RG",
    labelnames=["tenant", "alarm_phase", "error_type"],
)

RG_LLM_API_RETRY_COUNTS = prom.Histogram(
    "rg_llm_api_retry_counts",
    documentation="Distribution of retry counts per Gemini API request for RG",
    labelnames=["tenant", "alarm_phase"],
    buckets=(0, 1, 2, 3, 4, 5, 10),
)

RG_LLM_TASK_QUEUE_LENGTH = prom.Gauge(
    "rg_llm_task_queue_length",
    documentation="Current length of the RG LLM task queue",
)

RG_LLM_WORKER = prom.Gauge(
    "rg_llm_worker",
    documentation="Number of RG LLM worker processes",
)
