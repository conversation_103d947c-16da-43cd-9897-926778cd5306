import json
from typing import Optional

import redis
import structlog

from common_utils.message_broker.base_broker import ConsumerInterface
from common_utils.message_broker.base_redis_broker import BaseRedisBroker

log = structlog.get_logger("hakimo", module="Redis Consumer")


class RedisConsumer(BaseRedisBroker, ConsumerInterface):
    def __init__(self):
        super().__init__()

    def consume(self, queue_name: Optional[str] = None):
        task = None
        if queue_name is None:
            queue_name = self._MAIN_QUEUE_NAME
        try:
            task = self.redis_client.lpop(queue_name)
            if task:
                task = json.loads(task)
        except redis.RedisError as e:
            log.error(f"Error consuming message from stream: {e}")
            raise Exception(f"Error consuming message from stream: {e}")
        return task

    def ack(self, message: str, set_name: Optional[str] = None):
        if set_name is None:
            set_name = self._PROCESSING_QUEUE_NAME
        try:
            if isinstance(message, dict):
                message = json.dumps(message)
            self.redis_client.zrem(set_name, message)
            log.info(
                f"Message {message} acknowledged and deleted from processing queue."
            )
        except redis.RedisError as e:
            log.error(f"Error acknowledging message {message}: {e}")
