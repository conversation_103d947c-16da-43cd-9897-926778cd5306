"""Helper <PERSON>til class for mapping entities to objects and vice versa"""

import typing

import structlog

from interfaces.camera_info import CameraInfo
from interfaces.doors_camera_params import (
    DoorCameraParams as DoorCameraParams_iface,
)
from interfaces.doors_info import DoorsInfo
from models_rds.cameras import Cameras
from models_rds.door_camera_params import DoorCameraParams
from models_rds.doors import Doors

log = structlog.get_logger("hakimo", module="Mapping Utils")


def map_door_camera_info_to_interface(
    results: typing.Optional[typing.Tuple[DoorCameraParams, Doors, Cameras]],
) -> typing.Optional[DoorCameraParams_iface]:
    """Maps the door camera info tuple coming from db to DoorCameraParams interface

    Args:
        results ([tuple]): Door_camera_params tuple coming from db

    Returns:
        DoorCameraParams: DoorCameraParams interface containing mapping, doors and camera info
    """
    if results:
        doors_info = DoorsInfo(
            results[1].uuid,
            results[1].door_name,
            results[1].process_people,
            results[1].process_vehicles,
            results[1].city_and_building,
            results[1].source_system,
            results[1].raw_source_data_for_door,
            results[1].tenant_id,
            results[1].is_enabled,
        )
        camera_info = CameraInfo(
            results[2].uuid,
            results[2].name,
            results[2].client_camera_id,
            results[2].server_url,
            results[2].camera_timezone,
            results[2].camera_raw_info,
            results[2].rtsp_url,
            results[2].integration_type,
            results[2].cam_site_id,
            results[2].tenant_id,
            results[2].livestream_url,
        )
        door_camera_params = DoorCameraParams_iface(
            results[0].uuid,
            results[0].door_coordinates_in_cam_frame,
            results[0].camera_position,
            results[0].tenant_id,
            results[0].door_orientation_point,
            results[0].labelling_resolution,
            results[0].dead_zones,
            doors_info,
            camera_info,
        )
        return door_camera_params
    return None


def get_camera_info_dict(
    dcm: typing.Optional[DoorCameraParams_iface],
) -> typing.Optional[typing.Dict[str, typing.Any]]:
    """Given the door camera mapping interface, return the camera info
    dictionary that can be used to request video
    """
    if dcm is not None:
        return {
            "camera_name": dcm.camera_info.camera_name,
            "client_camera_id": dcm.camera_info.client_camera_id,
            "camera_timezone": dcm.camera_info.camera_timezone,
            "camera_raw_info": dcm.camera_info.raw_camera_info,
            "camera_integration_type": dcm.camera_info.integration_type,
            "camera_site_id": dcm.camera_info.cam_site_id,
            "server_url": dcm.camera_info.server_url,
        }
    return None
