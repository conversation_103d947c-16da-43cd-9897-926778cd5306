# ML_LAYER config
mlService:
  serviceName: ml-service
  port: 8080
  retrylimit: 5
anomalousAlarmsService:
  serviceName: anomalous-alarms-service
  port: 8081
USE_GPU: True

# Intercom Secret keys
INTERCOM_SECRET_KEY: /secrets/intercom_secret_key

PROCESSING_FPS: 5
FACE_RECOGNITION:
  pinecone_index: face_recognition_index
  rekognition_collection: face_rekognition_collection 
  embedding_dim: 128
  timeout: 5
  namespace: profile_images
  processing_queue_name: face_recognition_processing_queue
  main_queue_name: face_recognition_main_queue
  dlq_name: face_recognition_dlq
  use_aws_rekognition: True

OLD_MOTION_ALARM_SKIP: 3600
MOTION_PROCESSING_FPS: 1
AVAILABLE_DETECTORS:
  DFINE:
    type: dfine
    engine_path: /models/dfine_x_obj2coco_fp16.engine
    conf_threshold: 0.4
  YOLOV5X:
    type: yolo
    pretrain: True
    image_size: 640
    batch_size: 40
    model_gpu_path: /models/yolov5/yolov5x_gpu.torchscript
    model_cpu_path: /models/yolov5/yolov5x_cpu.torchscript
    conf_threshold: 0.65
  YOLOV5XVEHICLES:
    type: yolo
    pretrain: True
    image_size: 640
    batch_size: 40
    model_gpu_path: /models/yolov5/yolov5x_gpu.torchscript
    model_cpu_path: /models/yolov5/yolov5x_cpu.torchscript
    filter_in_classes: 
    - VEHICLE
    conf_threshold: 0.5
    fp_conf_threshold: 0.5
  YOLOV5STHERMAL:
    type: yolo
    pretrain: True
    image_size: 640
    batch_size: 5
    model_gpu_path: /models/yolov5/yolov5s_gpu.torchscript
    model_cpu_path: /models/yolov5/yolov5s_cpu.torchscript
    conf_threshold: 0.6
  YOLOV7XVEHICLES:
    type: yolo
    pretrain: True
    image_size: 640
    batch_size: 8
    model_gpu_path: /models/yolov7/yolov7x-vb-8.trt
    model_cpu_path: /models/yolov7/yolov7x_cpu.torchscript.pt
    filter_in_classes: 
    - VEHICLE
    conf_threshold: 0.5
    fp_conf_threshold: 0.3
  YOLOV7X:
    type: yolo
    pretrain: True
    image_size: 640
    batch_size: 8
    model_gpu_path: /models/yolov7/yolov7x-vb-8.trt
    model_cpu_path: /models/yolov7/yolov7x_cpu.torchscript.pt
    conf_threshold: 0.65
  YOLOV7D6VEHICLES:
    type: yolo
    pretrain: True
    image_size: 1280
    batch_size: 5
    model_gpu_path: /models/yolov7/yolov7-d6-vb-5.trt
    model_cpu_path: /models/yolov7/yolov7-d6_cpu.torchscript.pt
    filter_in_classes: 
    - VEHICLE
    conf_threshold: 0.5
    fp_conf_threshold: 0.3
  YOLOV7D6PRETRAINED:
    type: yolo
    pretrain: True
    image_size: 1280
    batch_size: 5
    model_gpu_path: /models/yolov7/yolov7-d6-vb-5.trt
    model_cpu_path: /models/yolov7/yolov7-d6_cpu.torchscript.pt
    filter_in_classes: 
    - VEHICLE
    - PERSON
    conf_threshold: 0.45
    fp_conf_threshold: 0.3
  YOLOV7D6PERSON:
    type: yolo
    pretrain: True
    image_size: 1280
    batch_size: 5
    model_gpu_path: /models/yolov7/yolov7-d6-vb-5.trt
    model_cpu_path: /models/yolov7/yolov7-d6_cpu.torchscript.pt
    filter_in_classes: 
    - PERSON
    conf_threshold: 0.45
    fp_conf_threshold: 0.3
  YOLOV5X6:
    type: yolo
    image_size: 1280
    pretrain: True
    batch_size: 20
    model_gpu_path: /models/yolov5/yolov5x6_gpu.torchscript
    model_cpu_path: /models/yolov5/yolov5x6_cpu.torchscript
    conf_threshold: 0.65
  YOLOV7D6FINETUNED:
    type: yolo
    image_size: 1280
    pretrain: False
    batch_size: 5
    # model_gpu_path: /models/yolov7/April20/yolov7_April20.torchscript.pt
    model_gpu_path: /models/yolov7/yolov7d6-july11.engine
    model_cpu_path: /models/yolov5/yolov7d6_cpu.torchscript.pt
    conf_threshold: 0.45
  YOLOV7D6TEACHERTRAINED:
    type: yolo
    image_size: 1280
    pretrain: False
    batch_size: 5
    model_gpu_path: /models/yolov7/yolov7d6-03-07-24-teacher-train-vb-5.trt
    model_cpu_path: /models/yolov5/yolov7d6_cpu.torchscript.pt
    conf_threshold: 0.2
  YOLOV7D6TEACHERTRAINEDCOMBINED:
    type: yolo
    pretrain: True
    image_size: 1280
    batch_size: 5
    model_gpu_path: /models/yolov7/yolov7d6-combined-teacher-trained-17-03-25-vb-5.engine
    model_cpu_path: /models/yolov7/yolov7d6-combined-teacher-trained-17-03-25-vb-5.engine
    filter_in_classes: 
    - VEHICLE
    - PERSON
    conf_threshold: 0.3
    veh_conf_threshold: 0.1
    fp_conf_threshold: 0.3
  YOLOV7D6THERMAL:
    type: yolo
    image_size: 1280
    pretrain: False
    batch_size: 5
    model_gpu_path: /models/yolov7/yolov7d6-thermal-roboflow-vb-5.trt
    model_cpu_path: /models/yolov5/yolov7d6_cpu.torchscript.pt
    conf_threshold: 0.37
  MMDETECTION:
    type: codetr
    batch_size: 1
    pretrain: True
    image_size: 1280
    model_gpu_path: /models/co-detr/co_dino_swinl.pth
    model_cpu_path: /models/co-detr/co_dino_swinl.pth
    conf_threshold: 0.4

DETECTOR:
  model_name: YOLOV7D6PERSON
  use_gpu_decode: True
  reid_models:
    PERSON: STRONG_BASELINE
PEOPLE_MOTION_DETECTOR:
  model_name: YOLOV7D6TEACHERTRAINED
  use_clahe: False
  use_gpu_decode: True
  reid_models: 
    PERSON: STRONG_BASELINE
BIG_DETECTOR:
  model_name: MMDETECTION
  use_clahe: False
  use_gpu_decode: True
  reid_models: 
    PERSON: STRONG_BASELINE
VEHICLE_MOTION_DETECTOR:
  model_name: YOLOV7D6VEHICLES
  use_clahe: False
  use_gpu_decode: True
  reid_models:
    VEHICLE: AI_CITY_VEHICLE
PEOPLE_THERMAL_MOTION_DETECTOR:
  model_name: YOLOV7D6THERMAL
  use_clahe: False
  use_gpu_decode: True
ENSEMBLE_MOTION_DETECTOR:
  model_name: 
  - YOLOV7D6VEHICLES
  - YOLOV7D6TEACHERTRAINED
  use_clahe: False
  use_gpu_decode: True
  reid_models:
    PERSON: STRONG_BASELINE
    VEHICLE: AI_CITY_VEHICLE
PEOPLE_VEHICLE_MOTION_DETECTOR:
  model_name: YOLOV7D6TEACHERTRAINEDCOMBINED 
  use_clahe: False
  use_gpu_decode: True
  reid_models:
    PERSON: STRONG_BASELINE
    VEHICLE: AI_CITY_VEHICLE

AVAILABLE_SEGMENTORS:
  RAFT:
    type: optical_flow_raft
    batch_size: 1
    model_gpu_path: /models/optical_flow/raft/large.torchscript.pt
    model_cpu_path: /models/optical_flow/raft/large.torchscript.pt
    img_size: 1280
    threshold: 172
  GPU:
    type: optical_flow_gpu
    vehicle_fp_threshold: 0.02
    vehicle_motion_threshold: 0.05
    person_threshold: 0.001
  CPU:
    type: optical_flow
    vehicle_fp_threshold: 0.01
    vehicle_motion_threshold: 0.5
    person_threshold: 0.001

SEGMENTOR:
  TYPE: GPU
  ENABLED: True

HIGHER_LEVEL_DETECTOR:
  model_name: MMDETECTION
  use_clahe: False
  use_gpu_decode: False
  reid_models: 
    PERSON: STRONG_BASELINE

HIGHER_LEVEL_CLASSIFIER:
  PATH: /models/higher_level/rf.pkl

EFFICIENTDET_MODEL_NAME: tf_efficientdet_d1
# EFFICIENTDET_MODEL_PATH: "/models/efficientdet/trained_effdet_models/20210513_175826/20210513_175826-20210513_175826latest-epoch=00000013-train_loss=0.10817-val_precision=0.85401.ckpt"
EFFICIENTDET_MODEL_PATH: "/models/efficientdet/trained_effdet_models/pretrained_tf_efficientdet_d1.ckpt"
HYPERIQA_MODEL_PATH: "/models/hyperiqa/koniq_pretrained.pkl"
LINEARITYIQA_MODEL_PATH: "/models/linearity_iqa/p1q2plus0.1variant.pth"
MOTION_DETECTION_THRESHOLD: 0.0005
VEHICLE_NMS_IOU_THRESHOLD: 0.8
DEAD_ZONE_INTERSECTION_THRESHOLD: 0.95
VEHICLE_DEAD_ZONE_INTERSECTION_THRESHOLD: 0.8
PARKING_ZONE_INTERSECTION_THRESHOLD: 0.8
ACTIVE_ZONE_INTERSECTION_THRESHOLD: 0.7
MOTION_ZONE_INTERSECTION_THRESHOLD: 0.25
videoProps:
  maxGapMultiplier: 2 # 2 * PROCESSING_FPS is the max number of continuous frames
  # that can be corrupt before abandoning processing
  corruptRetries: 2
reidModel:
  STRONG_BASELINE:
    modelPath: "/models/strong_baseline/trained_reid_models/lmbn.ckpt"
    threshold: 0.44
    batchSize: 32
  AI_CITY_VEHICLE:
    modelPath: "/models/ai_city_vehicle_reid/net_last.pth"
    batchSize: 32
TRACKER:
  TYPE: deepsort
  MATCHING_TYPE: enterprise_people
  KEEP_MAX_STALENESS: 0
  N_BEST_BOXES: 5
MOTION_VEHICLE_TRACKER:
  TYPE: deepsort
  MATCHING_TYPE: custom_vehicles
MOTION_PEOPLE_TRACKER:
  TYPE: deepsort
  KEEP_MAX_STALENESS: 3
  N_BEST_BOXES: 5
TRACKLET_MERGER:
  people:
    type: reid
    modelType: STRONG_BASELINE
    # mpnPath: /models/mpn/model_june_2022.ckpt
    # modelPath: /models/strong_baseline/lmbn_n_cuhk03_d.pth
    threshold: 0.44
    enable: true
    DUMP_IMG_PATCHES: False
    DUMP_PLOTS: False
    DUMP_PLOTS_IMGS_PATH: /tmp/
    DRAW_PATCHES: False
    MAX_TIME_DIFFERENCE: 10
    overlapIoUMin: 0.9
  vehicles:
    type: reid
    modelType: AI_CITY_VEHICLE
    DUMP_IMG_PATCHES: False
    DUMP_PLOTS: False
    DUMP_PLOTS_IMGS_PATH: /tmp/
    DRAW_PATCHES: False
    MAX_TIME_DIFFERENCE: 20

debugDataDumper:
  detectionsOutputFolder: /data/videos/detections
  # tracks and merges saved in same folder
  tracksOutputFolder: /data/videos/tracks
  trackletsOutputFolder: /data/videos/tracklets
  dumpTrackImagePatches: False
  dumpVideo: false
  videoOutputFolder: "/tmp"
  dumpTrackletMergeDebug: False
  trackletMergeDebugFolder: "/tmp"
CANCELLED_ALARM_TRANSITION_BUFFER: 5

# RDS Config
RDS_HOST: /secrets/rds_host
RDS_PORT: 3306
RDS_DATABASE: /secrets/rds_database
RDS_USERNAME: /secrets/rds_username
RDS_PASSWORD: /secrets/rds_password
RDS_MYSQL_DRIVERNAME: mysql+pymysql

# READ Replicas RDS Config
RDS_READ_HOST: /secrets/rds_read_host
RDS_READ_PORT: 3306
RDS_READ_DATABASE: /secrets/rds_read_database
RDS_READ_USERNAME: /secrets/rds_read_username
RDS_READ_PASSWORD: /secrets/rds_read_password

# Data Warehouse Config (currently Redshift)
DW_HOST: /secrets/dw_host
DW_PORT: 5439
DW_DATABASE: /secrets/dw_database
DW_USERNAME: /secrets/dw_username
DW_PASSWORD: /secrets/dw_password
DW_PSGS_DRIVERNAME: postgresql

# AUTH0 client secret to get token that will be used
# to post new notificaiton to UI.
AUTH0_CLIENT_SECRET: /secrets/auth0_client_secret

# AWS Access and Secret keys (should be coming from environment)
AWS_ACCESS_KEY_ID: /secrets/aws_access_key_id
AWS_SECRET_KEY: /secrets/aws_secret_key
AWS_S3_REGION: us-west-2
AWS_SES_REGION: us-west-2

SQS_AWS_ACCESS_KEY_ID: /secrets/sqs_aws_access_key_id
SQS_AWS_SECRET_KEY: /secrets/sqs_aws_secret_key
SQS_S3_BUCKET_NAME: /secrets/sqs_s3_bucket_name

SQS_VISIBILITY_TIMEOUT: 1800 # Visibility timeout set to 1800 seconds or 30 minutes when createing a new SQS queue
SQS_MESSAGE_RETENTION_PERIOD: 1800 # Message Retention Period set to 1800 seconds or 30 minutes when createing a new SQS queue

# Pinecone Config
PINECONE_ENABLED: false
PINECONE_API_KEY: /secrets/pinecone_api_key
PINECONE_INDEX: null

PINECONE_FACE_RECOG_ENABLED: true
PINECONE_FACE_RECOG_API_KEY: /secrets/pinecone_face_recog_api_key
PINECONE_FACE_RECOG_INDEX: face-embeddings

# Add all upcoming raw alarms to Location Alarm under escalation 
ESCALATION_GROUPING: true
ESCALATION_GROUPING_TIME: 7200

# WandB Config
WANDB_API_KEY: /secrets/wandb_api_key

# LabelStudio Config
LABEL_STUDIO_HOST: /secrets/label_studio_host
LABEL_STUDIO_TOKEN: /secrets/label_studio_token
LABEL_STUDIO_PORT: 8080

TABLEAU_URL: https://reports-beta-1.hakimo.ai/
# Tableau Config
TABLEAU_ADMIN: admin
TABLEAU_PASSWORD: /secrets/tableau_password
TABLEAU_HOST: /secrets/tableau_host
TOKEN_NAME: /secrets/token_name
TOKEN_VALUE: /secrets/token_value
PROJECT_NAME: Default
INSIGHT_HYPER_FILE: insights.hyper
HEALTH_HYPER_FILE: health.hyper

# disk paths for processing and processed stitched videos
PROCESSING_VIDEO_OUTPUT_PATH: /data/videos/processing
PROCESSED_VIDEO_OUTPUT_PATH: /data/videos/processed

# Object Storage for media files
# Ex: local, s3
OBJECT_STORAGE:
  type: s3
  # uncomment path when use local for object storage
  #path: /data/store

# Source Email ID for sending emails
HAKIMO_NO_REPLY_MAIL_ID: <EMAIL>
# email id to which alarm feedback from UI needs to be sent
HAKIMO_ALARM_FEEDBACK_MAIL_ID: <EMAIL>

TWILIO:
  ACCOUNT_SID_PATH: /secrets/twilio_account_sid
  AUTH_TOKEN_PATH: /secrets/twilio_auth_token
  TWIML_APP_SID_PATH: /secrets/twiml_app_sid
  API_KEY_PATH: /secrets/twilio_api_key
  API_SECRET_PATH: /secrets/twilio_api_secret
  NUMBER: "+***********"
  # Lambda function which sends notifications to #twilio-alerts slack channel
  SLACK_WEBHOOK_MIDDLEWARE_URL: https://fj4nk336k6yu7kcdd5cjt2nlwm0gqprb.lambda-url.us-west-2.on.aws

INCIDENT_URL_FORMAT: https://portal.hakimo.ai/alarms/{}

heuristics:
  minTrackDuration: 0
  restoredEventTimeout: 300
  tailgatingDeduplicatingThreshold: 5
  doorCloseEventThreshold: 2
  # this is in seconds, code is in milliseconds
  unauthorizedEntryDeduplicationThreshold: 5
  reducedDHOThreshold: 30
  entityHeuristics:
    people:
      accessEntryWindowBefore: 3
      accessEntryWindowAfter: 20
      escortEntryWindow: 30
      velocitySmoothingWindow: 2
      velocityCheckMethod: null
    vehicles:
      accessEntryWindowBefore: 50
      accessEntryWindowAfter: 50
      escortEntryWindow: 150
  videoAdjustmentTime: 5
HAKIMO_USER_ID: a12aa2ef-337a-11eb-bd45-06bdff6696eb
TIME_BEFORE_ALARM_TIMESTAMP: 10
TIME_AFTER_ALARM_TIMESTAMP: 10
LOITERING_TIME: 15
MOTION_TIME: 2
VEHICLE_HIGH_MOVEMENT_THRESHOLD: 0.5
VEHICLE_LOW_MOVEMENT_THRESHOLD: 0.45

videoAnalyzer:
  # Excess time beyond cancelled event to ask for
  endEventBuffer: 5

# HTTP API constants and configs - must match with ingestion processor
HTTP_API_PORT: 8080

# For checking video image quality
IMAGE_REFERENCE_HOUR: 20

# For checking Camera Health and Changes
camHealthMonitoring:
  IQACheck: False
  LINEARITY_IQA_THRESHOLD: 25
ormAuth:
  jwtKey: /secrets/jwt_secret_ui

anomalousAlarms:
  lookuptimesModelKey: anomalous_alarms_model/times/
  lookupdoorsModelKey: anomalous_alarms_model/doors/
  maxOptimalRange: 12
  afterHoursStart: 21
  afterHoursEnd: 5

etl:
  etlMaxLockDurationHours: 4
  fullETLMaxLockAttempts: 30
  fullETLLockAttemptDelaySeconds: 120
rabbitmq:
  enabled: false
  serviceName: rabbitmq
  port: 5672
  usernameFile: /secrets/rabbitmq_username
  passwordFile: /secrets/rabbitmq_password
redis:
  enabled: false
  # serviceName: host.docker.internal
  # serviceName: localhost
  serviceName: redis
  visionServiceName: redis
  port: 6379
# number of seconds since last livestream request was made to trigger
# stopping of a livestream.
LIVESTREAM_STOP_THRESH_SEC: 120
ALARM_TALKDOWN_LATENCY_SEC: 1800 # This is the max latency allowed between the alarm creation time and the time of automatic talkdwon. Currently 1800 seconds
MOTION_ALARM_GROUPING_TIME_SECS: 300 # A raw alarm is added to a location alarm within this time window
MOTION_VECTOR_GROUPING_TIME_SECS: 420 # Location alarms in this window are used for DEDUP

# EagleEye Integration: Client_id and Client_secret for oauth
EAGLEEYE:
  client_id: /secrets/eagleeye_client_id
  client_secret: /secrets/eagleeye_client_secret


AUDIO_TALKDOWN_FETCH_ENABLED: true
AXIS_AUDIO_TALKDOWN_FETCH_ENABLED: true
AUTO_TALKDOWN_MIN_GAP_SEC: 20
# External API
EXAPI:
  integrations:
    - integration_type: everbridge
      url: https://api.everbridge.net/rest
      secrets:
        user: /secrets/everbridge_user
        password: /secrets/everbridge_password
    - integration_type: zoom
      url: https://integrations.zoom.us/chat/webhooks/incomingwebhook

GEMINI_KEY: /secrets/api_token
GEMINI_ENTITY_CLASSIFICATION_KEY: /secrets/gemini_entity_api_token
GEMINI_ALARM_ANALYZER_KEY: /secrets/gemini_alarm_analyzer_api_token
GEMINI_EVENT_ANALYZER_KEY: /secrets/gemini_event_analyzer_api_token
GEMINI_ALARM_ANALYZER_QUEUE_SIZE: 200
GEMINI_ALARM_ANALYZER_WORKERS: 5
EVENT_QUEUE_URL: https://sqs.us-west-2.amazonaws.com/695273141991/vision_event_staging.fifo
LLM_EVENT_QUEUE_URL: https://sqs.us-west-2.amazonaws.com/695273141991/vision_llm_event_staging.fifo
LLM_ALARM_GROUP_QUEUE_URL: https://sqs.us-west-2.amazonaws.com/695273141991/vision_llm_alarm_group_staging.fifo
VISION_GLOBAL_TTL: 86400
VISION_SAFETY_THRESHOLD: 3
VISION_SAFETY_COUNT_TTL: 600
SCAN_ALARM_TRANSACTION_QUEUE_URL: https://sqs.us-west-2.amazonaws.com/695273141991/scan_get_alarm_transactions_staging.fifo
