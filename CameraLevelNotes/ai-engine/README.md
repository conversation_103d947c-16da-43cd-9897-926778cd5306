
[![Coverage Status](https://coveralls.io/repos/github/hakimo-ai/ai-engine/badge.svg?t=rWsMq0)](https://coveralls.io/github/hakimo-ai/ai-engine)
## About 

This repo contains the implementation of Hakimo Ingestion Processor (HIP)
and Hakimo AI Engine (HAIE).

### Run CI-scripts for unit tests, black format and pylint 
Build the docker with the changes on your branch
```
./ci_scripts/build-docker.sh
```
Run tests with the latest docker image built in the previous step
```
./ci_scripts/unit-tests.sh 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-ai-engine:<docker-img tag>
```
Replace **unit-tests.sh** in the previous step with *format-black.sh* or *pylint.sh* to check black format and linting errors

### Deploying HAIE in AWS EKS Cluster
- The AI Engine has a helm chart that packages all the required dependencies and runs the required containers in the specified namespace

- Run the following script with the Hakimo-ai-engine Image tag to deploy that version of AI Engine to EKS cluster
```
bash ci_scripts/deploy-ai-engine.sh <staging|preprod|prod> <Mandatory: Docker Image Tag>
```

### System Reinstall
```bash
sudo dpkg --configure -a
sudo apt-get update
sudo apt-get -f install
sudo apt-get full-upgrade
sudo apt-get install --reinstall ubuntu-desktop
sudo apt-get autoremove
sudo apt-get clean
sudo reboot
```
### nVIDIA driver upgrade
```bash
# find the cuda download local file from nvidia site
# probably https://developer.nvidia.com/cuda-toolkit-archive
wget https://developer.download.nvidia.com/compute/cuda/12.8.0/local_installers/cuda_12.8.0_570.86.10_linux.run
sudo sh cuda_12.8.0_570.86.10_linux.run
# this may fail due to existing driver
sudo apt-get remove --auto-remove nvidia-cuda-toolkit
# if nvidia drm issue is still there do a 
sudo reboot
# also make sure you dont select kernel objects nvidia-fs options
sudo sh cuda_12.8.0_570.86.10_linux.run
# to find which docker images work with the cuda version on the nvcr page
# look for a framework support matrix link and look at cuda ver vs driver ver
```

### Update FRP and Pushprox information
Create a Pull Request for `ai-engine` repository to add the FRP Port and Pushprox values in [helm/frp/values.yaml](../helm/frp/values.yaml) and [helm/pushprox/values.yaml](../helm/pushprox/values.yaml) respectively. You may refer the sample [PR 3933](https://github.com/hakimo-ai/ai-engine/pull/3933/files)

After the Pull Request is merged to `master` branch, checkout `master` branch of `ai-engine` and execute the command below:
```
cd ai-engine
git checkout master
git pull
helm upgrade -i support helm/frp --namespace support-server
bash infra/prometheus-configuration/update-prometheus-config.sh
```
To use the [infra/prometheus-configuration/update-prometheus-config.sh](../infra/prometheus-configuration/update-prometheus-config.sh) script, you would require a Python Virtual Environment.

Command to update the prometheus infra in EKS
```commandline
cd infra/prometheus
kubectl apply -f prometheus.yaml
```
or a helm upgrade command like below can be run
```commandline
cd infra/prometheus
helm upgrade prom-op prometheus-community/kube-prometheus-stack -n monitoring -f prometheus.yaml --version 56.0.0
```

Command to deploy ai-engine on staging
```commandline
bash ci_scripts/deploy-ai-engine.sh staging v1.138.31
or
helm upgrade -i  ai-engine-staging helm/ai-engine --timeout 15m --namespace staging --values helm/ai-engine/values-staging.yaml --set image.tag="v1.138.31"
```
