import datetime
import os
import os.path as osp
import time
from dataclasses import asdict
from pathlib import Path
from typing import Any, Dict, Optional

import pandas as pd
import structlog

from insights.common_interfaces.etl_interval import ETLInterval
from insights.etl.exceptions import PaginatedQueryFailedError
from insights.etl.history_data_loader import HistoricalDataReader
from insights.etl.interfaces import ETLExtraction
from insights.etl.utils import data_checks, misc_utils
from models_rds.raw_alarms import get_default_partition_key

log = structlog.get_logger("hakimo.etl", module="InsightsETL Extractor")


class ETLExtractor:
    def __init__(
        self,
        tenant_id,
        query_folder,
        source_engine,
        tmp_dir_name,
        history_cache,
    ):
        self._query_folder = query_folder
        self._source_engine = source_engine
        self._df_path = osp.join(tmp_dir_name, "hakimo_rds_query.csv")
        self._emp_path = osp.join(tmp_dir_name, "emps.csv")
        self._ml_outputs_path = osp.join(tmp_dir_name, "ml_outputs.csv")
        self._alarm_updates_path = osp.join(tmp_dir_name, "alarm_updates.csv")
        self._video_tags_path = osp.join(tmp_dir_name, "video_tags.csv")
        self._incidents_path = osp.join(tmp_dir_name, "incidents.csv")
        self._tenant_id = tenant_id
        self._history_reader = HistoricalDataReader(tenant_id)
        self._history_cache_path = history_cache
        self._tmp_dir_name = tmp_dir_name

    def get_query_args(
        self,
        interval: Optional[ETLInterval] = None,
        raw_alarm_ids: Optional[list] = None,
    ) -> Dict[str, Any]:
        kwargs = {
            "tenant_id": self._tenant_id,
        }
        extract_interval = ETLInterval()
        if interval:
            extract_interval = extract_interval.intersect(interval)
        if raw_alarm_ids:
            kwargs.update({"ids": raw_alarm_ids})
        kwargs.update({"partition_key": get_default_partition_key()})
        kwargs.update(asdict(extract_interval))
        return kwargs

    def handle_unfound_alarms(self, ret: ETLExtraction):
        if not ret.raw_alarms_path:
            raise ValueError("ret.raw_alarms_path is None")

        if ret.alarm_updates is None:
            raise ValueError("ret.alarm_updates is None")

        raw_alarms_df = pd.read_csv(ret.raw_alarms_path, sep="|")

        unfound_alarms_df = ret.alarm_updates[
            ~ret.alarm_updates["alarm_id"].isin(raw_alarms_df["id"])
        ]

        if unfound_alarms_df.empty:
            return

        unfound_alarms_ids = list(set(unfound_alarms_df["alarm_id"].tolist()))
        ret.unfound_alarm_ids = unfound_alarms_ids

        tables_to_update = [
            (
                "rds_raw_alarm_unfound.sql",
                self._df_path,
                True,
                unfound_alarms_ids,
                True,
            ),
            (
                "rds_incidents_unfound.sql",
                self._incidents_path,
                False,
                unfound_alarms_ids,
                False,
            ),
            (
                "rds_ml_outputs_unfound.sql",
                self._ml_outputs_path,
                False,
                unfound_alarms_ids,
                False,
            ),
        ]

        for (
            query_file,
            output_path,
            postprocess,
            unfound_alarms_ids,
            duplicate_ids_check,
        ) in tables_to_update[:-1]:
            self._get_paginated_data_and_save(
                query_file,
                output_path,
                header=False,
                interval=None,
                postprocess=postprocess,
                unlink_path=False,
                ids=unfound_alarms_ids,
                duplicate_ids_check=duplicate_ids_check,
            )

        ml_outputs = pd.read_csv(self._ml_outputs_path, sep="|")
        new_ml_output_ids = ml_outputs[
            ml_outputs.alarm_id.isin(unfound_alarms_ids)
        ].ml_output_id.tolist()

        query_file = "rds_video_tags_unfound.sql"
        output_path = self._video_tags_path
        postprocess = False
        unfound_alarms_ids = new_ml_output_ids
        if len(unfound_alarms_ids) == 0:
            return
        self._get_paginated_data_and_save(
            query_file,
            output_path,
            header=False,
            interval=None,
            postprocess=postprocess,
            unlink_path=False,
            ids=unfound_alarms_ids,
        )

    def _process_df(self, df: pd.DataFrame, duplicate_ids_check: bool = False):
        if duplicate_ids_check:
            data_checks.check_for_duplicate_ids(df)
        if "alarm_type" in df.columns:
            misc_utils.clear_newlines(df, "alarm_type")
        if "source_id" in df.columns:
            misc_utils.clear_newlines(df, "source_id")
        if "plain_text_comment" in df.columns:
            misc_utils.clear_newlines(df, "plain_text_comment")
        return df

    def _split_alarm_ids_into_chunks(self, alarm_ids):
        """Split alarm IDs into smaller chunks of 1000 each"""
        chunks = []
        for i in range(0, len(alarm_ids), 1000):
            chunks.append(alarm_ids[i : i + 1000])
        return chunks

    def _split_interval_into_chunks(
        self, interval: ETLInterval, days_per_chunk: int = 5
    ) -> list:
        """Split interval into smaller chunks of N days each"""
        chunks = []
        current_start = interval.begin

        while current_start < interval.end:
            chunk_end = min(
                current_start + datetime.timedelta(days=days_per_chunk),
                interval.end,
            )
            chunks.append(ETLInterval(begin=current_start, end=chunk_end))
            current_start = chunk_end

        return chunks

    def _get_paginated_data_and_save(
        self,
        query_file: str,
        output_path: str,
        header: bool,
        interval: Optional[ETLInterval] = None,
        postprocess: bool = False,
        unlink_path: bool = True,
        ids: list = None,
        duplicate_ids_check: bool = False,
    ):
        log.info("Extracting data...", query_file=query_file)
        query_file = osp.join(self._query_folder, query_file)
        if unlink_path:
            Path(output_path).unlink(missing_ok=True)
        if interval:
            chunks = self._split_interval_into_chunks(
                interval, days_per_chunk=5
            )
        elif ids:
            chunks = self._split_alarm_ids_into_chunks(ids)
        else:
            chunks = [None]
        for chunk in chunks:
            df_iter = misc_utils.execute_paginated_query_from_file(
                query_file,
                self._source_engine,
                self.get_query_args(
                    interval=chunk if interval else None,
                    raw_alarm_ids=chunk if ids else None,
                ),
            )
            log.info("Iterating through data...", query_file=query_file)
            for idx, df in enumerate(df_iter):
                if postprocess:
                    df = self._process_df(
                        df, duplicate_ids_check=duplicate_ids_check
                    )
                df.to_csv(
                    output_path, mode="a", header=header, sep="|", index=False
                )
                header = False
                log.info(
                    "Completed data read iteration",
                    query_file=query_file,
                    iteration=idx,
                )
        log.info(
            "Query success. Data written to disk.",
            query_file=query_file,
            df_path=output_path,
        )

    def _get_data_and_save(
        self,
        ret: ETLExtraction,
        query_file: str,
        query_args: Dict,
        df_name: str,
    ):
        log.info("Extracting data...", query_file=query_file)
        query_file = osp.join(self._query_folder, query_file)
        df = misc_utils.execute_query_from_file(
            query_file, self._source_engine, query_args
        )
        if df_name == "doors":
            ret.doors = df
        if df_name == "cameras":
            ret.cameras = df
        log.info(
            "Completed data extraction",
            query_file=query_file,
            df_shape=df.shape,
        )

    def extract_data_new(
        self, interval: Optional[ETLInterval] = None, incremental: bool = False
    ) -> ETLExtraction:
        log.info(f"Running ETL Extract over time range: {interval}")
        ret = ETLExtraction()

        non_paginated_queries = [
            ("rds_doors.sql", self.get_query_args(), "doors"),
            ("rds_cameras.sql", self.get_query_args(), "cameras"),
        ]

        for query_file, query_args, df_name in non_paginated_queries:
            log.info("Extracting non-paginated data...", query_file=query_file)
            self._get_data_and_save(
                ret=ret,
                query_file=query_file,
                query_args=query_args,
                df_name=df_name,
            )

        paginated_queries = [
            ("hakimo_rds_query.sql", self._df_path, interval, True, True),
            (
                "rds_incidents.sql",
                self._incidents_path,
                interval,
                False,
                False,
            ),
            (
                "rds_ml_outputs.sql",
                self._ml_outputs_path,
                interval,
                False,
                False,
            ),
            (
                "rds_alarm_updates.sql",
                self._alarm_updates_path,
                interval,
                True,
                False,
            ),
            (
                "rds_video_tags.sql",
                self._video_tags_path,
                interval,
                False,
                False,
            ),
            ("rds_employees.sql", self._emp_path, None, False, False),
        ]

        for (
            query_file,
            output_path,
            interval,
            postprocess,
            duplicate_ids_check,
        ) in paginated_queries:
            if not os.path.exists(output_path):
                try:
                    log.info(
                        "Extracting paginated data...", query_file=query_file
                    )
                    self._get_paginated_data_and_save(
                        query_file=query_file,
                        output_path=output_path,
                        header=True,
                        interval=interval,
                        postprocess=postprocess,
                        duplicate_ids_check=duplicate_ids_check,
                    )
                except:
                    raise PaginatedQueryFailedError(file_path=output_path)

        # Assign the path of the raw alarms data
        ret.raw_alarms_path = self._df_path

        # Load alarm updates data from CSV file and convert timestamp column to datetime
        ret.alarm_updates = pd.read_csv(self._alarm_updates_path, sep="|")
        ret.alarm_updates["update_timestamp_utc"] = pd.to_datetime(
            ret.alarm_updates["update_timestamp_utc"], errors="coerce"
        ).dt.tz_localize(None)

        if incremental:
            # Handle any unfound alarms that are updated later
            self.handle_unfound_alarms(ret=ret)

        # Load incidents data from CSV file
        ret.incidents = pd.read_csv(self._incidents_path, sep="|")
        ret.incidents.raw_alarms_id = ret.incidents.raw_alarms_id.astype(str)

        # Load ML outputs data from CSV file and convert timestamp column to datetime
        ret.ml_outputs = pd.read_csv(self._ml_outputs_path, sep="|")
        ret.ml_outputs["ml_output_timestamp_utc"] = pd.to_datetime(
            ret.ml_outputs["ml_output_timestamp_utc"], errors="coerce"
        ).dt.tz_localize(None)

        # Assign the path of the employees data
        ret.employees_path = self._emp_path

        # Load video tags data from CSV file
        ret.video_tags = pd.read_csv(self._video_tags_path, sep="|")

        # Disable historical data processing
        ret.hist_alarms_path = None
        return ret

    def extract_data(
        self, interval: Optional[ETLInterval] = None
    ) -> ETLExtraction:
        """Connects to Hakimo RDS and loads
        data using the queries with 'rds' string in the name
        in the query folder.

        Once all of these are loaded/saved, the
        min() 'alarm_timestamp_utc' from
        RDS is set to variable
        'min_time_rds', which is then passed as
        the upper timebound to read_history_data()
        so that there is no overlap between historical data and
        RDS data.
        Following tables can be loaded for incremental ETL:
            raw_alarms, alarm_updates, incidents, ml_outputs, video_tags
        interval: If specified keeps only entries in the
            specified time range for above tables.
        Doors and Employees tables are always fetched in full.
        """
        log.info(f"Running ETL Extract over time range: {interval}")
        ret = ETLExtraction()
        # get doors data
        doors_query_file = osp.join(self._query_folder, "rds_doors.sql")
        log.info("Extracting data...", query_file=doors_query_file)
        ret.doors = misc_utils.execute_query_from_file(
            doors_query_file,
            self._source_engine,
            self.get_query_args(),
        )
        log.info(
            "Query success. Door Data loaded into DF.",
            df_shape=ret.doors.shape,
            df_cols=ret.doors.columns,
        )
        # get cameras data
        cameras_query_file = osp.join(self._query_folder, "rds_cameras.sql")
        log.info("Extracting data...", query_file=cameras_query_file)
        ret.cameras = misc_utils.execute_query_from_file(
            cameras_query_file,
            self._source_engine,
            self.get_query_args(),
        )
        log.info(
            "Query success. Camera Data loaded into DF.",
            df_shape=ret.doors.shape,
            df_cols=ret.doors.columns,
        )
        # get event data
        event_query_file = osp.join(self._query_folder, "hakimo_rds_query.sql")
        log.info("Extracting data...", query_file=event_query_file)

        df_iter = misc_utils.execute_paginated_query_from_file(
            event_query_file,
            self._source_engine,
            self.get_query_args(interval=interval),
        )
        Path(self._df_path).unlink(missing_ok=True)
        header = True
        min_time_rds = datetime.datetime.max
        num_rows = 0
        log.info("Iterating through data...", query_file=event_query_file)
        for idx, df in enumerate(df_iter):
            start_time = time.time()  # Record start time
            if not df.empty:
                min_time_rds = min(
                    df["alarm_timestamp_utc"].min(), min_time_rds
                )
            data_checks.check_for_duplicate_ids(df)
            misc_utils.clear_newlines(df, "alarm_type")
            misc_utils.clear_newlines(df, "source_id")
            df.to_csv(
                self._df_path, mode="a", header=header, sep="|", index=False
            )
            header = False
            cols = df.columns
            end_time = time.time()  # Record end time
            duration = end_time - start_time  # Calculate duration
            log.info(
                "Completed RDS data read iteration",
                iteration=idx,
                duration=duration,  # Log duration
            )
        ret.raw_alarms_path = self._df_path
        log.info(
            "Query success. Data written to disk.",
            df_shape=(num_rows, len(cols)),
            df_cols=cols,
        )
        # get incidents data
        incidents_query_file = osp.join(
            self._query_folder, "rds_incidents.sql"
        )
        ret.incidents = misc_utils.execute_query_from_file(
            incidents_query_file,
            self._source_engine,
            self.get_query_args(interval=interval),
            iterate=False,
        )
        # get ml output data
        ml_output_query_file = osp.join(
            self._query_folder, "rds_ml_outputs.sql"
        )
        log.info("Extracting data...", query_file=ml_output_query_file)
        ml_output_df_iter = misc_utils.execute_paginated_query_from_file(
            ml_output_query_file,
            self._source_engine,
            self.get_query_args(interval=interval),
        )
        header = True
        ml_outputs_path = self._ml_outputs_path
        Path(ml_outputs_path).unlink(missing_ok=True)
        for idx, df in enumerate(ml_output_df_iter):
            df.to_csv(
                ml_outputs_path, mode="a", header=header, sep="|", index=False
            )
            header = False
            log.info("Completed ML Outputs data read iteration", iteration=idx)
        ret.ml_outputs = pd.read_csv(ml_outputs_path, sep="|")
        # Convert the 'timestamp_utc' column to datetime
        ret.ml_outputs["ml_output_timestamp_utc"] = pd.to_datetime(
            ret.ml_outputs["ml_output_timestamp_utc"], errors="coerce"
        ).dt.tz_localize(None)
        # get alarm update data
        alarm_update_query_file = osp.join(
            self._query_folder, "rds_alarm_updates.sql"
        )
        log.info("Extracting data...", query_file=alarm_update_query_file)
        alarm_update_df_iter = misc_utils.execute_paginated_query_from_file(
            alarm_update_query_file,
            self._source_engine,
            self.get_query_args(interval=interval),
        )
        header = True
        alarm_updates_path = self._alarm_updates_path
        Path(alarm_updates_path).unlink(missing_ok=True)
        for idx, df in enumerate(alarm_update_df_iter):
            df.to_csv(
                alarm_updates_path,
                mode="a",
                header=header,
                sep="|",
                index=False,
            )
            header = False
            log.info(
                "Completed Alarm Updates data read iteration", iteration=idx
            )
        ret.alarm_updates = pd.read_csv(alarm_updates_path, sep="|")
        ret.alarm_updates["update_timestamp_utc"] = pd.to_datetime(
            ret.alarm_updates["update_timestamp_utc"], errors="coerce"
        ).dt.tz_localize(None)
        log.info(
            "Query success. Tag loaded into DF.",
            df_shape=ret.ml_outputs.shape,
            df_cols=ret.ml_outputs.columns,
        )
        # get employee data
        emp_query_file = osp.join(self._query_folder, "rds_employees.sql")
        log.info("Extracting data...", query_file=emp_query_file)
        df_iter = misc_utils.execute_paginated_query_from_file(
            emp_query_file,
            self._source_engine,
            self.get_query_args(),
        )
        header = True
        Path(self._emp_path).unlink(missing_ok=True)
        for idx, df in enumerate(df_iter):
            df.to_csv(
                self._emp_path, mode="a", header=header, sep="|", index=False
            )
            header = False
            log.info("Completed Employees data read iteration", iteration=idx)
        log.info(
            "Query success. Emp Data loaded into file.", df_path=self._emp_path
        )
        ret.employees_path = self._emp_path
        # get tags data
        tags_query_file = osp.join(self._query_folder, "rds_video_tags.sql")
        log.info("Extracting data...", query_file=tags_query_file)
        video_tags_df_iter = misc_utils.execute_paginated_query_from_file(
            tags_query_file,
            self._source_engine,
            self.get_query_args(interval=interval),
        )
        header = True
        video_tags_path = self._video_tags_path
        Path(video_tags_path).unlink(missing_ok=True)
        for idx, df in enumerate(video_tags_df_iter):
            df.to_csv(
                video_tags_path, mode="a", header=header, sep="|", index=False
            )
            header = False
            log.info("Completed Video Tags data read iteration", iteration=idx)
        ret.video_tags = pd.read_csv(video_tags_path, sep="|")
        log.info(
            "Query success. Tag loaded into DF.",
            df_shape=ret.video_tags.shape,
            df_cols=ret.video_tags.columns,
        )
        # Disable historical data processing.
        # ret.hist_alarms_path = self._history_reader.read_history_data(
        #     self._history_cache_path,
        #     self._tmp_dir_name,
        #     ret.doors,
        #     ret.employees_path,
        #     min_time_rds,
        # )
        ret.hist_alarms_path = None
        return ret

    def extract_data_onprem(self) -> ETLExtraction:
        """Connects to Hakimo RDS and loads
        data using the queries with 'rds' string in the name
        in the query folder.

        Once all of these are loaded/saved, the
        min() 'alarm_timestamp_utc' from
        RDS is set to variable
        'min_time_rds', which is then passed as
        the upper timebound to read_history_data()
        so that there is no overlap between historical data and
        RDS data
        """
        ret = ETLExtraction()
        # get doors data
        doors_query_file = osp.join(self._query_folder, "rds_doors.sql")
        log.info("Extracting data...", query_file=doors_query_file)
        ret.doors = misc_utils.execute_query_from_file(
            doors_query_file,
            self._source_engine,
            {"tenant_id": self._tenant_id},
        )
        log.info(
            "Query success. Door Data loaded into DF.",
            df_shape=ret.doors.shape,
            df_cols=ret.doors.columns,
        )
        # get cameras data
        cameras_query_file = osp.join(self._query_folder, "rds_cameras.sql")
        log.info("Extracting data...", query_file=cameras_query_file)
        ret.cameras = misc_utils.execute_query_from_file(
            cameras_query_file,
            self._source_engine,
            self.get_query_args(),
        )
        log.info(
            "Query success. Camera Data loaded into DF.",
            df_shape=ret.doors.shape,
            df_cols=ret.doors.columns,
        )
        # get event data
        event_query_file = osp.join(self._query_folder, "hakimo_rds_query.sql")
        log.info("Extracting data...", query_file=event_query_file)

        df_iter = misc_utils.execute_paginated_query_from_file(
            event_query_file,
            self._source_engine,
            {
                "tenant_id": self._tenant_id,
                "partition_key": get_default_partition_key(),
            },
        )
        Path(self._df_path).unlink(missing_ok=True)
        header = True
        min_time_rds = datetime.datetime.max
        num_rows = 0
        log.info("Iterating through data...", query_file=event_query_file)
        for idx, df in enumerate(df_iter):
            start_time = time.time()  # Record start time
            if not df.empty:
                min_time_rds = min(
                    df["alarm_timestamp_utc"].min(), min_time_rds
                )
            data_checks.check_for_duplicate_ids(df)
            misc_utils.clear_newlines(df, "alarm_type")
            misc_utils.clear_newlines(df, "source_id")
            df.to_csv(
                self._df_path, mode="a", header=header, sep="|", index=False
            )
            header = False
            cols = df.columns
            end_time = time.time()  # Record end time
            duration = end_time - start_time  # Calculate duration
            log.info(
                "Completed RDS data read iteration",
                iteration=idx,
                duration=duration,  # Log duration
            )
        ret.raw_alarms_path = self._df_path
        log.info(
            "Query success. Data written to disk.",
            df_shape=(num_rows, len(cols)),
            df_cols=cols,
        )
        # get incidents data
        incidents_query_file = osp.join(
            self._query_folder, "rds_incidents.sql"
        )
        ret.incidents = misc_utils.execute_query_from_file(
            incidents_query_file,
            self._source_engine,
            {"tenant_id": self._tenant_id},
            iterate=False,
        )
        # get ml output data
        ml_output_query_file = osp.join(
            self._query_folder, "rds_ml_outputs.sql"
        )
        log.info("Extracting data...", query_file=ml_output_query_file)
        ml_output_df_iter = misc_utils.execute_paginated_query_from_file(
            ml_output_query_file,
            self._source_engine,
            {"tenant_id": self._tenant_id},
        )
        header = True
        ml_outputs_path = self._ml_outputs_path
        Path(ml_outputs_path).unlink(missing_ok=True)
        for idx, df in enumerate(ml_output_df_iter):
            df.to_csv(
                ml_outputs_path, mode="a", header=header, sep="|", index=False
            )
            header = False
            log.info("Completed ML Outputs data read iteration", iteration=idx)
        ret.ml_outputs = pd.read_csv(ml_outputs_path, sep="|")
        # Convert the 'timestamp_utc' column to datetime
        ret.ml_outputs["ml_output_timestamp_utc"] = pd.to_datetime(
            ret.ml_outputs["ml_output_timestamp_utc"], errors="coerce"
        ).dt.tz_localize(None)
        # get alarm update data
        alarm_update_query_file = osp.join(
            self._query_folder, "rds_alarm_updates.sql"
        )
        log.info("Extracting data...", query_file=alarm_update_query_file)
        alarm_update_df_iter = misc_utils.execute_paginated_query_from_file(
            alarm_update_query_file,
            self._source_engine,
            {"tenant_id": self._tenant_id},
        )
        header = True
        alarm_updates_path = self._alarm_updates_path
        Path(alarm_updates_path).unlink(missing_ok=True)
        for idx, df in enumerate(alarm_update_df_iter):
            df.to_csv(
                alarm_updates_path,
                mode="a",
                header=header,
                sep="|",
                index=False,
            )
            header = False
            log.info(
                "Completed Alarm Updates data read iteration", iteration=idx
            )
        ret.alarm_updates = pd.read_csv(alarm_updates_path, sep="|")
        ret.alarm_updates["update_timestamp_utc"] = pd.to_datetime(
            ret.alarm_updates["update_timestamp_utc"], errors="coerce"
        ).dt.tz_localize(None)
        log.info(
            "Query success. Tag loaded into DF.",
            df_shape=ret.ml_outputs.shape,
            df_cols=ret.ml_outputs.columns,
        )
        # get employee data
        emp_query_file = osp.join(self._query_folder, "rds_employees.sql")
        log.info("Extracting data...", query_file=emp_query_file)
        df_iter = misc_utils.execute_paginated_query_from_file(
            emp_query_file,
            self._source_engine,
            {"tenant_id": self._tenant_id},
        )
        header = True
        Path(self._emp_path).unlink(missing_ok=True)
        for idx, df in enumerate(df_iter):
            df.to_csv(
                self._emp_path, mode="a", header=header, sep="|", index=False
            )
            header = False
            log.info("Completed Employees data read iteration", iteration=idx)
        log.info(
            "Query success. Emp Data loaded into file.", df_path=self._emp_path
        )
        ret.employees_path = self._emp_path
        # get tags data
        tags_query_file = osp.join(self._query_folder, "rds_video_tags.sql")
        log.info("Extracting data...", query_file=tags_query_file)
        video_tags_df_iter = misc_utils.execute_paginated_query_from_file(
            tags_query_file,
            self._source_engine,
            {"tenant_id": self._tenant_id},
        )
        header = True
        video_tags_path = self._video_tags_path
        Path(video_tags_path).unlink(missing_ok=True)
        for idx, df in enumerate(video_tags_df_iter):
            df.to_csv(
                video_tags_path, mode="a", header=header, sep="|", index=False
            )
            header = False
            log.info("Completed Video Tags data read iteration", iteration=idx)
        ret.video_tags = pd.read_csv(video_tags_path, sep="|")
        log.info(
            "Query success. Tag loaded into DF.",
            df_shape=ret.video_tags.shape,
            df_cols=ret.video_tags.columns,
        )
        ret.hist_alarms_path = self._history_reader.read_history_data_onprem(
            self._history_cache_path,
            self._tmp_dir_name,
            ret.doors,
            ret.employees_path,
            min_time_rds,
        )
        return ret
