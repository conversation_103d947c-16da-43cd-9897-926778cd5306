import os
import os.path as osp
import typing
from enum import Enum

import numpy as np
import pandas as pd
import structlog
from sqlalchemy import text as sqltext

from common_utils.local_utils import move_file
from common_utils.s3_utils import (
    delete_objects_in_bucket_prefix,
    get_s3_path_details,
    upload_file_to_s3,
)
from config import backend_config as config
from insights.etl.interfaces import ETLProcessOutput

log = structlog.get_logger(
    "hakimo.etl.insights", module="InsightsETL Data Loader"
)


class ObjectStorage(str, Enum):
    local = "local"
    s3 = "s3"


class ETLLoader:
    def __init__(
        self,
        dest_engine,
        query_folder: str,
        s3_upload_path: str,
        tenant_id: str,
    ):
        self._query_folder = query_folder
        self._dest_engine = dest_engine
        self._s3_upload_path = s3_upload_path
        self._tenant_id = tenant_id

    def load_data(
        self, processed_data: ETLProcessOutput, keep_tenant_data: bool = False
    ) -> None:
        """
        1. Uploads data to s3.
        2. Deletes existing rows in redshift for tenant_id.
        3. Copies new and replacement rows into redshift for tenant_id
        """
        # upload data to s3
        s3_details = get_s3_path_details(self._s3_upload_path)
        if s3_details is None:
            raise ValueError("Invalid S3 Upload path")
        self._upload_csvs_to_s3(processed_data, s3_details)

        log.info("Loading data into data warehouse")
        if not keep_tenant_data:
            log.info("Removing existing tenant data.")
            with open(
                osp.join(self._query_folder, "redshift_del_tenant.sql"), "r"
            ) as f:
                sql = sqltext(f.read())
            with self._dest_engine.connect().execution_options(
                autocommit=True
            ) as conn:
                conn.execute(sql, tenant_id=self._tenant_id)

        # ingest new data for that tenant into redshift
        with open(
            osp.join(self._query_folder, "redshift_ingest.sql"), "r"
        ) as f:
            sql = sqltext(f.read())

        # delete old records for alarms with latest updates
        if processed_data.unfound_alarms_output_path is not None:
            self._delete_existing_records_for_unfound_alarms(processed_data)

        with self._dest_engine.connect().execution_options(
            autocommit=True
        ) as conn:
            conn.execute(
                sql,
                s3_folder=osp.join(
                    f"s3://{s3_details['bucket_name']}",
                    s3_details["file_name"],
                ),
                s3_region=config.HAIE.AWS_S3_REGION,
            )

        log.info("Write to RedShift succeeded")

    def load_data_onprem(self, processed_data: ETLProcessOutput) -> str:
        """
        1. Uploads data to shared file system.
        """
        object_storage_type = config.gateway()["OBJECT_STORAGE"]["type"]
        log.info("data is loaded for onprem", type=object_storage_type)
        return self._upload_csv(processed_data, object_storage_type)

    def _upload_csv(
        self,
        processed_data: ETLProcessOutput,
        object_type_storage: str,
    ) -> str:
        if ObjectStorage.local == object_type_storage:
            # data is saved in CSV
            for csv in processed_data.output_paths:
                storage_path = config.gateway()["OBJECT_STORAGE"]["path"]
                dest_filename = osp.join(storage_path, osp.basename(csv))
                move_file(csv, dest_filename)
            return dest_filename
        s3_details = get_s3_path_details(self._s3_upload_path)
        if s3_details is None:
            raise ValueError("Invalid S3 Upload path")
        self._upload_csvs_to_s3(processed_data, s3_details)
        return ""

    def _upload_csvs_to_s3(
        self,
        processed_data: ETLProcessOutput,
        s3_details: typing.Dict[str, str],
    ) -> None:
        """
        1. Deletes existing processed data (csvs) from s3.
        2. Uploads new processed data to s3.
        """
        log.info("Deleting existing objects in dest S3 prefix")
        delete_objects_in_bucket_prefix(
            s3_details["bucket_name"], prefix=s3_details["file_name"]
        )
        log.info("Uploading data to S3")
        unfound_alarms_csv_path = (
            [processed_data.unfound_alarms_output_path]
            if processed_data.unfound_alarms_output_path
            else []
        )
        csv_file_paths = processed_data.output_paths + unfound_alarms_csv_path
        for csv in csv_file_paths:
            s3_filename = osp.join(s3_details["file_name"], osp.basename(csv))
            upload_file_to_s3(s3_details["bucket_name"], s3_filename, csv)
            log.info(
                "Uploaded successfully",
                local_file=csv,
                s3_bucket=s3_details["bucket_name"],
                s3_filename=s3_filename,
            )
            # run_create_hyper_file_from_csv(csv, "insights_ds.hyper")
            # publish_hyper("insights_ds.hyper")
            if csv not in unfound_alarms_csv_path:
                os.remove(csv)

    def _delete_existing_records_for_unfound_alarms(
        self, processed_data: ETLProcessOutput
    ):
        log.info("Updating existing records in Redshift.")

        # Prepare the IDs and data to update from processed_data
        unfound_alarms_df = pd.read_csv(
            processed_data.unfound_alarms_output_path
        )
        unfound_alarms_df = unfound_alarms_df.replace({np.nan: None})

        # Generate the update query
        with open(
            osp.join(self._query_folder, "redshift_del_alarms_query.sql"), "r"
        ) as f:
            delete_query = sqltext(f.read())

        with self._dest_engine.connect().execution_options(
            autocommit=True
        ) as conn:
            alarm_ids = unfound_alarms_df["id"].tolist()
            for i in range(0, len(alarm_ids), 1000):
                try:
                    conn.execute(
                        delete_query,
                        ids='"' + '", "'.join(alarm_ids[i : i + 1000]) + '"',
                    )
                except Exception as e:
                    log.error(
                        "Error while updating record",
                        error=str(e),
                    )
            os.remove(str(processed_data.unfound_alarms_output_path))
