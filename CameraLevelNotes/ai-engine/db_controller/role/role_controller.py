from typing import List

from cachetools import TTL<PERSON>ache, cachedmethod
from sqlalchemy import select

from db_controller.interface import ControllerBase
from models_rds.rbac.capabilities import Capabilities
from models_rds.rbac.roles import Roles
from models_rds.rbac.roles_capabilities import RolesCapabilities


class RoleController(ControllerBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Create a TTL cache that expires after 10 minutes (600 seconds)
        self._role_cache = TTLCache(maxsize=512, ttl=600)

    @cachedmethod(lambda self: self._role_cache)
    def get_capabilities_for_role(self, role_id: int) -> List[str]:
        with self.db.get_session() as sess:
            query = (
                select(Capabilities.name)
                .join(RolesCapabilities)
                .join(Roles)
                .filter(Roles.int_id == role_id)
                .filter(Roles.int_id == RolesCapabilities.role_id)
                .filter(Capabilities.int_id == RolesCapabilities.capability_id)
                .filter(RolesCapabilities.enabled)
            )
            return sess.execute(query).scalars().all()
