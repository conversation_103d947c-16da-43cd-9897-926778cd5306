import datetime
from dataclasses import dataclass
from typing import List, Optional


@dataclass
class AlarmGroupFilters:
    tenant_ids: Optional[List[str]] = None
    camera_group_ids: Optional[List[str]] = None
    states: Optional[List[str]] = None
    resolutions: Optional[List[str]] = None
    date_from: Optional[datetime.datetime] = None
    date_to: Optional[datetime.datetime] = None
    page: int = 0
    page_size: int = 20

    def __post_init__(self):
        """Validate filter parameters after initialization."""
        if self.page < 0:
            raise ValueError("page must be non-negative")
        if self.page_size <= 0 or self.page_size > 100:
            raise ValueError("page_size must be between 1 and 100")

        # Validate date range
        if self.date_from and self.date_to and self.date_from > self.date_to:
            raise ValueError("date_from cannot be after date_to")
