import random
from datetime import datetime, timezone
from typing import Dict, List, Optional, <PERSON><PERSON>

import sqlalchemy
import structlog
from sqlalchemy import and_, func, or_, select

from db_controller.alarm_group.alarm_group_filters import AlarmGroupFilters
from db_controller.interface import ControllerBase
from db_controller.tenant.tenant_controller import Tenant<PERSON><PERSON>roller
from models_rds.alarm_group import AlarmGroup
from models_rds.event import Event

# from models_rds.camera_groups import CameraGroups
# from models_rds.cameras import Cameras
from vision.services.src.event_processor.models.events import (
    AlarmGroupQueueState,
    AlarmGroupRecommendation,
    AlarmGroupState,
    Resolution,
    Severity,
)

logger = structlog.get_logger("hakimo", "alarm_group_controller")


class AlarmGroupController(ControllerBase):
    # def check_camera_group_exists(self, camera_group_id: str) -> bool:
    #     try:
    #         with self.db.get_session() as session:
    #             query = select(CameraGroups).filter(CameraGroups.id == camera_group_id)
    #             return session.execute(query).scalars().first() is not None
    #     except Exception as e:
    #         logger.error("Error checking if camera group exists", error=str(e), camera_group_id=camera_group_id)
    #         raise

    # def check_camera_has_camera_group(self, camera_group_id: str, camera_id: str) -> bool:
    #     with self.db.get_session() as session:
    #         query = select(Cameras).filter(and_(Cameras.camera_group_id == camera_group_id, Cameras.uuid == camera_id))
    #         return session.execute(query).scalars().first() is not None

    # def get_camera_group_id(self, camera_id: str) -> Optional[str]:
    #     with self.db.get_session() as session:
    #         query = select(Cameras).filter(Cameras.uuid == camera_id)
    #         camera = session.execute(query).scalars().first()
    #         if camera:
    #             return camera.camera_group_id
    #         return None

    def is_auto_resolution_enabled(self, tenant_id: str) -> bool:
        # TODO: WE need to use cache here
        try:
            tenant_controller = TenantController(self.db, self.read_db)
            config = (
                tenant_controller.get_tenant_alarm_processing_config_by_id(
                    tenant_id
                )
            )
            if config is None:
                return False
            return config.get("llmEventAnalyzerConfig", {}).get(
                "resolveAlarmGroup", False
            )
        except Exception:
            return False

    def is_escalation_open(self, alarm_group_id: str) -> bool:
        try:
            with self.read_db.get_session() as session:
                query = select(AlarmGroup).filter(
                    and_(
                        AlarmGroup.id == alarm_group_id,
                        AlarmGroup.state == AlarmGroupState.IN_PROGRESS,
                        AlarmGroup.resolution == Resolution.ESCALATION_OPEN,
                    )
                )
                return (
                    session.execute(query).scalars().one_or_none() is not None
                )
        except Exception:
            return False

    def is_escalation_open_camera_group(self, camera_group_id: str) -> bool:
        try:
            with self.read_db.get_session() as session:
                query = select(AlarmGroup).filter(
                    and_(
                        AlarmGroup.camera_group_id == camera_group_id,
                        AlarmGroup.state == AlarmGroupState.IN_PROGRESS,
                        AlarmGroup.resolution == Resolution.ESCALATION_OPEN,
                    )
                )
                return session.execute(query).scalars().count() > 0
        except Exception:
            return False

    def create_alarm_group_v1(
        self, alarm_group: AlarmGroup, cutoff_time: dict
    ) -> Tuple[str, bool, str]:
        try:
            with self.db.get_session() as session:
                # Get the alarm_group_id which are in pending state
                # if we found it, then check if we need to do the pending_cutoff
                # if we did not find it, then create a new alarm_group_id
                # if it fails due to integrity error, then check if someone else created an active alarm
                # if someone else created it, then return the alarm_group_id
                pending_cutoff_alarm_group_id = None

                escalation_open_alarm_group = (
                    self.get_active_escalation_open_alarm_group(
                        alarm_group.camera_group_id
                    )
                )
                if escalation_open_alarm_group:
                    # TODO: add cutoff for escalations
                    logger.debug(
                        "escalation_open_alarm_group",
                        group_id=escalation_open_alarm_group.id,
                        camera_group_id=escalation_open_alarm_group.camera_group_id,
                        state=escalation_open_alarm_group.state,
                    )
                    return (
                        escalation_open_alarm_group.id,
                        False,
                        pending_cutoff_alarm_group_id,
                    )
                else:
                    logger.debug(
                        "no_escalation_open_alarm_group",
                        camera_group_id=alarm_group.camera_group_id,
                    )

                query = select(AlarmGroup).filter(
                    and_(
                        AlarmGroup.camera_group_id
                        == alarm_group.camera_group_id,
                        AlarmGroup.state == AlarmGroupState.PENDING,
                    )
                )
                existing_active = (
                    session.execute(query).scalars().one_or_none()
                )

                if existing_active:
                    if cutoff_time and cutoff_time.get("isActive", False):
                        event_cutoff_time = cutoff_time.get(
                            "eventCutoffTime", 0
                        )
                        alarm_cutoff_time = cutoff_time.get(
                            "alarmCutoffTime", 0
                        )
                        latest_event_time_utc = (
                            existing_active.latest_event_time_utc
                        )
                        current_event_time_utc = (
                            alarm_group.latest_event_time_utc
                        )
                        # check if latest_event_time_utc is null or invalid, update with current_event_time_utc
                        if latest_event_time_utc is None or not isinstance(
                            latest_event_time_utc, datetime
                        ):
                            latest_event_time_utc = current_event_time_utc
                            existing_active.latest_event_time_utc = (
                                latest_event_time_utc
                            )
                            session.commit()
                        latest_event_time_utc = latest_event_time_utc.replace(
                            tzinfo=timezone.utc
                        )

                        diff = (
                            current_event_time_utc - latest_event_time_utc
                        ).total_seconds()

                        if diff < event_cutoff_time:
                            return (
                                existing_active.id,
                                False,
                                pending_cutoff_alarm_group_id,
                            )
                        alarm_start_time_utc = existing_active.start_time_utc
                        alarm_start_time_utc = alarm_start_time_utc.replace(
                            tzinfo=timezone.utc
                        )
                        event_start_time_utc = alarm_group.start_time_utc
                        diff = (
                            event_start_time_utc - alarm_start_time_utc
                        ).total_seconds()
                        if diff < alarm_cutoff_time:
                            return (
                                existing_active.id,
                                False,
                                pending_cutoff_alarm_group_id,
                            )
                        self.update_alarm_group_pending_cutoff(
                            existing_active.id
                        )
                        pending_cutoff_alarm_group_id = existing_active.id
                    else:
                        # If cutoff is not active, return the existing active alarm group
                        return (
                            existing_active.id,
                            False,
                            pending_cutoff_alarm_group_id,
                        )
                try:
                    session.add(alarm_group)
                    session.flush()
                    logger.debug(
                        "alarm_group_created",
                        group_id=alarm_group.id,
                        camera_group_id=alarm_group.camera_group_id,
                        state=alarm_group.state,
                        pending_cutoff_alarm_group_id=pending_cutoff_alarm_group_id,
                    )
                    return (
                        alarm_group.id,
                        True,
                        pending_cutoff_alarm_group_id,
                    )
                except sqlalchemy.exc.IntegrityError as e:
                    session.rollback()
                    query = select(AlarmGroup).filter(
                        and_(
                            AlarmGroup.camera_group_id
                            == alarm_group.camera_group_id,
                            AlarmGroup.state == AlarmGroupState.PENDING,
                        )
                    )
                    existing_active = (
                        session.execute(query).scalars().one_or_none()
                    )
                    if existing_active:
                        return (existing_active.id, False, None)
                    # If no existing active found, it was a different integrity error
                    logger.error(
                        "unexpected_integrity_error",
                        error=str(e),
                        camera_group_id=alarm_group.camera_group_id,
                        tenant_id=alarm_group.tenant_id,
                        exc_info=True,
                    )
                    raise
        except Exception as e:
            logger.error(
                "unexpected_error_creating_alarm_group",
                error=str(e),
                exc_info=True,
            )
            raise

    def create_alarm_group(
        self, alarm_group: AlarmGroup, cutoff_time: dict
    ) -> Tuple[str, bool, str]:
        try:
            with self.db.get_session() as session:
                # Optimistic check first for better performance
                query = select(AlarmGroup).filter(
                    and_(
                        AlarmGroup.camera_group_id
                        == alarm_group.camera_group_id,
                        AlarmGroup.state.in_(
                            [
                                AlarmGroupState.PENDING,
                                AlarmGroupState.IN_PROGRESS,
                            ]
                        ),
                    )
                )
                existing_active = session.execute(query).scalars().first()

                pending_cutoff_alarm_group_id = None

                if existing_active:
                    logger.debug(
                        "active_alarm_group_exists",
                        group_id=existing_active.id,
                        camera_group_id=existing_active.camera_group_id,
                        state=existing_active.state,
                    )
                    if (
                        existing_active.resolution
                        == Resolution.ESCALATION_OPEN
                    ):
                        # No cut off in case of escalation open
                        # TODO: Add  cut off logic for escalation open
                        return (
                            existing_active.id,
                            False,
                            pending_cutoff_alarm_group_id,
                        )
                    if cutoff_time and cutoff_time.get("isActive", False):
                        event_cutoff_time = cutoff_time.get(
                            "eventCutoffTime", 0
                        )
                        alarm_cutoff_time = cutoff_time.get(
                            "alarmCutoffTime", 0
                        )
                        latest_event_time_utc = (
                            existing_active.latest_event_time_utc
                        )
                        current_event_time_utc = (
                            alarm_group.latest_event_time_utc
                        )
                        # check if latest_event_time_utc is null or invalid, update with current_event_time_utc
                        if latest_event_time_utc is None or not isinstance(
                            latest_event_time_utc, datetime
                        ):
                            latest_event_time_utc = current_event_time_utc
                            existing_active.latest_event_time_utc = (
                                latest_event_time_utc
                            )
                            session.commit()
                        latest_event_time_utc = latest_event_time_utc.replace(
                            tzinfo=timezone.utc
                        )

                        diff = (
                            current_event_time_utc - latest_event_time_utc
                        ).total_seconds()

                        if diff < event_cutoff_time:
                            return (
                                existing_active.id,
                                False,
                                pending_cutoff_alarm_group_id,
                            )
                        alarm_start_time_utc = existing_active.start_time_utc
                        alarm_start_time_utc = alarm_start_time_utc.replace(
                            tzinfo=timezone.utc
                        )
                        event_start_time_utc = alarm_group.start_time_utc
                        diff = (
                            event_start_time_utc - alarm_start_time_utc
                        ).total_seconds()
                        if diff < alarm_cutoff_time:
                            return (
                                existing_active.id,
                                False,
                                pending_cutoff_alarm_group_id,
                            )
                        self.update_alarm_group_pending_cutoff(
                            existing_active.id
                        )
                        pending_cutoff_alarm_group_id = existing_active.id
                    else:
                        # If cutoff is not active, return the existing active alarm group
                        return (
                            existing_active.id,
                            False,
                            pending_cutoff_alarm_group_id,
                        )
                try:
                    session.add(alarm_group)
                    session.flush()
                    logger.debug(
                        "alarm_group_created",
                        group_id=alarm_group.id,
                        camera_group_id=alarm_group.camera_group_id,
                        state=alarm_group.state,
                    )
                    return (
                        alarm_group.id,
                        True,
                        pending_cutoff_alarm_group_id,
                    )

                except sqlalchemy.exc.IntegrityError as e:
                    session.rollback()
                    # Double check if someone else created an active alarm
                    # between our check and insert
                    query = select(AlarmGroup).filter(
                        and_(
                            AlarmGroup.camera_group_id
                            == alarm_group.camera_group_id,
                            AlarmGroup.state.in_(
                                [
                                    AlarmGroupState.PENDING,
                                    AlarmGroupState.IN_PROGRESS,
                                ]
                            ),
                        )
                    )
                    existing_active = session.execute(query).scalars().first()

                    if existing_active:
                        logger.info(
                            "active_alarm_group_created_by_another_transaction",
                            group_id=existing_active.id,
                            camera_group_id=existing_active.camera_group_id,
                            state=existing_active.state,
                        )
                        return (
                            existing_active.id,
                            False,
                            pending_cutoff_alarm_group_id,
                        )

                    # If no existing active found, it was a different integrity error
                    logger.error(
                        "unexpected_integrity_error",
                        error=str(e),
                        camera_group_id=alarm_group.camera_group_id,
                        tenant_id=alarm_group.tenant_id,
                        exc_info=True,
                    )
                    raise

        except Exception as e:
            logger.error(
                "unexpected_error_creating_alarm_group",
                error=str(e),
                exc_info=True,
            )
            raise

    def get_active_escalation_open_alarm_group(
        self, camera_group_id: str
    ) -> Optional[AlarmGroup]:
        try:
            with self.read_db.get_session() as session:
                query = (
                    select(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.camera_group_id == camera_group_id,
                            AlarmGroup.state == AlarmGroupState.IN_PROGRESS,
                            AlarmGroup.resolution
                            == Resolution.ESCALATION_OPEN,
                        )
                    )
                    .order_by(AlarmGroup.start_time_utc.desc())
                )
                result = session.execute(query).scalars().first()
                session.expunge_all()
                return result
        except Exception:
            return None

    def get_alarm_group_escalation_open(
        self, group_id: str, severity: Severity
    ) -> Optional[str]:
        try:
            with self.read_db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.camera_group_id == group_id,
                            AlarmGroup.state.in_(
                                [
                                    AlarmGroupState.PENDING,
                                    AlarmGroupState.IN_PROGRESS,
                                ]
                            ),
                            AlarmGroup.resolution.in_(
                                [Resolution.ESCALATION_OPEN, Resolution.OPEN]
                            ),
                            AlarmGroup.severity == severity,
                        )
                    )
                    .first()
                )
                if not alarm_group:
                    return None
                return alarm_group.id
        except Exception as e:
            logger.error(
                "Error getting pending alarm group escalation open",
                error=str(e),
                group_id=group_id,
            )
            raise

    def get_pending_alarm_group(self, group_id: str) -> Optional[str]:
        """
        Get an alarm group by ID that is in PENDING state
        Args:
                group_id: ID of the alarm group to retrieve
        Returns:
                AlarmGroup if found in PENDING state, None otherwise
        """
        try:
            with self.db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.camera_group_id == group_id,
                            AlarmGroup.state == AlarmGroupState.PENDING,
                        )
                    )
                    .first()
                )
                return alarm_group.id

        except Exception as e:
            logger.error(
                "error_getting_pending_alarm_group",
                error=str(e),
                group_id=group_id,
                exc_info=True,
            )
            raise

    def get_escalated_alarm_group(self, group_id: str) -> Optional[AlarmGroup]:
        """
        Get an alarm group by ID that is in IN_PROGRESS state with ESCALATION_OPEN resolution
        Args:
                group_id: ID of the alarm group to retrieve
        Returns:
                AlarmGroup if found with matching criteria, None otherwise
        """
        try:
            with self.read_db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.camera_group_id == group_id,
                            AlarmGroup.state == AlarmGroupState.IN_PROGRESS,
                            AlarmGroup.resolution
                            == Resolution.ESCALATION_OPEN,
                        )
                    )
                    .first()
                )
                if not alarm_group:
                    return None
                return alarm_group.id

        except Exception as e:
            logger.error(
                "error_getting_escalated_alarm_group",
                error=str(e),
                group_id=group_id,
                exc_info=True,
            )
            raise

    def update_alarm_group(self, alarm_group: AlarmGroup) -> AlarmGroup:
        with self.db.get_session() as session:
            session.add(alarm_group)
            session.commit()
            return alarm_group

    def update_alarm_group_recommendation(
        self, alarm_group_id: str, recommendation: AlarmGroupRecommendation
    ) -> str:
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id == alarm_group_id
                ).update({"recommendation": recommendation})
                session.commit()
                return alarm_group_id
        except Exception as e:
            logger.error(
                "error_updating_alarm_group_recommendation",
                error=str(e),
                alarm_group_id=alarm_group_id,
                recommendation=recommendation,
            )
            raise

    def update_alarm_group_escalation_close_event(
        self,
        alarm_group_id: str,
        end_time: datetime,
        operator_id: str,
        resolution_comment: str,
        state: AlarmGroupState,
    ) -> str:
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id == alarm_group_id
                ).update(
                    {
                        "state": state,
                        "resolution": Resolution.ESCALATION_CLOSE,
                        "end_time_utc": end_time,
                        "operator_id": operator_id,
                        "resolution_comment": resolution_comment,
                    }
                )
                session.commit()
                return alarm_group_id
        except Exception as e:
            logger.error(
                "Error updating alarm group escalation close event",
                error=str(e),
                alarm_group_id=alarm_group_id,
                end_time=end_time,
                operator_id=operator_id,
                resolution_comment=resolution_comment,
            )
            raise

    def update_alarm_group_escalation_open_event(
        self, alarm_group_id: str, start_time: datetime, operator_id: str
    ) -> str:
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id == alarm_group_id
                ).update(
                    {
                        "state": AlarmGroupState.IN_PROGRESS,
                        "resolution": Resolution.ESCALATION_OPEN,
                        "start_time_utc": start_time,
                        "operator_id": operator_id,
                    }
                )
                session.commit()
                return alarm_group_id
        except Exception as e:
            logger.error(
                "Error updating alarm group escalation open event",
                error=str(e),
                alarm_group_id=alarm_group_id,
                start_time=start_time,
                operator_id=operator_id,
            )
            raise

    def update_alarm_group_pending_cutoff(self, alarm_group_id: str) -> str:
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id == alarm_group_id
                ).update({"state": AlarmGroupState.PENDING_CUTOFF})
                session.commit()
                return alarm_group_id
        except Exception as e:
            logger.error(
                "Error updating alarm group pending cutoff",
                error=str(e),
                alarm_group_id=alarm_group_id,
            )
            raise

    def update_alarm_group_safe_event(
        self,
        alarm_group_id: str,
        resolution: Resolution,
        resolution_comment: str,
        end_time: datetime,
        operator_id: str,
        state: AlarmGroupState,
    ) -> str:
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id == alarm_group_id
                ).update(
                    {
                        "state": state,
                        "resolution": resolution,
                        "resolution_comment": resolution_comment,
                        "end_time_utc": end_time,
                        "operator_id": operator_id,
                    }
                )
                session.commit()
                return alarm_group_id
        except Exception as e:
            logger.error(
                "error_updating_alarm_group_safe_event",
                error=str(e),
                alarm_group_id=alarm_group_id,
                exc_info=True,
            )
            raise

    def get_active_alarm_groups(
        self, camera_group_ids: List[str]
    ) -> List[dict]:
        """
        Get alarm groups by camera_group_ids that are in either PENDING or IN_PROGRESS state
        Args:
                camera_group_ids: List of camera group IDs to retrieve associated alarm groups
        Returns:
                List of event_data dictionaries if found, empty list otherwise
        """
        try:
            with self.read_db.get_session() as session:
                query = session.query(AlarmGroup).filter(
                    and_(
                        AlarmGroup.camera_group_id.in_(camera_group_ids),
                        AlarmGroup.state.in_(
                            [
                                AlarmGroupState.PENDING,
                                AlarmGroupState.IN_PROGRESS,
                                AlarmGroupState.PENDING_CUTOFF,
                            ]
                        ),
                    )
                )
                alarm_groups = query.all()
                event_datas = []
                for alarm_group in alarm_groups:
                    event_data_high = {
                        "alarm_group_id": alarm_group.id,
                        "tenant_id": alarm_group.tenant_id,
                        "camera_group_id": alarm_group.camera_group_id,
                        "severity": Severity.HIGH,
                    }
                    event_data_low = {
                        "alarm_group_id": alarm_group.id,
                        "tenant_id": alarm_group.tenant_id,
                        "camera_group_id": alarm_group.camera_group_id,
                        "severity": Severity.LOW,
                    }
                    event_datas.append(event_data_high)
                    event_datas.append(event_data_low)
                return event_datas
        except Exception as e:
            logger.error(
                "error_getting_active_alarm_groups",
                error=str(e),
                camera_group_ids=camera_group_ids,
                exc_info=True,
            )
            raise

    def get_active_alarm_group(
        self, camera_group_id: str, severity: Optional[Severity] = None
    ) -> Tuple[Optional[str], Optional[AlarmGroup]]:
        """
        Get an alarm group by camera_group_id that is in either PENDING or IN_PROGRESS state
        Args:
                group_id: ID of the camera group to retrieve associated alarm group
                severity: Severity of the alarm group to retrieve
        Returns:
                Tuple of (alarm_group_id, alarm_group) if found, (None, None) otherwise
        """
        try:
            with self.read_db.get_session() as session:
                query = session.query(AlarmGroup).filter(
                    and_(
                        AlarmGroup.camera_group_id == camera_group_id,
                        AlarmGroup.state.in_(
                            [
                                AlarmGroupState.PENDING,
                                AlarmGroupState.IN_PROGRESS,
                            ]
                        ),
                    )
                )
                if severity:
                    query = query.filter(AlarmGroup.severity == severity)
                alarm_group = query.first()
                if not alarm_group:
                    return None, None
                return alarm_group.id, alarm_group

        except Exception as e:
            logger.error(
                "error_getting_active_alarm_group",
                error=str(e),
                camera_group_id=camera_group_id,
                exc_info=True,
            )
            raise

    def update_alarm_group_operator_id(
        self, id: str, operator_id: Optional[str]
    ):
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(AlarmGroup.id == id).update(
                    {"operator_id": operator_id}
                )
                session.commit()
        except Exception as e:
            logger.error(
                "Error updating alarm group operator id",
                error=str(e),
                id=id,
                operator_id=operator_id,
            )
            raise

    def get_pending_alarm_group_ids_for_operator(
        self, operator_id: str
    ) -> List[Tuple[str, str, str]]:
        try:
            with self.read_db.get_session() as session:
                alarm_groups = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.operator_id == operator_id,
                            AlarmGroup.state.in_(
                                [
                                    AlarmGroupState.PENDING,
                                    AlarmGroupState.IN_PROGRESS,
                                    AlarmGroupState.PENDING_CUTOFF,
                                ]
                            ),
                        )
                    )
                    .all()
                )
                if not alarm_groups:
                    return []
                return [
                    (ag.id, ag.camera_group_id, ag.tenant_id)
                    for ag in alarm_groups
                ]

        except Exception as e:
            logger.error(
                "error_getting_pending_alarm_groups_for_operator",
                error=str(e),
                operator_id=operator_id,
                exc_info=True,
            )
            raise

    def mark_alarm_groups_orphaned(self, alarm_group_ids: List[str]) -> None:
        """
        Bulk-move the given alarm groups to the 'orphan' operator.
        """
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id.in_(alarm_group_ids)
                ).update(
                    {"operator_id": "orphan"}, synchronize_session="fetch"
                )
                session.commit()
        except Exception as e:
            logger.error(
                "error_modifying_alarm_groups_to_orphan",
                error=str(e),
                alarm_group_ids=alarm_group_ids,
                exc_info=True,
            )
            raise

    def mark_alarm_groups_as_ready_to_be_allocated(
        self, alarm_group_ids: List[str]
    ) -> None:
        """
        Mark the given alarm groups as ready to be allocated.
        """
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id.in_(alarm_group_ids)
                ).update(
                    {"operator_id": "ready_to_be_allocated"},
                    synchronize_session="fetch",
                )
                session.commit()
        except Exception as e:
            logger.error(
                "error_marking_alarm_groups_as_ready_to_be_allocated",
                error=str(e),
                alarm_group_ids=alarm_group_ids,
                exc_info=True,
            )
            raise

    def update_alarm_groups_queue_state(
        self,
        alarm_group_ids: List[str],
        queue_state: AlarmGroupQueueState,
        time_stamp_utc: datetime,
    ) -> None:
        """
        Update the queue state of the given alarm groups.
        """
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id.in_(alarm_group_ids)
                ).update(
                    {
                        "alarm_group_queue_state": queue_state,
                        "alarm_group_queue_state_updated_at_utc": time_stamp_utc,
                    },
                    synchronize_session="fetch",
                )
                session.commit()
        except Exception as e:
            logger.error(
                "error_updating_alarm_groups_queue_state",
                error=str(e),
                alarm_group_ids=alarm_group_ids,
                queue_state=queue_state,
                exc_info=True,
            )
            raise

    def mark_alarm_group_as_ready_to_be_allocated(
        self, alarm_group_id: str
    ) -> None:
        """
        Mark the given alarm groups as ready to be allocated.
        """
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id == alarm_group_id
                ).update(
                    {"operator_id": "ready_to_be_allocated"},
                    synchronize_session="fetch",
                )
                session.commit()
        except Exception as e:
            logger.error(
                "error_marking_alarm_group_as_ready_to_be_allocated",
                error=str(e),
                alarm_group_id=alarm_group_id,
                exc_info=True,
            )
            raise

    def get_active_alarm_group_for_operator(
        self, group_id: str
    ) -> Optional[str]:
        """
        Get an alarm group by ID and operator ID that is in either PENDING or IN_PROGRESS state
        Args:
                group_id: ID of the alarm group to retrieve
                operator_id: ID of the operator assigned to the alarm group
        Returns:
                AlarmGroup ID if found in PENDING or IN_PROGRESS state with matching operator, None otherwise
        """
        try:
            with self.db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.camera_group_id == group_id,
                            AlarmGroup.state.in_(
                                [
                                    AlarmGroupState.PENDING,
                                    AlarmGroupState.IN_PROGRESS,
                                ]
                            ),
                        )
                    )
                    .first()
                )
                if not alarm_group:
                    return None
                return alarm_group.id

        except Exception as e:
            logger.error(
                "error_getting_active_alarm_group_for_operator",
                error=str(e),
                group_id=group_id,
                exc_info=True,
            )
            raise

    def update_alarm_group_severity(
        self, alarm_group_id: str, severity: Severity
    ) -> str:
        """
        Update the severity of an alarm group
        Args:
                alarm_group_id: ID of the alarm group to update
                severity: New severity value to set
        Returns:
                The alarm group ID
        """
        try:
            with self.db.get_session() as session:
                session.query(AlarmGroup).filter(
                    AlarmGroup.id == alarm_group_id
                ).update({"severity": severity})
                session.commit()
                return alarm_group_id
        except Exception as e:
            logger.error(
                "error_updating_alarm_group_severity",
                error=str(e),
                alarm_group_id=alarm_group_id,
                severity=severity,
                exc_info=True,
            )
            raise

    def get_start_time_utc(self, alarm_group_id: str) -> Optional[datetime]:
        try:
            with self.read_db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(AlarmGroup.id == alarm_group_id)
                    .first()
                )
                if not alarm_group:
                    return None
                return alarm_group.start_time_utc
        except Exception as e:
            logger.error(
                "error_getting_start_time_utc",
                error=str(e),
                alarm_group_id=alarm_group_id,
                exc_info=True,
            )
            raise

    def get_camera_group_id_and_tenant_id_of_alarm_group(
        self, alarm_group_id: str
    ) -> Tuple[Optional[str], Optional[str]]:
        try:
            with self.read_db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(AlarmGroup.id == alarm_group_id)
                    .first()
                )
                if not alarm_group:
                    return None, None
                return alarm_group.camera_group_id, alarm_group.tenant_id
        except Exception as e:
            logger.error(
                "error_getting_camera_group_id_and_tenant_id_of_alarm_group",
                error=str(e),
                alarm_group_id=alarm_group_id,
                exc_info=True,
            )
            raise

    def get_pending_alarm_groups_for_tenants_v1(
        self, tenant_ids: List[str], limit: int = 9, order: str = "desc"
    ) -> List[Tuple[str, datetime]]:
        """
        Get camera group IDs for pending alarm groups for a list of tenants
        Args:
                tenant_ids: List of tenant IDs to retrieve alarm groups for
                limit: Limit on the number of results to return (default: 9)
                order: Ordering for start_time_utc - 'desc' (default), 'asc', or 'random'
        Returns:
                List of camera_group_id strings ordered by start_time_utc
        """
        try:
            with self.read_db.get_session() as session:
                # Determine order column based on order parameter
                min_start_time_utc = sqlalchemy.func.min(
                    AlarmGroup.start_time_utc
                ).label("min_start_time_utc")

                if order == "random":
                    # Randomly choose between ascending and descending order
                    order_column = (
                        min_start_time_utc.desc()
                        if random.choice([True, False])
                        else min_start_time_utc
                    )
                elif order == "asc":
                    order_column = min_start_time_utc
                else:  # default to desc
                    order_column = min_start_time_utc.desc()

                query = (
                    session.query(
                        AlarmGroup.camera_group_id, min_start_time_utc
                    )
                    .filter(
                        or_(
                            AlarmGroup.operator_id.is_(None),
                            AlarmGroup.operator_id == "orphan",
                        ),
                        AlarmGroup.state.in_(
                            [
                                AlarmGroupState.PENDING,
                                AlarmGroupState.IN_PROGRESS,
                                AlarmGroupState.PENDING_CUTOFF,
                            ]
                        ),
                        AlarmGroup.tenant_id.in_(tenant_ids),
                    )
                    .group_by(AlarmGroup.camera_group_id)
                    .order_by(order_column)
                    .limit(limit)
                )

                result = query.all()
                camera_group_ids = [
                    (
                        row.camera_group_id,
                        row.min_start_time_utc,
                    )
                    for row in result
                ]
                return camera_group_ids
        except Exception as e:
            logger.error(
                "error_getting_pending_alarm_groups_for_tenants",
                error=str(e),
                tenant_ids=tenant_ids,
                limit=limit,
                order=order,
                exc_info=True,
            )
            raise

    def get_pending_alarm_groups_for_tenants(
        self, tenant_ids: List[str], limit: int = 9, order: str = "desc"
    ) -> List[Tuple[str, datetime]]:
        """
        Get camera group IDs for pending alarm groups for a list of tenants
        Args:
                tenant_ids: List of tenant IDs to retrieve alarm groups for
                limit: Limit on the number of results to return (default: 9)
                order: Ordering for start_time_utc - 'desc' (default), 'asc', or 'random'
        Returns:
                List of camera_group_id strings ordered by start_time_utc
        """
        try:
            with self.read_db.get_session() as session:
                # Determine order column based on order parameter
                if order == "random":
                    # Randomly choose between ascending and descending order
                    order_column = (
                        AlarmGroup.start_time_utc.desc()
                        if random.choice([True, False])
                        else AlarmGroup.start_time_utc
                    )
                elif order == "asc":
                    order_column = AlarmGroup.start_time_utc
                else:  # default to desc
                    order_column = AlarmGroup.start_time_utc.desc()

                query = (
                    session.query(AlarmGroup)
                    .filter(
                        or_(
                            AlarmGroup.operator_id.is_(None),
                            AlarmGroup.operator_id == "orphan",
                        ),
                        AlarmGroup.state.in_(
                            [
                                AlarmGroupState.PENDING,
                                AlarmGroupState.IN_PROGRESS,
                                AlarmGroupState.PENDING_CUTOFF,
                            ]
                        ),
                        AlarmGroup.tenant_id.in_(tenant_ids),
                    )
                    .order_by(order_column)
                    .limit(limit)
                )

                alarm_groups = query.all()
                camera_group_ids = [
                    (
                        alarm_group.camera_group_id,
                        alarm_group.start_time_utc,
                    )
                    for alarm_group in alarm_groups
                ]
                return camera_group_ids
        except Exception as e:
            logger.error(
                "error_getting_pending_alarm_groups_for_tenants",
                error=str(e),
                tenant_ids=tenant_ids,
                limit=limit,
                order=order,
                exc_info=True,
            )
            raise

    def get_alarm_groups(
        self,
        filters: AlarmGroupFilters,
    ) -> List[AlarmGroup]:
        """
        Returns list of AlarmGroup objects for the given filters,
        paginated and ordered by created_at_utc DESC.

        Args:
                filters: AlarmGroupFilters object containing all filter parameters

        Returns:
                List of AlarmGroup objects
        """
        with self.db.get_session() as session:  # type: Session
            qry = session.query(AlarmGroup)

            # build filters
            filt = []
            if filters.tenant_ids:
                filt.append(AlarmGroup.tenant_id.in_(filters.tenant_ids))
            if filters.camera_group_ids:
                filt.append(
                    AlarmGroup.camera_group_id.in_(filters.camera_group_ids)
                )
            if filters.states:
                filt.append(AlarmGroup.state.in_(filters.states))
            if filters.resolutions:
                filt.append(AlarmGroup.resolution.in_(filters.resolutions))
            if filters.date_from:
                filt.append(AlarmGroup.created_at_utc >= filters.date_from)
            if filters.date_to:
                filt.append(AlarmGroup.created_at_utc <= filters.date_to)
            if filt:
                qry = qry.filter(and_(*filt))

            # Sort and paginate
            qry = (
                qry.order_by(AlarmGroup.created_at_utc.desc())
                .offset(filters.page * filters.page_size)
                .limit(filters.page_size)
            )

            # Execute query
            results = qry.all()

            # Detach objects from session to prevent detached instance errors
            session.expunge_all()

            return results

    def get_event_counts_for_alarm_groups(
        self,
        alarm_group_ids: List[str],
    ) -> Dict[str, int]:
        """
        Get event counts for multiple alarm group IDs.

        Args:
                alarm_group_ids: List of alarm group IDs to get event counts for

        Returns:
                Dictionary mapping alarm_group_id to event_count
        """
        if not alarm_group_ids:
            return {}

        with self.db.get_session() as session:  # type: Session
            # Get event counts for the specific alarm groups
            event_counts = (
                session.query(
                    Event.alarm_group_id,
                    func.count(Event.id).label("event_count"),
                )
                .filter(Event.alarm_group_id.in_(alarm_group_ids))
                .group_by(Event.alarm_group_id)
                .all()
            )

            # Create a lookup dictionary for event counts
            event_count_lookup = {
                ag_id: count for ag_id, count in event_counts
            }

            # Ensure all requested alarm_group_ids are in the result (with count 0 if no events)
            result = {}
            for alarm_group_id in alarm_group_ids:
                result[alarm_group_id] = event_count_lookup.get(
                    alarm_group_id, 0
                )

            return result

    def get_alarm_group_by_id(
        self, alarm_group_id: str
    ) -> Optional[AlarmGroup]:
        try:
            db = self.read_db or self.db
            with db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(AlarmGroup.id == alarm_group_id)
                    .one_or_none()
                )
                session.expunge_all()
                return alarm_group
        except Exception as e:
            logger.error(
                "error_getting_alarm_group_by_id",
                error=str(e),
                alarm_group_id=alarm_group_id,
            )
            raise

    def get_alarm_group_events(
        self,
        alarm_group_id: str,
    ) -> List[Event]:
        """
        Get all events for a specific alarm group.

        Args:
                alarm_group_id: ID of the alarm group to get events for

        Returns:
                List of Event objects ordered by event_time_utc DESC
        """
        try:
            with self.db.get_session() as session:  # type: Session
                events = (
                    session.query(Event)
                    .filter(Event.alarm_group_id == alarm_group_id)
                    .order_by(Event.event_time_utc.desc())
                    .all()
                )

                # Detach objects from session to prevent detached instance errors
                session.expunge_all()

                return events
        except Exception as e:
            logger.error(
                "error_getting_alarm_group_events",
                error=str(e),
                alarm_group_id=alarm_group_id,
                exc_info=True,
            )
            raise

    def get_most_recent_alarm_group(
        self, camera_group_id: str, states: List[AlarmGroupState] = None
    ) -> Optional[str]:
        """
        Get the most recently created alarm group ID for a given camera_group_id and state(s)
        Args:
                camera_group_id: ID of the camera group
                states: List of states to filter by (optional, defaults to PENDING, PENDING_CUTOFF, IN_PROGRESS)
        Returns:
                Most recent alarm group ID if found, None otherwise
        """
        try:
            if states is None:
                states = [
                    AlarmGroupState.PENDING,
                    AlarmGroupState.PENDING_CUTOFF,
                    AlarmGroupState.IN_PROGRESS,
                ]

            with self.read_db.get_session() as session:
                query = (
                    select(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.camera_group_id == camera_group_id,
                            AlarmGroup.state.in_(states),
                        )
                    )
                    .order_by(AlarmGroup.start_time_utc.desc())
                )

                most_recent_alarm_group = (
                    session.execute(query).scalars().first()
                )

                if most_recent_alarm_group:
                    return most_recent_alarm_group.id
                return None

        except Exception as e:
            logger.error(
                "error_getting_most_recent_alarm_group",
                error=str(e),
                camera_group_id=camera_group_id,
                states=states,
                exc_info=True,
            )
            raise

    def get_unallocated_or_orphan_alarm_groups(
        self,
        tenant_ids: List[str],
        limit: int,
        order: str,
        recommendation: str,
    ) -> List[dict]:
        """
        Get unallocated or orphan alarm groups for a list of tenants
        Args:
                tenant_ids: List of tenant IDs to retrieve alarm groups for
                limit: Limit on the number of results to return
                order: Ordering for start_time_utc - 'asc', 'desc', or 'random'
                recommendation: Recommendation to filter by (optional, defaults to None)
        Returns:w
                List of dictionaries containing alarm_group_id, tenant_id, camera_group_id, and start_timestamp_utc
        """
        try:
            with self.read_db.get_session() as session:
                if order == "asc":
                    order_column = AlarmGroup.created_at_utc
                else:  # default to desc
                    order_column = AlarmGroup.created_at_utc.desc()

                # temp_alarm_groups = (
                #     session.query(AlarmGroup)
                #     .filter(
                #         or_(
                #             AlarmGroup.operator_id.is_(None),
                #             AlarmGroup.operator_id == "orphan",
                #         ),
                #         AlarmGroup.tenant_id.in_(tenant_ids),
                #         AlarmGroup.state.in_(
                #             [
                #                 AlarmGroupState.PENDING,
                #                 AlarmGroupState.PENDING_CUTOFF,
                #                 AlarmGroupState.IN_PROGRESS,
                #             ]
                #         ),
                #     )
                #     .order_by(order_column)
                #     .limit(limit)
                # )
                # # Apply recommendation filter if provided
                # if recommendation:
                #     temp_alarm_groups = temp_alarm_groups.filter(
                #         AlarmGroup.recommendation == recommendation
                #     )
                # else:
                #     temp_alarm_groups = temp_alarm_groups.filter(
                #         AlarmGroup.recommendation.in_(
                #             [
                #                 AlarmGroupRecommendation.ANALYZING,
                #                 AlarmGroupRecommendation.RESOLVE,
                #                 AlarmGroupRecommendation.ESCALATE,
                #                 AlarmGroupRecommendation.ESCALATE_ZERO_TOLERANCE,
                #                 AlarmGroupRecommendation.ERROR,
                #             ]
                #         )
                #     )
                recommendation_values = (
                    [recommendation]
                    if recommendation
                    else [
                        AlarmGroupRecommendation.ANALYZING,
                        AlarmGroupRecommendation.RESOLVE,
                        AlarmGroupRecommendation.ESCALATE,
                        AlarmGroupRecommendation.ESCALATE_ZERO_TOLERANCE,
                        AlarmGroupRecommendation.ERROR,
                    ]
                )

                temp_alarm_groups = (
                    session.query(AlarmGroup)
                    .filter(
                        or_(
                            AlarmGroup.operator_id.is_(None),
                            AlarmGroup.operator_id == "orphan",
                        ),
                        AlarmGroup.tenant_id.in_(tenant_ids),
                        AlarmGroup.state.in_(
                            [
                                AlarmGroupState.PENDING,
                                AlarmGroupState.PENDING_CUTOFF,
                                AlarmGroupState.IN_PROGRESS,
                            ]
                        ),
                        AlarmGroup.recommendation.in_(recommendation_values),
                        AlarmGroup.alarm_group_queue_state
                        == AlarmGroupQueueState.PENDING,
                    )
                    .order_by(order_column)
                    .limit(limit)
                )

                alarm_groups = []
                for ag in temp_alarm_groups:
                    auto_enabled = self.is_auto_resolution_enabled(
                        ag.tenant_id
                    )
                    if auto_enabled:
                        if ag.recommendation in [
                            AlarmGroupRecommendation.ESCALATE,
                            AlarmGroupRecommendation.ESCALATE_ZERO_TOLERANCE,
                        ]:
                            alarm_groups.append(ag)
                    else:
                        alarm_groups.append(ag)

                # Format the return data and ensure proper sorting
                result = []
                for alarm_group in alarm_groups:
                    result.append(
                        {
                            "alarm_group_id": alarm_group.id,
                            "tenant_id": alarm_group.tenant_id,
                            "camera_group_id": alarm_group.camera_group_id,
                            "start_timestamp_utc": alarm_group.created_at_utc,
                            "recommendation": alarm_group.recommendation,
                        }
                    )

                # Sort the result by start_timestamp_utc to ensure proper ordering
                result.sort(
                    key=lambda x: x["start_timestamp_utc"],
                    reverse=(order != "asc"),
                )

                return result

        except Exception as e:
            logger.error(
                "error_getting_unallocated_or_orphan_alarm_groups",
                error=str(e),
                tenant_ids=tenant_ids,
                limit=limit,
                order=order,
                recommendation=recommendation,
                exc_info=True,
            )
            raise

    def get_unallocated_alarm_groups_for_operator_camera_groups(
        self, operator_id: str
    ) -> List[dict]:
        """
        Get unallocated alarm group event data for camera groups that have active alarms assigned to the specified operator.

        This function finds camera groups that have alarm groups allocated to the given operator
        (in PENDING, PENDING_CUTOFF, or IN_PROGRESS states), then returns alarm group event data
        from those same camera groups that are unallocated (operator_id is None or 'orphan').

        Args:
                operator_id: ID of the operator to check for allocated camera groups

        Returns:
                List of event_data dictionaries that are unallocated but belong to camera groups
                that have active alarms assigned to the specified operator
        """
        try:
            with self.read_db.get_session() as session:
                # Subquery to find camera_group_ids that have active alarms for the specified operator
                active_camera_groups_subquery = (
                    session.query(AlarmGroup.camera_group_id)
                    .filter(
                        AlarmGroup.operator_id == operator_id,
                        AlarmGroup.state.in_(
                            [
                                AlarmGroupState.PENDING,
                                AlarmGroupState.PENDING_CUTOFF,
                                AlarmGroupState.IN_PROGRESS,
                            ]
                        ),
                    )
                    .distinct()
                    .subquery()
                )

                # Main query to find unallocated alarm groups in those camera groups
                query = session.query(AlarmGroup).filter(
                    or_(
                        AlarmGroup.operator_id.is_(None),
                        AlarmGroup.operator_id == "orphan",
                    ),
                    AlarmGroup.state.in_(
                        [
                            AlarmGroupState.PENDING,
                            AlarmGroupState.PENDING_CUTOFF,
                            AlarmGroupState.IN_PROGRESS,
                        ]
                    ),
                    AlarmGroup.camera_group_id.in_(
                        session.query(
                            active_camera_groups_subquery.c.camera_group_id
                        )
                    ),
                )

                alarm_groups = query.all()
                event_datas = []
                for alarm_group in alarm_groups:
                    event_data_high = {
                        "alarm_group_id": alarm_group.id,
                        "tenant_id": alarm_group.tenant_id,
                        "camera_group_id": alarm_group.camera_group_id,
                        "severity": Severity.HIGH,
                    }
                    event_data_low = {
                        "alarm_group_id": alarm_group.id,
                        "tenant_id": alarm_group.tenant_id,
                        "camera_group_id": alarm_group.camera_group_id,
                        "severity": Severity.LOW,
                    }
                    event_datas.append(event_data_high)
                    event_datas.append(event_data_low)
                return event_datas

        except Exception as e:
            logger.error(
                "error_getting_unallocated_alarm_groups_for_operator_camera_groups",
                error=str(e),
                operator_id=operator_id,
                exc_info=True,
            )
            raise

    # def get_total_pending_alarm_group_count(
    #     self, tenant_ids: List[str]
    # ) -> int:
    #     try:
    #         with self.read_db.get_session() as session:
    #             count = (
    #                 session.query(AlarmGroup)
    #                 .filter(
    #                     AlarmGroup.tenant_id.in_(tenant_ids),
    #                     AlarmGroup.state.in_(
    #                         [
    #                             AlarmGroupState.PENDING,
    #                             AlarmGroupState.PENDING_CUTOFF,
    #                             AlarmGroupState.IN_PROGRESS,
    #                         ]
    #                     ),
    #                 )
    #                 .count()
    #             )
    #             return count
    #     except Exception as e:
    #         logger.error(
    #             "error_getting_total_open_count",
    #             error=str(e),
    #             tenant_ids=tenant_ids,
    #             exc_info=True,
    #         )
    #         raise

    # def get_total_escalation_open_count(self, tenant_ids: List[str]) -> int:
    #     try:
    #         with self.read_db.get_session() as session:
    #             count = (
    #                 session.query(AlarmGroup)
    #                 .filter(
    #                     AlarmGroup.tenant_id.in_(tenant_ids),
    #                     AlarmGroup.state.in_([AlarmGroupState.IN_PROGRESS]),
    #                 )
    #                 .count()
    #             )
    #             return count
    #     except Exception as e:
    #         logger.error(
    #             "error_getting_total_escalation_open_count",
    #             error=str(e),
    #             tenant_ids=tenant_ids,
    #             exc_info=True,
    #         )
    #         raise

    # def get_total_operator_allocation_count(
    #     self,
    #     operator_id: str,
    # ) -> int:
    #     try:
    #         with self.read_db.get_session() as session:
    #             count = (
    #                 session.query(AlarmGroup)
    #                 .filter(
    #                     AlarmGroup.operator_id == operator_id,
    #                     AlarmGroup.state.in_(
    #                         [
    #                             AlarmGroupState.PENDING,
    #                             AlarmGroupState.PENDING_CUTOFF,
    #                             AlarmGroupState.IN_PROGRESS,
    #                         ]
    #                     ),
    #                 )
    #                 .count()
    #             )
    #             return count
    #     except Exception as e:
    #         logger.error(
    #             "error_getting_total_operator_allocation_count",
    #             error=str(e),
    #             operator_id=operator_id,
    #             exc_info=True,
    #         )
    #         raise

    # def get_total_unallocation_count(
    #     self, operator_id: str, tenant_ids: List[str]
    # ) -> int:
    #     try:
    #         with self.read_db.get_session() as session:
    #             count = (
    #                 session.query(AlarmGroup)
    #                 .filter(
    #                     or_(
    #                         AlarmGroup.operator_id.is_(None),
    #                         AlarmGroup.operator_id == "orphan",
    #                     ),
    #                     AlarmGroup.tenant_id.in_(tenant_ids),
    #                     AlarmGroup.state.in_(
    #                         [
    #                             AlarmGroupState.PENDING,
    #                             AlarmGroupState.PENDING_CUTOFF,
    #                             AlarmGroupState.IN_PROGRESS,
    #                         ]
    #                     ),
    #                 )
    #                 .count()
    #             )
    #             return count
    #     except Exception as e:
    #         logger.error(
    #             "error_getting_total_operator_allocation_count",
    #             error=str(e),
    #             operator_id=operator_id,
    #             exc_info=True,
    #         )
    #         raise

    def get_total_pending_alarm_group_count(
        self, tenant_ids: List[str]
    ) -> int:
        try:
            with self.read_db.get_session() as session:
                temp_alarm_groups = session.query(AlarmGroup).filter(
                    AlarmGroup.tenant_id.in_(tenant_ids),
                    AlarmGroup.state.in_(
                        [
                            AlarmGroupState.PENDING,
                            AlarmGroupState.PENDING_CUTOFF,
                            AlarmGroupState.IN_PROGRESS,
                        ]
                    ),
                )

                count = 0
                for ag in temp_alarm_groups:
                    auto_enabled = self.is_auto_resolution_enabled(
                        ag.tenant_id
                    )
                    if auto_enabled:
                        if ag.recommendation in [
                            AlarmGroupRecommendation.ESCALATE,
                            AlarmGroupRecommendation.ESCALATE_ZERO_TOLERANCE,
                        ]:
                            count += 1
                    else:
                        count += 1

                return count
        except Exception as e:
            logger.error(
                "error_getting_total_open_count",
                error=str(e),
                tenant_ids=tenant_ids,
                exc_info=True,
            )
            raise

    def get_total_escalation_open_count(self, tenant_ids: List[str]) -> int:
        try:
            with self.read_db.get_session() as session:
                count = (
                    session.query(AlarmGroup)
                    .filter(
                        AlarmGroup.tenant_id.in_(tenant_ids),
                        AlarmGroup.resolution.in_(
                            [Resolution.ESCALATION_OPEN]
                        ),
                    )
                    .count()
                )
                return count
        except Exception as e:
            logger.error(
                "error_getting_total_escalation_open_count",
                error=str(e),
                tenant_ids=tenant_ids,
                exc_info=True,
            )
            raise

    def get_total_operator_allocation_count(
        self,
        operator_id: str,
    ) -> int:
        try:
            with self.read_db.get_session() as session:
                temp_alarm_groups = session.query(AlarmGroup).filter(
                    AlarmGroup.operator_id == operator_id,
                    AlarmGroup.state.in_(
                        [
                            AlarmGroupState.PENDING,
                            AlarmGroupState.PENDING_CUTOFF,
                            AlarmGroupState.IN_PROGRESS,
                        ]
                    ),
                )

                count = 0
                for ag in temp_alarm_groups:
                    auto_enabled = self.is_auto_resolution_enabled(
                        ag.tenant_id
                    )
                    if auto_enabled:
                        if ag.recommendation in [
                            AlarmGroupRecommendation.ESCALATE,
                            AlarmGroupRecommendation.ESCALATE_ZERO_TOLERANCE,
                        ]:
                            count += 1
                    else:
                        count += 1

        except Exception as e:
            logger.error(
                "error_getting_total_operator_allocation_count",
                error=str(e),
                operator_id=operator_id,
                exc_info=True,
            )
            raise

    def get_total_unallocation_count(
        self, operator_id: str, tenant_ids: List[str]
    ) -> int:
        try:
            with self.read_db.get_session() as session:
                temp_alarm_groups = session.query(AlarmGroup).filter(
                    AlarmGroup.tenant_id.in_(tenant_ids),
                    or_(
                        AlarmGroup.operator_id.is_(None),
                        AlarmGroup.operator_id == "orphan",
                    ),
                    AlarmGroup.state.in_(
                        [
                            AlarmGroupState.PENDING,
                            AlarmGroupState.PENDING_CUTOFF,
                            AlarmGroupState.IN_PROGRESS,
                        ]
                    ),
                )

                count = 0
                for ag in temp_alarm_groups:
                    auto_enabled = self.is_auto_resolution_enabled(
                        ag.tenant_id
                    )
                    if auto_enabled:
                        if ag.recommendation in [
                            AlarmGroupRecommendation.ESCALATE,
                            AlarmGroupRecommendation.ESCALATE_ZERO_TOLERANCE,
                        ]:
                            count += 1
                    else:
                        count += 1

                return count
        except Exception as e:
            logger.error(
                "error_getting_total_operator_allocation_count",
                error=str(e),
                operator_id=operator_id,
                exc_info=True,
            )
            raise
