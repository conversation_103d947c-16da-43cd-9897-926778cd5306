from typing import List, Optional, <PERSON><PERSON>

import structlog

from db_controller.interface import ControllerBase
from models_rds.event import Event
from vision.services.src.event_processor.models.events import (
    EventRecommendation,
    EventState,
    Severity,
)

logger = structlog.get_logger("hakimo", module="EventController")


class EventController(ControllerBase):
    def create_event(self, event: Event) -> Tuple[str, Event]:
        try:
            with self.db.get_session() as session:
                session.add(event)
                session.flush()
                session.expunge(event)
                return event.id, event
        except Exception as e:
            logger.error("error_creating_event", error=str(e), exc_info=True)
            raise

    def get_event(self, event_id: str) -> Optional[Event]:
        """
        Get an event by ID
        Args:
            event_id: ID of the event to retrieve
        Returns:
            Event ID if found, None otherwise
        """
        try:
            db = self.read_db or self.db
            with db.get_session() as session:
                event = (
                    session.query(Event)
                    .filter(Event.id == event_id)
                    .one_or_none()
                )
                session.expunge_all()
                return event

        except Exception as e:
            logger.error(
                "error_getting_event",
                error=str(e),
                event_id=event_id,
                exc_info=True,
            )
            raise

    def get_all_events_for_alarm_group(
        self,
        alarm_group_id: str,
        limit: Optional[int],
        order_by: Optional[str] = None,
    ) -> List[Event]:
        """
        Get an event by ID
        Args:
            alarm_group_id: ID of the alarm group to retrieve events for
            limit: Limit the number of events to retrieve
            order_by: can be None or 'asc' or 'desc'
        Returns:
            List of Event objects with the alarm_group_id
        """
        try:
            db = self.read_db or self.db
            with db.get_session() as session:
                query = session.query(Event).filter(
                    Event.alarm_group_id == alarm_group_id
                )
                if order_by is not None:
                    if order_by == "asc":
                        query = query.order_by(Event.event_time_utc.asc())
                    elif order_by == "desc":
                        query = query.order_by(Event.event_time_utc.desc())
                    else:
                        logger.error(
                            "Invalid order_by in get_all_events_for_alarm_group. Not using any order",
                            order_by=order_by,
                            alarm_group_id=alarm_group_id,
                        )
                if limit is not None:
                    query = query.limit(limit)
                events = query.all()
                session.expunge_all()
                return events

        except Exception as e:
            logger.error(
                "error_getting_event",
                error=str(e),
                alarm_group_id=alarm_group_id,
                exc_info=True,
            )
            raise

    def update_event_state_severity_recommendation(
        self,
        event_id: str,
        state: EventState,
        recommendation: EventRecommendation,
        severity: Severity,
    ):
        try:
            with self.db.get_session() as session:
                session.query(Event).filter(Event.id == event_id).update(
                    {
                        "state": state,
                        "recommendation": recommendation,
                        "severity": severity,
                    }
                )
                session.commit()
        except Exception as e:
            logger.error(
                "error_updating_event_state_and_recommendation",
                error=str(e),
                event_id=event_id,
                state=state,
                recommendation=recommendation,
                exc_info=True,
            )
            raise
