from typing import Optional

import structlog
from cachetools import TTLCache, cachedmethod
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from db_controller.interface import ControllerBase
from models_rds.users import Users

log = structlog.get_logger("hakimo", module="UserController")


class UserController(ControllerBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Create a TTL cache that expires after 10 minutes (600 seconds)
        self._user_cache = TTLCache(maxsize=512, ttl=600)

    def user_by_id(self, user_uuid: str) -> Optional[Users]:
        """Get the user object given the uuid."""
        with self.db.get_session() as sess:
            a = (
                sess.execute(select(Users).filter(Users.uuid == user_uuid))
                .scalars()
                .first()
            )
            sess.expunge_all()
            return a

    @cachedmethod(lambda self: self._user_cache)
    def user_by_email(self, user_email: str) -> Optional[Users]:
        """Get the user object given the email."""
        with self.db.get_session() as sess:
            query = (
                select(Users)
                .filter(Users.email == user_email)
                .options(joinedload(Users.role))
            )
            a = sess.execute(query).scalars().first()
            sess.expunge_all()
            return a
