import typing

from cachetools import TTL<PERSON>ache, cachedmethod

from db_controller.interface import ControllerBase
from models_rds.ai_outputs_location_alarms import AIOutputsLocationAlarms
from models_rds.ai_outputs_raw_alarms import AIOutputsRawAlarms


class AIOutputsController(ControllerBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Create a TTL cache that expires after 15 minutes (900 seconds)
        self._ai_output_cache = TTLCache(maxsize=1024, ttl=900)

    def add_ai_output_for_event(
        self,
        event_id: str,
        tenant_id: str,
        analysis: typing.Optional[str],
        recommendation: typing.Optional[str],
    ) -> int:
        with self.db.get_session() as sess:
            ao = AIOutputsRawAlarms(
                raw_alarm_id=event_id,
                tenant_id=tenant_id,
                analysis=analysis,
                recommendation=recommendation,
            )
            sess.add(ao)
            sess.commit()
            sess.refresh(ao)
            return ao.int_id

    @cachedmethod(lambda self: self._ai_output_cache)
    def get_latest_ai_output_for_event(
        self, event_id: str
    ) -> typing.Optional[AIOutputsRawAlarms]:
        db = self.read_db or self.db
        with db.get_session() as sess:
            ao = (
                sess.query(AIOutputsRawAlarms)
                .filter(AIOutputsRawAlarms.raw_alarm_id == event_id)
                .order_by(AIOutputsRawAlarms.created_at_utc.desc())
                .first()
            )
            sess.expunge_all()
            return ao

    def add_or_update_ai_output_for_alarm_group(
        self,
        alarm_group_id: str,
        tenant_id: str,
        summary: typing.Optional[str],
        recommendation: typing.Optional[str],
        explanation: typing.Optional[str],
    ) -> int:
        with self.db.get_session() as sess:
            ao = (
                sess.query(AIOutputsLocationAlarms)
                .filter(
                    AIOutputsLocationAlarms.alarm_group_id == alarm_group_id
                )
                .first()
            )
            if ao is None:
                ao = AIOutputsLocationAlarms(
                    alarm_group_id=alarm_group_id,
                    tenant_id=tenant_id,
                    summary=summary,
                    recommendation=recommendation,
                    explanation=explanation,
                )
                sess.add(ao)
            else:
                ao.summary = summary
                ao.recommendation = recommendation
                ao.explanation = explanation
            sess.commit()
            sess.refresh(ao)
            return ao.int_id

    def get_latest_ai_output_for_alarm_group_id(
        self, alarm_group_id: str
    ) -> typing.Optional[AIOutputsLocationAlarms]:
        with self.read_db.get_session() as sess:
            ao = (
                sess.query(AIOutputsLocationAlarms)
                .filter(
                    AIOutputsLocationAlarms.alarm_group_id == alarm_group_id
                )
                .first()
            )
            sess.expunge_all()
            return ao
