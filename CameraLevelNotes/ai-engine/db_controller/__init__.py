import typing

from database.db_adapter import DBAdapter
from db_controller.ai_outputs.ai_outputs_controller import AIOutputsController
from db_controller.alarm_group.alarm_group_controller import (
    AlarmGroupController,
)
from db_controller.alarm_group_update.alarm_group_update_controller import (
    AlarmGroupUpdateController,
)
from db_controller.camera.camera_controller import CameraController
from db_controller.event.event_controller import EventController
from db_controller.locations.locations_controller import LocationsController
from db_controller.role.role_controller import RoleController
from db_controller.sop.sop_controller import SOPController
from db_controller.tenant.tenant_controller import TenantController
from db_controller.user.user_controller import UserController


class DBControllerV1:
    def __init__(
        self,
        db_adapter: DBAdapter,
        read_db_adapter: typing.Optional[DBAdapter] = None,
    ):
        if read_db_adapter is None:
            read_db_adapter = db_adapter
        self._ai_outputs = AIOutputsController(db_adapter, read_db_adapter)
        self._alarm_group = AlarmGroupController(db_adapter, read_db_adapter)
        self._alarm_group_update = AlarmGroupUpdateController(
            db_adapter, read_db_adapter
        )
        self._camera = CameraController(db_adapter, read_db_adapter)
        self._event = EventController(db_adapter, read_db_adapter)
        self._locations = LocationsController(db_adapter, read_db_adapter)
        self._sop = SOPController(db_adapter, read_db_adapter)
        self._tenant = TenantController(db_adapter, read_db_adapter)
        self._user = UserController(db_adapter, read_db_adapter)
        self._role = RoleController(db_adapter, read_db_adapter)

    @property
    def ai_outputs(self) -> AIOutputsController:
        return self._ai_outputs

    @property
    def alarm_group(self) -> AlarmGroupController:
        return self._alarm_group

    @property
    def event(self) -> EventController:
        return self._event

    @property
    def alarm_group_update(self) -> AlarmGroupUpdateController:
        return self._alarm_group_update

    @property
    def camera(self) -> CameraController:
        return self._camera

    @property
    def locations(self) -> LocationsController:
        return self._locations

    @property
    def sop(self) -> SOPController:
        return self._sop

    @property
    def tenant(self) -> TenantController:
        return self._tenant
