pretrained_data/yolo/*.weights
*.DS_Store
*.pyc
.DS_Store
.idea/*
ml_module/pretrained_data/checkpoints/*
data/*
out.mp4
ml_module/yolo/yolov3_tf2/__pycache__/*
output/
input/
ml_module/pretrained_data/**/*.weights
.idea/
*.pkl
*.pickle
/venv
/env
/vision_processor_new
*.pytest_cache
*.mypy_cache
*.ruff_cache
.coverage
.vscode
events.out.tfevents*
lightning_logs/
*.ipynb_checkpoints/
*.swp
*.ipynb
coverage_html_report/
*.bin
.vs/
*.dll
*.exe
*.pth
ml_module/benchmark/scripts/wandb/*

# Integration's gitignore file
integ/genetec/hakimo-genetec-sdk/genetec/[Bb]in/
integ/genetec/hakimo-genetec-sdk/genetec/[Oo]bj/
integ/genetec/hakimo-genetec-sdk/genetec/[Ll]og/
integ/genetec/hakimo-genetec-sdk/genetec/[Ll]ogs/
integ/genetec/hakimo-genetec-sdk/**/[Pp]ackages/*
integ/genetec/hakimo-genetec-sdk/supervisor/*.spec
integ/common/vm_supervisor/[Bb]uild/
integ/common/vm_supervisor/[Dd]ist/
integ/common/vm_supervisor/[Vv]env/

integ/milestone/MilestoneSdk/MilestoneSdk/[Bb]in/
integ/milestone/MilestoneSdk/MilestoneSdk/[Oo]bj/
integ/milestone/MilestoneSdk/MilestoneSdk/[Ll]og/
integ/milestone/MilestoneSdk/MilestoneSdk/[Ll]ogs/
integ/milestone/MilestoneSdk/**/[Pp]ackages/*

integ/velocity/velocity-sdk/[Bb]in/
integ/velocity/velocity-sdk/[Oo]bj/
integ/velocity/velocity-sdk/[Ll]og/
integ/velocity/velocity-sdk/[Ll]ogs/
integ/velocity/velocity-sdk/[Vv]elocity-sdk-build/
integ/velocity/velocity-sdk/[Vv]elocity-sdk-publish/
integ/velocity/velocity-sdk/[Pp]roperties/

ansible/auto
vision_processor_new
vision_processor_p_3_8
venv_vision_etl

integ/prowatch/TestClientEventService/[Bb]in/
integ/prowatch/TestClientEventService/[Oo]bj/
integ/prowatch/TestClientEventService/[Ll]og/
integ/prowatch/TestClientEventService/[Ll]ogs/
integ/prowatch/TestClientEventService/[Dd]ebug/
integ/prowatch/TestClientEventService/**/[Pp]ackages/*

