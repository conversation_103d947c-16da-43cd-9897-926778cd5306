# This workflow does the following:
# 1. Run testing
# 2. Build latest docker once tests pass
name: Go workflow
on:
  pull_request:
    branches:
      - master
  push:
    branches:
      - master

jobs:
  build:
    runs-on: [blacksmith-2vcpu-ubuntu-2204]
    timeout-minutes: 10
    strategy:
      matrix:
        dir: ["gateway/http_api/control_plane/log-server"]
    steps:
      # See here:
      # https://github.com/actions/checkout/issues/760
      - name: work around permission issue
        run: git config --global --add safe.directory "$GITHUB_WORKSPACE"
      - uses: actions/checkout@v3
        with:
          fetch-depth: 1
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Extract branch name
        shell: bash
        run: |
          branch=$(bash ci_scripts/get-github-branch.sh | tail -n 1)
          echo "branch=$branch" >> $GITHUB_OUTPUT
        id: extract-branch
      - uses: WillAbides/setup-go-faster@v1.8.0
        with:
          go-version: "1.18.1"
      - uses: dominikh/staticcheck-action@v1.3.0
        with:
          version: "2022.1.1"
          install-go: false
          working-directory: ${{ matrix.dir }}
      - name: test-log-server
        run: |
          bash ci_scripts/run-go-tests.sh

      - name: Build Docker
        id: build-log-server
        env:
          BRANCH: ${{ steps.extract-branch.outputs.branch }}
        run: |
          echo "Building log-server"
          DOCKER_IMG=$(bash ci_scripts/build-docker-log-server.sh $BRANCH | tail -n 1)
          echo "DOCKER=$DOCKER_IMG" >> $GITHUB_ENV
