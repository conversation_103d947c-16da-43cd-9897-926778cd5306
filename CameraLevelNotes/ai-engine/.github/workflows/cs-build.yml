# This workflow will build and push a new container image to Amazon ECR,
# and also deploy the built docker to the staging k8s cluster
#
# 1. Create an ECR repository to store your images.
#    For example: `aws ecr create-repository --repository-name my-ecr-repo --region us-east-2`.
#    Replace the value of `ECR_REPOSITORY` in the workflow below with your repository's name.
#    Replace the value of `aws-region` in the workflow below with your repository's region.
#
# 2. Store an IAM user access key in GitHub Actions secrets named `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY`.
#    See the documentation for each action used below for the recommended IAM policies for this IAM user,
#    and best practices on handling the access key credentials.

on:
  pull_request:
    branches:
      - master

  push:
    branches:
      - "master"
  release:
    types:
      - "released"
name: Build C# Dockers
concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true
jobs:
  build-deploy-cs:
    name: Build and Upload C#
    runs-on: [self-hosted, windows]
    timeout-minutes: 30

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Build Genetec
        shell: powershell
        run: |
          .\integ\genetec\hakimo-genetec-sdk\genetec\ci\build-sdk.ps1

      - name: Build Velocity
        shell: powershell
        run: |
          .\integ\velocity\velocity-sdk\ci\build-sdk.ps1

      - name: Build Milestone
        shell: powershell
        run: |
          .\integ\milestone\hakimo_milestone_sdk\MilestoneSdk\ci\build-sdk.ps1

      - name: Archive genetec production artifacts
        uses: actions/upload-artifact@v4
        with:
          name: cs-build-genetec
          retention-days: 1
          path: |
            .\sdk-publish

      - name: Archive milestone production artifacts
        uses: actions/upload-artifact@v4
        with:
          name: cs-build-milestone
          retention-days: 1
          path: |
            .\milestone-sdk-publish

      - name: Archive velocity production artifacts
        uses: actions/upload-artifact@v4
        with:
          name: cs-build-velocity
          retention-days: 1
          path: |
            .\velocity-sdk-publish
  build-win-binaries:
    name: Build and Upload Windows Binaries
    runs-on: [self-hosted, windows]
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1
      - uses: actions/setup-python@v4
        id: py-setup
        with:
          cache: "pip"
          python-version: "3.8"
      - name: Install Requirements
        env:
          PYTHON_LOC: ${{ steps.py-setup.python-path }}
        run: |
          set PATH=%PATH%;$Env:PYTHON_LOC
          python -m pip install --upgrade -r `
          .\integ\common\vm_supervisor\requirements.txt
      - name: Build Python Binary
        shell: powershell
        run: |
          New-Item -Path .\supervisor-bin -ItemType Directory
          pyinstaller --onefile `
          .\integ\common\vm_supervisor\api.py `
          --distpath .\supervisor-bin
      - name: Archive production artifacts
        uses: actions/upload-artifact@v4
        with:
          name: win-build-supervisor
          retention-days: 1
          path: |
            .\supervisor-bin
  build-docker:
    name: Build C# dockers
    runs-on: [self-hosted, linux]
    needs:
      - build-deploy-cs
      - build-win-binaries
    steps:
      # See here:
      # https://github.com/actions/checkout/issues/760
      - name: work around permission issue
        run: git config --global --add safe.directory "$GITHUB_WORKSPACE"
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      # Download the genetec build artifacts to be added to the docker image
      - name: Download Genetec Build
        uses: actions/download-artifact@v4
        with:
          path: ./built-artifacts/cs-build-genetec
          name: cs-build-genetec

      # Download the milestone build artifacts to be added to the docker image
      - name: Download Milestone Build
        uses: actions/download-artifact@v4
        with:
          path: ./built-artifacts/cs-build-milestone
          name: cs-build-milestone

      # Download the velocity build artifacts to be added to the docker image
      - name: Download Velocity Build
        uses: actions/download-artifact@v4
        with:
          path: ./built-artifacts/cs-build-velocity
          name: cs-build-velocity

      # Download the windows supervisor build artifacts to be added to the docker image
      - name: Download Win-Supervisor Build
        uses: actions/download-artifact@v4
        with:
          path: ./built-artifacts/win-build-supervisor
          name: win-build-supervisor

      - name: Get release
        continue-on-error: true
        id: get_release
        uses: bruceadams/get-release@v1.3.2
        env:
          GITHUB_TOKEN: ${{ github.token }}
      - name: Set Docker Tag
        id: set-tag
        run: |
          if [ -n ${{ steps.get_release.outputs.tag_name }} ];
          then
            echo "TAG=${{ steps.get_release.outputs.tag_name }}" >> $GITHUB_ENV
          fi

      - name: Build and Push C# Dockers
        id: build-cs
        run: |
          echo "Building C# Dockers"
          if [ "$(ls ./built-artifacts | wc -l)" -ne $NUM_ARTIFACTS ]; then
            echo "Build artifacts not produced"
            exit -1
          fi
          bash ci_scripts/build-docker-k8s-supervisor.sh ${{env.TAG}}
