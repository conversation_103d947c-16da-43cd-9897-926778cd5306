name: Access control system integration tests
on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

jobs:
  run_tests:
    runs-on: [self-hosted, linux]
    timeout-minutes: 60
    steps:
      # See here:
      # https://github.com/actions/checkout/issues/760
      - name: work around permission issue
        run: git config --global --add safe.directory "$GITHUB_WORKSPACE"

      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      
      - name: Extract branch name
        shell: bash
        run: |
          branch=$(bash ci_scripts/get-github-branch.sh | tail -n 1)
          echo "branch=$branch" >> $GITHUB_OUTPUT
        id: extract-branch

      - name: Build Docker
        id: build-docker
        env:
          BRANCH: ${{ steps.extract-branch.outputs.branch }}
        run: |
          echo "Building Docker"
          DOCKER_IMG=$(bash ci_scripts/build-docker.sh $BRANCH | tail -n 1)
          echo "DOCKER=$DOCKER_IMG" >> $GITHUB_ENV

      - name: Run Lenel tests
        run: |
          docker run -e RUN_INTEGRATION_TESTS=true -e LENEL_OA_USERNAME=${{ secrets.LENEL_OA_USERNAME }} \
          -e LENEL_OA_PASSWORD=${{ secrets.LENEL_OA_PASSWORD }} -i ${{ env.DOCKER }} \
          python -m gevent.monkey --module pytest -s integ/lenel/tests/test_openaccess.py
      
      - name: Run S2 tests
        run: |
          docker run -e RUN_INTEGRATION_TESTS=true -e S2_PASSWORD=${{ secrets.S2_PASSWORD }} \
          -e S2_DB_PASSWORD=${{ secrets.S2_DB_PASSWORD }} -i ${{ env.DOCKER }} \
          pytest integ/s2/test/test_s2_api.py integ/s2/test/test_s2_web_api.py integ/s2/test/test_s2_login.py \
          integ/s2/test/test_s2_web_login.py integ/s2/test/test_s2_database_client.py
      
      - name: Notify on Slack
        uses: ravsamhq/notify-slack-action@2.3.0
        if: always()
        with:
          status: ${{ job.status }}
          notify_when: "failure,cancelled"
          notification_title: "{workflow} is failing"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

