# This workflow does the following:
# 1. Checks if requirements files and Dockerfiles have changed (compared to master)
# 2. If they have, build a new docker and store it in the ECR ci-repo
# 3. If not, then use the latest built docker from master
# 4. Run linting, testing, and black in the selected docker
name: Python workflow
env:
  MODELS_TAG_FILE: "helm/ai-engine/values.yaml"
on:
  pull_request:
    branches:
      - master
    paths-ignore:
      - 'vision/**'
      - 'Dockerfile.vision.*'
  push:
    branches:
      - master
    paths-ignore:
      - 'vision/**'
      - 'Dockerfile.vision.*'
concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true
jobs:
  build:
    runs-on: [self-hosted, linux]
    timeout-minutes: 120

    steps:
      # See here:
      # https://github.com/actions/checkout/issues/760
      - name: work around permission issue
        run: git config --global --add safe.directory "$GITHUB_WORKSPACE"
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      
      # Add step to check if changes include vision module
      - name: Check for vision module changes
        id: check_changes
        run: |
          git fetch origin master
          CHANGED_FILES=$(git diff --name-only origin/master..HEAD)
          VISION_CHANGES=false
          
          for file in $CHANGED_FILES; do
            if [[ $file == vision/* || $file == Dockerfile.vision.* ]]; then
              VISION_CHANGES=true
              break
            fi
          done
          
          echo "vision_changes=$VISION_CHANGES" >> $GITHUB_OUTPUT
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Extract branch name
        shell: bash
        run: |
          branch=$(bash ci_scripts/get-github-branch.sh | tail -n 1)
          echo "branch=$branch" >> $GITHUB_OUTPUT
        id: extract-branch

      # - name: Check helm lint (appliance)
      #   id: helm-lint-appliance
      #   uses: koslib/helm-eks-action@v1.22.0
      #   env:
      #     KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_OMEGA }}
      #   with:
      #     command: bash ci_scripts/helm-lint-appliance.sh

      # - name: Check helm lint (cloud)
      #   id: helm-lint-cloud
      #   uses: koslib/helm-eks-action@v1.22.0
      #   env:
      #     KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_EKS_DELTA_2_STAGING }}
      #   with:
      #     command: bash ci_scripts/helm-lint-cloud.sh

      # - name: Cleanup helm lint
      #   id: cleanup-helm
      #   if: always()
      #   run: rm kubeconfig;
      #     echo ${{ steps.helm-lint.outputs.response }}
      - name: Build Docker
        id: build-ai-engine
        env:
          BRANCH: ${{ steps.extract-branch.outputs.branch }}
        run: |
          echo "Building Docker"
          DOCKER_IMG=$(bash ci_scripts/build-docker.sh $BRANCH | tail -n 1)
          echo "DOCKER=$DOCKER_IMG" >> $GITHUB_ENV

      - name: Check formatting with ruff
        run: |
          bash ci_scripts/format-ruff.sh ${{ env.DOCKER }}

      - name: Check for unused imports
        run: |
          bash ci_scripts/check-unused-imports.sh ${{ env.DOCKER }}

      - name: Check typing with mypy
        if: steps.check_changes.outputs.vision_changes != 'true'
        run: |
          bash ci_scripts/mypy-check.sh ${{ env.DOCKER }}

      - name: Analysing the code with pylint
        if: steps.check_changes.outputs.vision_changes != 'true'
        run: |
          bash ci_scripts/pylint.sh ${{ env.DOCKER }}
          
      - name: Test with pytest
        if: steps.check_changes.outputs.vision_changes != 'true'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          bash ci_scripts/unit-tests.sh ${{ env.DOCKER }}

  # eks-deploy:
  #   runs-on: [self-hosted, linux]
  #   needs: build
  #   if: ${{ github.ref_name == 'master' }}
  #   concurrency: ${{ github.ref_name }}

  #   steps:
  #     - name: Configure AWS credentials
  #       uses: aws-actions/configure-aws-credentials@v2
  #       with:
  #         aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
  #         aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  #         aws-region: us-west-2

  #     - uses: actions/checkout@v3
  #       with:
  #         fetch-depth: 1

  #     - name: Extract branch name
  #       shell: bash
  #       run: |
  #         branch=$(bash ci_scripts/get-github-branch.sh | tail -n 1)
  #         echo "branch=$branch" >> $GITHUB_OUTPUT
  #       id: extract-branch

  #     - name: Helm Deploy to Omega
  #       id: deploy-omega
  #       uses: koslib/helm-eks-action@v1.8.0
  #       env:
  #         KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_OMEGA }}
  #         ENV: integ
  #         BRANCH: ${{ steps.extract-branch.outputs.branch }}
  #       with:
  #         command: |
  #           git config --global --add safe.directory "$GITHUB_WORKSPACE"
  #           COMMIT_SHA=$(git rev-parse HEAD)
  #           TAG_PREFIX=$( [ $BRANCH == 'master' ] && echo "master" || echo "dev" )
  #           bash ci_scripts/deploy-appliance.sh $TAG_PREFIX-$COMMIT_SHA
