# This workflow asserts that a PR has labels assigned
name: Label Check

on:
  pull_request:
    types: [synchronize, edited, unlabeled, labeled]
    branches:
      - "master"
jobs:
  check-pr-label:
    runs-on: [blacksmith-2vcpu-ubuntu-2204]
    timeout-minutes: 5
    steps:
      - name: Get PR labels
        id: pr-labels
        uses: joerick/pr-labels-action@v1.0.6

      - name: Verify
        env:
          LABELS: ${{ steps.pr-labels.outputs.labels }}
        run: |
          if [[ -n $LABELS ]]; then
            exit 0;
          else
            exit 1;
          fi
