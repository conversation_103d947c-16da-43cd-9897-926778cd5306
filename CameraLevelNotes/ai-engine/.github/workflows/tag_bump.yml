# This workflow will build and push a new container image to Amazon ECR,
# and also deploy the built docker to the prod k8s cluster

name: New Hakimo Release
on:
  release:
    types:
      - "released"

jobs:
  deploy:
    name: Deploy Docker
    runs-on: [self-hosted, linux]
    timeout-minutes: 60

    steps:
      - name: work around permission issue
        # See here:
        # https://github.com/actions/checkout/issues/760
        run: git config --global --add safe.directory "$GITHUB_WORKSPACE"
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Get release
        id: get_release
        uses: bruceadams/get-release@v1.3.2
        env:
          GITHUB_TOKEN: ${{ github.token }}

      - name: Extract branch name
        shell: bash
        run: |
          branch=$(bash ci_scripts/get-github-branch.sh | tail -n 1)
          echo "branch=$branch" >> $GITHUB_OUTPUT
        id: extract-branch

      - name: Build and Push Docker
        id: build-ai-engine
        env:
          BRANCH: ${{ steps.extract-branch.outputs.branch }}
          TAG: ${{ steps.get_release.outputs.tag_name }}
        run: |
          echo "Building Docker"
          bash ci_scripts/build-docker.sh $BRANCH $TAG
# Backend ns has been deprecated. No more auto deployments to backend
#      - name: Deploy to Backend
#        id: deploy-backend
#        uses: koslib/helm-eks-action@v1.8.0
#        env:
#          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_EKS_DELTA_2 }}
#          TAG: ${{ steps.get_release.outputs.tag_name }}
#        with:
#          command: |
#            bash ci_scripts/deploy-ai-engine.sh preprod $TAG

  releaseHelmCharts:
    name: Release Helm Charts
    runs-on: [blacksmith-2vcpu-ubuntu-2204]
    timeout-minutes: 10

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - uses: actions/setup-python@v2
        name: Python setup
        id: python-setup
        with:
          python-version: "3.8"
          architecture: "x64"
      - name: Get release
        id: get_release
        uses: bruceadams/get-release@v1.3.2
        env:
          GITHUB_TOKEN: ${{ github.token }}
      - name: Modify chart versions
        id: helm-version-charts
        env:
          TAG: ${{ steps.get_release.outputs.tag_name }}
        run: |
          python -m pip install pyyaml
          python ci_scripts/chart_tag_bump.py $TAG
      - name: Package and Push release
        env:
          CLIENT_CERT: ${{ secrets.CI_CLIENT_CERT }}
          CLIENT_KEY: ${{ secrets.CI_CLIENT_KEY }}
          CA_BUNDLE: ${{ secrets.CI_CA_BUNDLE }}
          TAG: ${{ steps.get_release.outputs.tag_name }}
          GODEBUG: "x509ignoreCN=0"
        run: |
          bash ci_scripts/helm-push-repo.sh $TAG
