# This workflow does the following:
# 1. Runs clang-format on cpp and header files and checks if code
#   is properly formatted
name: CPP workflow
on:
  pull_request:
    branches:
      - master
  push:
    branches:
      - master

jobs:
  build:
    runs-on: [blacksmith-2vcpu-ubuntu-2204]
    timeout-minutes: 10
    steps:
      # See here:
      # https://github.com/actions/checkout/issues/760
      - name: work around permission issue
        run: git config --global --add safe.directory "$GITHUB_WORKSPACE"
      - uses: actions/checkout@v3
        with:
          fetch-depth: 1
      - name: Run clang-format style check for C/C++ programs.
        uses: jidicula/clang-format-action@v4.5.0
        with:
          clang-format-version: "13"
          fallback-style: "google" # optional
