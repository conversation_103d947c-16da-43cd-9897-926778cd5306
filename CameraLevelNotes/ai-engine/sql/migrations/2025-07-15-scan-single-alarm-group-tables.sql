CREATE TABLE `alarm_group_v1` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `severity` enum('high','low','not_known') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` enum('pending','in_progress','pending_cutoff','resolved_by_hakimo','resolved_by_operator') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `resolution` enum('open','safe','escalation_open','escalation_close') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `resolution_comment` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '',
  `start_time_utc` timestamp NULL DEFAULT NULL,
  `end_time_utc` timestamp NULL DEFAULT NULL,
  `camera_group_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `tenant_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `operator_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `is_active` int GENERATED ALWAYS AS ((case when (`state` = _utf8mb4'pending') then 1 else NULL end)) STORED,
  `created_at_utc` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at_utc` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `latest_event_time_utc` timestamp NULL DEFAULT NULL,
  `recommendation` enum('analyzing','resolve','escalate','escalate_zero_tolerance','error') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'analyzing',
  `alarm_group_queue_state` enum('pending','queued','assigned') NOT NULL DEFAULT 'pending',
  `alarm_group_queue_state_updated_at_utc` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `alarm_group_v1_unique` (`state`,`is_active`,`camera_group_id`),
  KEY `alarm_group_v1_camera_groups_FK` (`camera_group_id`),
  KEY `alarm_group_v1_tenants_FK` (`tenant_id`),
  CONSTRAINT `alarm_group_v1_camera_groups_FK` FOREIGN KEY (`camera_group_id`) REFERENCES `camera_groups` (`id`),
  CONSTRAINT `alarm_group_v1_tenants_FK` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

CREATE TABLE `alarm_group_update_v1` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `alarm_group_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `event_type` enum('alarm_group_queue_state_update','detectionevent','safe','escalationopen','escalationclosed','allocation','orphan','talkdown','twilio_call','twilio_message','investigation','llmevent','llmalarmgroupevent','autoresolvedevent','alarmgroupcutoff','view_alarm') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `event_details` json DEFAULT (json_object()),
  `created_at_utc` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at_utc` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `alarm_group_update_v1_alarm_groups_FK` (`alarm_group_id`),
  CONSTRAINT `alarm_group_update_v1_alarm_groups_FK` FOREIGN KEY (`alarm_group_id`) REFERENCES `alarm_group_v1` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

CREATE TABLE `event_v1` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `severity` enum('high','low','not_known') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `event_time_utc` timestamp NOT NULL,
  `event_details` json DEFAULT (json_object()),
  `tenant_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `camera_group_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `alarm_group_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at_utc` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at_utc` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `camera_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `recommendation` enum('resolve','escalate','not_known','error') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'not_known',
  `state` enum('PROCESSED','UNPROCESSED','ERROR') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'UNPROCESSED',
  PRIMARY KEY (`id`),
  KEY `event_v1_camera_groups_FK` (`camera_group_id`),
  KEY `event_v1_tenants_FK` (`tenant_id`),
  KEY `event_v1_alarm_groups_FK` (`alarm_group_id`),
  CONSTRAINT `event_v1_alarm_groups_FK` FOREIGN KEY (`alarm_group_id`) REFERENCES `alarm_group_v1` (`id`),
  CONSTRAINT `event_v1_camera_groups_FK` FOREIGN KEY (`camera_group_id`) REFERENCES `camera_groups` (`id`),
  CONSTRAINT `event_v1_tenants_FK` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci


-- 1. Main query index (most critical)
CREATE INDEX idx_alarm_group_main 
ON alarm_group_v1 (alarm_group_queue_state, tenant_id, state, recommendation);

-- 2. Count queries index  
CREATE INDEX idx_alarm_group_tenant_count 
ON alarm_group_v1 (tenant_id, state, recommendation);

-- 3. Operator-based queries
CREATE INDEX idx_alarm_group_operator 
ON alarm_group_v1 (operator_id, state, tenant_id, recommendation);

-- 4. Escalation count query
CREATE INDEX idx_alarm_group_escalation 
ON alarm_group_v1 (tenant_id, resolution);

--5. Get events for alarm group
CREATE INDEX idx_event_alarm_group 
ON event_v1 (alarm_group_id);