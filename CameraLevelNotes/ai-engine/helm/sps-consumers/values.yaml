# Default values for ai-engine.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.


image:
  repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-sps
  pullPolicy: Always
  tag: latest

nonGpuNodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: nvidia.com/gpu.count
            operator: DoesNotExist
          - key: purpose
            operator: DoesNotExist

amazoncli:
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/amazon/aws-cli
    tag: 2.23.5

# Creating a new sps consumer group to process tenant wise
speakerConsumerV2:
  enabled: true
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-sps
  resources:
    limits:
      cpu: 500m
      memory: 500Mi
    requests:
      cpu: 200m
      memory: 200Mi
  tenants: []

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8080

affinity: {}

nodeSelector: {}

tolerations: []

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "sps-consumers"
