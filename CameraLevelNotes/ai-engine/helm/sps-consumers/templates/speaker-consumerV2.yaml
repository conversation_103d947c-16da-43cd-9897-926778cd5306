{{- if .Values.speakerConsumerV2.enabled }}
{{- range .Values.speakerConsumerV2.tenants }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "sps-consumers.fullname" $ }}-{{ .name }}-speaker-consumer
  labels:
    {{- include "sps-consumers.labels" $ | nindent 4 }}
spec:
  replicas: 1
  strategy:
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels:
      {{- include "sps-consumers.selectorLabels" $ | nindent 6 }}
      "deploymentPod": {{ .name }}
  template:
    metadata:
      {{- with $.Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "sps-consumers.selectorLabels" $ | nindent 8 }}
        "deploymentPod": {{ .name }}
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                - key: nvidia.com/gpu.count
                  operator: DoesNotExist

      {{- with $.Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if $.Values.serviceAccount.create }}
      serviceAccountName: {{ include "sps-consumers.serviceAccountName" $ }}
      {{- end }}
      securityContext:
        {{- toYaml $.Values.podSecurityContext | nindent 8 }}
      initContainers:
      - name: download-speaker-clips
        image: {{ $.Values.amazoncli.image.repository }}:{{ $.Values.amazoncli.image.tag }}
        command: ["aws", "s3", "cp", "s3://speaker-clips/devices/", "/mnt/data/", "--recursive"]
        env:
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                name: aws-cred-sqs
                key: AWS_ACCESS_KEY_ID
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: aws-cred-sqs
                key: AWS_SECRET_ACCESS_KEY
        volumeMounts:
        - name: shared-data
          mountPath: /mnt/data
      containers:
      {{- range .speakers }}
      - name: {{ .name }}
        image: {{ $.Values.speakerConsumerV2.image.repository }}:{{ $.Values.image.tag }}
        imagePullPolicy: {{ $.Values.image.pullPolicy }}
        command: [ "python" ]
        args:
          - "integ/audio/sps/audio_consumer.py"
          - {{ .sqsQName | quote}}
          - {{ .waitTimeSecs | quote}}
          - {{ .containerPort | quote }}
        volumeMounts:
          - name: secrets
            mountPath: /secrets
            readOnly: true
          - name: conf
            mountPath: /conf
            readOnly: true
          - name: shared-data
            mountPath: /mnt/data
        ports:
          - name: met-sqs-{{ .containerPort }}
            containerPort: {{ .containerPort }}
            protocol: TCP
        resources:
          requests:
            cpu: {{ $.Values.speakerConsumerV2.resources.requests.cpu }}
            memory: {{ $.Values.speakerConsumerV2.resources.requests.memory }}
          limits:
            cpu: {{ $.Values.speakerConsumerV2.resources.limits.cpu }}
            memory: {{ $.Values.speakerConsumerV2.resources.limits.memory }}
      {{- end }}
      volumes:
        - name: engine-volume
          emptyDir: {}
        - name: shared-data
          emptyDir: {}
        - name: conf
          projected:
            sources:
              - configMap:
                  name: {{ include "sps-consumers.fullname" $ }}-conf
        - name: secrets
          projected:
            sources:
            - secret:
                name: rds-read-auth
                items:
                  - key: username
                    path: rds_read_username
                  - key: password
                    path: rds_read_password
                  - key: host
                    path: rds_read_host
                  - key: database
                    path: rds_read_database
            - secret:
                name: rds-auth
                items:
                  - key: username
                    path: rds_username
                  - key: password
                    path: rds_password
                  - key: host
                    path: rds_host
                  - key: database
                    path: rds_database
            - secret:
                name: rabbitmq-default-user
                optional: true
                items:
                  - key: username
                    path: rabbitmq_username
                  - key: password
                    path: rabbitmq_password
            - secret:
                name: aws-auth
                items:
                  - key: access_key_id
                    path: aws_access_key_id
                  - key: secret_key
                    path: aws_secret_key
            - secret:
                name: twilio-creds
                optional: true
                items:
                  - key: twilio_account_sid
                    path: twilio_account_sid
                  - key: twilio_auth_token
                    path: twilio_auth_token
                  - key: twiml_app_sid
                    path: twiml_app_sid
                  - key: twilio_api_key
                    path: twilio_api_key
                  - key: twilio_api_secret
                    path: twilio_api_secret
            - secret:
                name: aws-cred-consumer
                items:
                  - key: sqs_aws_access_key_id
                    path: sqs_aws_access_key_id
                  - key: sqs_aws_secret_key
                    path: sqs_aws_secret_key
                  - key: sqs_s3_bucket_name
                    path: sqs_s3_bucket_name
            - secret:
                name: {{ .name }}-speaker-creds
                items:
                  {{- range .speakers }}
                  - key: {{ .name }}-password
                    path: {{ .name }}-password
                  {{- end }}
---
{{- end }}
{{- end }}
