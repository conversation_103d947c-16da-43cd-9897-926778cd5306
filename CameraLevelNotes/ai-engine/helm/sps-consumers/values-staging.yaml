
image:
  repository: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-sps
  pullPolicy: Always
  tag: latest

nonGpuNodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: nvidia.com/gpu.count
            operator: NotIn
            values:
              - "1"

speakerConsumerV2:
  enabled: true
  resources:
    limits:
      cpu: 100m
      memory: 500Mi
    requests:
      cpu: 1m
      memory: 10Mi
  tenants:
    - name: "tenant-1"
      speakers:
        - name: "stage-20"
          sqsQName: "Staging_192_168_20_20_80.fifo"
          containerPort: 8101
          waitTimeSecs: 8
        - name: "stage-25"
          sqsQName: "Staging_Onvif.fifo"
          containerPort: 8102
          waitTimeSecs: 8
    - name: "tenant-2"
      speakers:
        - name: "stage-20"
          sqsQName: "Staging_192_168_20_20_80.fifo"
          containerPort: 8103
          waitTimeSecs: 8
        - name: "stage-25"
          sqsQName: "Staging_Onvif.fifo"
          containerPort: 8104
          waitTimeSecs: 8
    - name: "blr-india-app"
      speakers:
        - name: "bellflower-speaker3-1984"
          sqsQName: "blr_india_app_10_8_0_173_1984.fifo"
          containerPort: 8105
          waitTimeSecs: 8
