# Default values for frp.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
tls:
  enabled: false

tenants:
  - name: trl
    port: 5100
  - name: trl-bk
    port: 5102  
  - name: pgd-host
    port: 5430
  - name: pgdhostbk
    port: 5432
  - name: ricoh
    port: 5440
  - name: ricoh-bk
    port: 5442
  - name: ricoh-k8s
    port: 5444
  - name: oportun
    port: 5460
  - name: oportun-bk
    port: 5462
  - name: oportun-k8s
    port: 5464
  - name: prsgr
    port: 5470
  - name: prsgr-bk
    port: 5472
  - name: prsgr-k8s
    port: 5474
  - name: bartonpilot
    port: 5520
  - name: lenlyalchev
    port: 5540
  - name: towneford
    port: 5570
  - name: jsphtyta
    port: 5580
  - name: pprotect
    port: 5610
  - name: avondale1
    port: 5620
  - name: avondale2
    port: 5630
  - name: avondale3
    port: 5640
  - name: onetyta1
    port: 5670
  - name: onetyta2
    port: 5671
  - name: onetyta3
    port: 5672
  - name: onetyta4
    port: 5673
  - name: saitemple
    port: 5690
  - name: elkgrove
    port: 5700
  - name: elkgrove-bk
    port: 5701
  - name: hffcnstr
    port: 5705
  - name: phyundai
    port: 5710
  - name: kkfarms
    port: 5715
  - name: hakmotion
    port: 5720
  - name: sanleancd
    port: 5730
  - name: kcerritos
    port: 5740
  - name: slvbuick
    port: 5745
  - name: trinity01
    port: 5750
  - name: trinity02
    port: 5755
  - name: fer-au
    port: 5760
  - name: fer-au-cam
    port: 5761
  - name: sanlean
    port: 5765
  - name: sanleanc
    port: 5766
  - name: sanleanw
    port: 5767
  - name: sanleana
    port: 5768
  - name: ter-lan
    port: 5770
  - name: ter-lan-cam
    port: 5771
  - name: ss-stor
    port: 5775
  - name: ss-stor-cam
    port: 5776
  - name: almauto
    port: 5780
  - name: almauto-cam
    port: 5781
  - name: slvauto
    port: 5785
  - name: slvauto-cam
    port: 5786
  - name: am-stor
    port: 5790
  - name: am-stor-cam
    port: 5791
  - name: psi
    port: 5795
  - name: psi-k8s
    port: 5796
  - name: tracy
    port: 5800
  - name: tracy-cam
    port: 5801
  - name: zoomhan
    port: 5115
  - name: zoomhan-k8s
    port: 5116
  - name: zoomsuz
    port: 5105
  - name: zoomsuz-k8s
    port: 5106
  - name: zoomhef
    port: 5110
  - name: zoomhef-k8s
    port: 5111
  - name: blrind
    port: 5120
  - name: sanleandr
    port: 5125
  - name: zoomsanj
    port: 5130
  - name: zoomsan-k8s
    port: 5131
  - name: markley
    port: 5135
  - name: avantus
    port: 5200
  - name: aranov
    port: 5150
  - name: jonford
    port: 5155
  - name: edison
    port: 5160
  - name: eisley2
    port: 5170
  - name: eisley1
    port: 5175
  - name: startecpp
    port: 5180
  - name: almros
    port: 5185
  - name: mhfl
    port: 5190
  - name: almgwin
    port: 5195
  - name: parkcros
    port: 5205
  - name: banngm
    port: 5210
  - name: taylor
    port: 5215
  - name: destkn
    port: 5220
  - name: lex-ed
    port: 5225
  - name: lex-rtsp
    port: 5226
  - name: shieldss
    port: 5420
  - name: encinal
    port: 5425
  - name: yodertcic
    port: 5450
  - name: yoderwcg
    port: 5455
  - name: provpark
    port: 5910
  - name: solarentals
    port: 5915
  - name: pacifico
    port: 5940
  - name: wexleyrg
    port: 5900
  - name: hakimous
    port: 5120
  - name: edison
    port: 5855
  - name: beachworld
    port: 5960
  - name: chapmanauto
    port: 5975
  - name: urbaneast
    port: 5905

rdp:
  - name: prsgr
    port: 5471

image:
  repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-utility
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "frp.0.37.1"
nonGpuNodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: nvidia.com/gpu.count
            operator: DoesNotExist
imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: LoadBalancer
  port: 443
  elasticips:
    - eipalloc-015b373e458c05050
    - eipalloc-0bd499db389d2163d

resources:
  limits:
    cpu: 2
    memory: 1Gi
  requests:
    cpu: 40m
    memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}
