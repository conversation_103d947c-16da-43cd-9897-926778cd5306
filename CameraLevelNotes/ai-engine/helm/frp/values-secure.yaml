# Default values for frp.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
tls:
  enabled: true

tenants:
  - name: pgd
    port: 5430
  - name: ricoh
    port: 5440
  - name: oportun
    port: 5460

rdp: null

image:
  repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-utility
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "frp.0.37.1"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: LoadBalancer
  port: 443
  elasticips:
    - eipalloc-0b5e5045ea52249f1
    - eipalloc-01faf929c39163fb6

resources:
  limits:
    cpu: 2
    memory: 1Gi
  requests:
    cpu: 40m
    memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}
