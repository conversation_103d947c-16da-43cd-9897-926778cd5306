# Default values for ai-engine.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

image:
  repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-ai-engine
  pullPolicy: Always
  tag: latest
confOverrides:
  redis:
    enabled: true
    serviceName: hakimo-prod-redis.og9q14.ng.0001.usw2.cache.amazonaws.com
    visionServiceName: redis
  PINECONE_ENABLED: true
  PINECONE_INDEX: person-3584-prod
imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

global:
  models:
    image:
      repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-ai-models
      tag: "2025-03-20"
  gpuCount: 1

nonGpuNodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: nvidia.com/gpu.count
            operator: DoesNotExist
          - key: purpose
            operator: DoesNotExist

amazoncli:
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/amazon/aws-cli
    tag: 2.23.5

etlNodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: nvidia.com/gpu.count
            operator: DoesNotExist
          - key: purpose
            operator: In
            values:
              - "etl"

processor:
  enterpriseProcessor:
    image:
      repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-ai-engine
    resources:
      limits:
        cpu: 1000m
        memory: 4Gi
      requests:
        cpu: 300m
        memory: 1Gi
    replicaCount: 1
    terminationGracePeriodSeconds: 180
  queueProcessor:
    resources:
      limits:
        cpu: 1000m
        memory: 4Gi
      requests:
        cpu: 300m
        memory: 1Gi
    replicaCount: 0
    terminationGracePeriodSeconds: 180
  withVideo:
    image:
      repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-ai-engine
    resources:
      limits:
        cpu: 1000m
        memory: 4Gi
      requests:
        cpu: 300m
        memory: 1Gi
    replicaCount: 1
    terminationGracePeriodSeconds: 180
  withoutVideo:
    image:
      repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-ai-engine
    resources:
      limits:
        cpu: 500m
        memory: 2Gi
      requests:
        cpu: 50m
        memory: 512Mi
    replicaCount: 1
    terminationGracePeriodSeconds: 10

ml-service:
  enabled: true
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-ml-service
    pullPolicy: Always
    tag: latest
  service:
    name: ml-service
  haieConfOverrides:
    PINECONE_ENABLED: true
    PINECONE_INDEX: person-3584-prod

healthEtlPipeline:
  enabled: true
  tolerations:
  - key: "purpose"
    operator: "Equal"
    value: "etl"
    effect: "NoSchedule"
  resources:
    limits:
      cpu: 1500m
      memory: 5Gi
    requests:
      cpu: 10m
      memory: 1Gi
  tenants: []
gateway:
  replicaCount: 2
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-ai-engine
  resources:
    limits:
      cpu: 2000m
      memory: 4Gi
    requests:
      cpu: 50m
      memory: 2Gi
  conf:
    OBJECT_STORAGE:
      type: s3
    s3Bucket: haie-videos
    s3_talkdown_ingestion_bucket: hakimo-staging-talkdown-ingestion
    server:
      # Number of gunicorn workers within each gateway pod.
      workers: 6
      timeout: 120
    ui:
      feedback_url: https://dev-frontend.i.hakimo.ai
      url: https://demo.hakimo.ai/api/graphql
      authUrl: https://dev-fcsxgcsd.us.auth0.com/oauth/token
      auth0_client_id: GVZdZSBgj4IetnX2ZzAG2WQnSlVEHofm
      auth0_audience: https://hakimo.ai/orm
    apiAuthentication:
      auth0Domain: dev-0swt1hpa.us.auth0.com
      auth0Audience: http://localhost:4201/api
    authType: auth0
    cloudfront:
      public_key_id: K1ZNJODHGNHFVR
      url: https://dko4474d0ryge.cloudfront.net

etlPipeline:
  enabled: true
  tolerations:
  - key: "purpose"
    operator: "Equal"
    value: "etl"
    effect: "NoSchedule"
  tenants: []
  resources:
    limits:
      cpu: 1500m
      memory: 20Gi
    requests:
      cpu: 10m
      memory: 1Gi

laETLPipeline:
  enabled: true
  tolerations:
  - key: "purpose"
    operator: "Equal"
    value: "etl"
    effect: "NoSchedule"
  tenants: []
  resources:
    limits:
      cpu: 1500m
      memory: 20Gi
    requests:
      cpu: 10m
      memory: 1Gi

camHealth:
  enabled: true
  resources:
    limits:
      cpu: 1500m
      memory: 5Gi
    requests:
      cpu: 10m
      memory: 100Mi
  tenants: []

serverHealth:
  enabled: true
  resources:
    limits:
      cpu: 1500m
      memory: 5Gi
    requests:
      cpu: 10m
      memory: 100Mi
  tenants: []

tenantProcessor:
  enabled: false
  replicaCount: 1
  resources:
    limits:
      cpu: 1000m
      memory: 4Gi
    requests:
      cpu: 300m
      memory: 1Gi
  terminationGracePeriodSeconds: 180
  tenants: []

speakerConsumer:
  enabled: true
  queue: integ/audio/sps/audio_consumer.py
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-sps
  resources:
    limits:
      cpu: 100m
      memory: 100Mi
    requests:
      cpu: 1m
      memory: 10Mi
  tenants: []

# Creating a new sps consumer group to process tenant wise
speakerConsumerV2:
  enabled: true
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-sps
  queue: integ/audio/sps/audio_consumer.py
  resources:
    limits:
      cpu: 500m
      memory: 500Mi
    requests:
      cpu: 200m
      memory: 200Mi
  tenants: []

visionEventConsumer:
  enabled: true
  kedaEnabled: false
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/queue-consumer
  replicaCount: 1
  maxReplicas: 4
  scaleupQueueLength: 5
  queueUrl: https://sqs.us-west-2.amazonaws.com/************/vision_event_staging.fifo
  deadLetterQueueUrl: https://sqs.us-west-2.amazonaws.com/************/vision_event_staging_dlq.fifo
  awsRegion: us-west-2
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 100Mi

visionGetAlarmSequentialConsumer:
  enabled: true
  queueUrl: https://sqs.us-west-2.amazonaws.com/************/scan_get_alarm_transactions_staging.fifo
  awsRegion: us-west-2
  deadLetterQueueUrl: https://sqs.us-west-2.amazonaws.com/************/scan_get_alarm_transactions_staging_dlq.fifo
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/queue-consumer
  replicaCount: 1
  maxReplicas: 10
  scaleupQueueLength: 5
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 100Mi

visionLLMConsumer:
  enabled: true
  kedaEnabled: false
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/queue-consumer
  replicaCount: 1
  maxReplicas: 10
  scaleupQueueLength: 5
  queueUrl: https://sqs.us-west-2.amazonaws.com/************/vision_llm_event_staging.fifo
  awsRegion: us-west-2
  deadLetterQueueUrl: https://sqs.us-west-2.amazonaws.com/************/vision_llm_event_staging_dlq.fifo
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 100Mi

visionLLMAGConsumer:
  enabled: true
  kedaEnabled: false
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/queue-consumer
  replicaCount: 1
  maxReplicas: 10
  scaleupQueueLength: 5
  queueUrl: https://sqs.us-west-2.amazonaws.com/************/vision_llm_alarm_group_staging.fifo
  awsRegion: us-west-2
  deadLetterQueueUrl: https://sqs.us-west-2.amazonaws.com/************/vision_llm_alarm_group_staging_dlq.fifo
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 100Mi

visionETLPipeline:
  enabled: true
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/insight-etl
  tolerations:
  - key: "purpose"
    operator: "Equal"
    value: "etl"
    effect: "NoSchedule"
  tenants: []
  bucketName: hakimo-apmt-staging
  bucketKey: insight/hakimo_vision_insight.csv
  bucketRegion: us-west-2
  sheetUrl: https://docs.google.com/spreadsheets/d/1sbUa8qr8dAdNdQJaMeREu-oeDSp_k4YjTlP0Mgzhcf8/edit?pli=1&gid=1414635650#gid=1414635650
  s3Prefix: insight
  s3CsvFileName: hakimo_vision_insight.csv

  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 100Mi

visionEventHttp:
  enabled: true
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/http-server
  replicaCount: 3
  httpWorkers: 4
  resources:
    limits:
      cpu: 4000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 1000Mi

locationAlarmHttp:
  enabled: true
  image:
    repository: ************.dkr.ecr.us-west-2.amazonaws.com/hakimo-ai-engine
  replicaCount: 1
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 100Mi

stateManager:
  enabled: true
servicenow:
  enabled: false
  tenants: []
liveStreaming:
  enabled: true
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "ai-engine"

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8080

ingress:
  tls: []
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/auth-tls-verify-client: "on"
    nginx.ingress.kubernetes.io/auth-tls-secret: "backend/ca-secret"
    nginx.ingress.kubernetes.io/auth-tls-verify-depth: "1"
    nginx.ingress.kubernetes.io/auth-tls-pass-certificate-to-upstream: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "300m"
  hosts:
    - host: "local.i.hakimo.ai"
      paths:
        - path: /api
          pathType: Prefix
          backend:
            serviceName: ai-engine
            servicePort: 8080
ormIngress:
  tls: []
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: "local.i.hakimo.ai"
      paths:
        - path: /v2/orm
          pathType: Prefix
          backend:
            serviceName: ai-engine
            servicePort: 8080

affinity: {}

nodeSelector: {}

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

tolerations: []

log-server:
  enabled: true

redis:
  enabled: true

cloud:
  enabled: true

dockerPrePull:
  enabled: false

localStorage:
  enabled: false

dataPath: /data
bindPV: true
anomalous-alarms-service:
  enabled: false
  enableServer: false

#kubectl cordon ip-10-2-9-95.us-west-2.compute.internal
#kubectl drain  --ignore-daemonsets ip-10-2-9-95.us-west-2.compute.internal
#kubectl drain  --ignore-daemonsets ip-10-2-9-95.us-west-2.compute.internal --delete-emptydir-data
#kdno ip-10-2-9-95.us-west-2.compute.internal
#aws autoscaling terminate-instance-in-auto-scaling-group --instance-id i-0aa169aceade61b73\
#
#aws autoscaling terminate-instance-in-auto-scaling-group --instance-id i-0aa169aceade61b73 --should-decrement-desired-capacity
#kubectl cordon ip-10-2-52-100.us-west-2.compute.internal
#dd21e007aaeba1604158b09b28163e77877f8355
