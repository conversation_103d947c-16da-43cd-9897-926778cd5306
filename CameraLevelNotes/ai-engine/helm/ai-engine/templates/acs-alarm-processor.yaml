apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ai-engine.fullname" . }}-acs-processor
  labels:
    {{- include "ai-engine.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.processor.enterpriseProcessor.replicaCount }}
  {{- end }}
  strategy:
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels:
      {{- include "ai-engine.selectorLabels" . | nindent 6 }}
      "deploymentPod": "processor"
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "ai-engine.selectorLabels" . | nindent 8 }}
        "deploymentPod": "processor"
    spec:
      {{- with .Values.nonGpuNodeAffinity }}
      affinity:
        nodeAffinity:
        {{- toYaml . | nindent 10 }}
      {{- end }}

      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "ai-engine.serviceAccountName" . }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      terminationGracePeriodSeconds: {{ .Values.processor.withVideo.terminationGracePeriodSeconds }}
      containers:
        - name: processor
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.processor.withVideo.image.tag | default .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - name: engine-volume
              mountPath: /data
            - name: secrets
              mountPath: /secrets
              readOnly: true
            - name: conf
              mountPath: /conf
              readOnly: true
          {{- if .Values.localStorage.enabled }}
            - mountPath: /media
              name: data
          {{- end }}
          ports:
            - name: metrics-proc
              containerPort: 8000
              protocol: TCP
          command: ["python"]
          args:
            - "ml_module/ml_processor.py"
            - "--with_video"
            - "--enterprise_processor"
            - "true"
            {{- if .Values.tenantProcessor.enabled }}
            - "--tenants"
            {{- range .Values.tenantProcessor.tenants }}
            - {{ . | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- toYaml .Values.processor.enterpriseProcessor.resources | nindent 12 }}
      volumes:
        - name: engine-volume
          emptyDir: {}
        - name: conf
          projected:
            sources:
              - configMap:
                  name: {{ include "gateway.conf" . }}
              - configMap:
                  name: {{ include "ai-engine.fullname" . }}-conf
      {{- if .Values.localStorage.enabled }}
        - name: data
          persistentVolumeClaim:
            claimName: {{ .Release.Name }}-data
            readOnly: false
      {{- end }}
        - name: secrets
          projected:
            sources:
            - secret:
                name: rds-read-auth
                items:
                  - key: username
                    path: rds_read_username
                  - key: password
                    path: rds_read_password
                  - key: host
                    path: rds_read_host
                  - key: database
                    path: rds_read_database
            - secret:
                name: rds-auth
                items:
                  - key: username
                    path: rds_username
                  - key: password
                    path: rds_password
                  - key: host
                    path: rds_host
                  - key: database
                    path: rds_database
            - secret:
                name: pinecone-auth
                items:
                  - key: api-key
                    path: pinecone_api_key
            - secret:
                name: api-auth
                items:
                  # username is unused. But this secret is of type
                  # "basic-auth" for which either of username or password
                  # field needs to be present.
                  - key: username
                    path: api_username
                  - key: auth0_client_secret
                    path: auth0_client_secret
            - secret:
                name: jwt-secret-ui
                items:
                  - key: JWT_SECRET
                    path: jwt_secret_ui
            - secret:
                name: rabbitmq-default-user
                optional: true
                items:
                  - key: username
                    path: rabbitmq_username
                  - key: password
                    path: rabbitmq_password
            {{- if .Values.cloud.enabled }}
            - secret:
                name: aws-auth
                items:
                  - key: access_key_id
                    path: aws_access_key_id
                  - key: secret_key
                    path: aws_secret_key
            - secret:
                name: everbridge
                optional: true
                items:
                  - key: everbridge_user
                    path: everbridge_user
                  - key: everbridge_password
                    path: everbridge_password
            - secret:
                name: twilio-creds
                optional: true
                items:
                  - key: twilio_account_sid
                    path: twilio_account_sid
                  - key: twilio_auth_token
                    path: twilio_auth_token
                  - key: twiml_app_sid
                    path: twiml_app_sid
                  - key: twilio_api_key
                    path: twilio_api_key
                  - key: twilio_api_secret
                    path: twilio_api_secret
            - secret:
                name: google-api-creds
                items:
                 - key: entity-api-token
                   path: gemini_entity_api_token
                 - key: gemini-alarm-analyzer-api-token
                   path: gemini_alarm_analyzer_api_token
            {{- end }}


      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
