{{- if .Values.visionLLMAGConsumer.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ai-engine.fullname" $ }}-vision-llm-ag-consumer
  labels:
    {{- include "ai-engine.labels" $ | nindent 4 }}
spec:
  replicas: 1
  strategy:
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels:
      {{- include "ai-engine.selectorLabels" . | nindent 6 }}
      "deploymentPod": "vision-llm-ag-consumer"
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "ai-engine.selectorLabels" . | nindent 8 }}
        "deploymentPod": "vision-llm-ag-consumer"
    spec:
      {{- with .Values.nonGpuNodeAffinity }}
      affinity:
        nodeAffinity:
        {{- toYaml . | nindent 10 }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "ai-engine.serviceAccountName" . }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      volumes:
        - name: secrets
          projected:
            sources:
            - secret:
                name: rds-read-auth
                items:
                  - key: username
                    path: rds_read_username
                  - key: password
                    path: rds_read_password
                  - key: host
                    path: rds_read_host
                  - key: database
                    path: rds_read_database
            - secret:
                name: rds-auth
                items:
                  - key: username
                    path: rds_username
                  - key: password
                    path: rds_password
                  - key: host
                    path: rds_host
                  - key: database
                    path: rds_database
            - secret:
                name: aws-auth
                items:
                  - key: access_key_id
                    path: aws_access_key_id
                  - key: secret_key
                    path: aws_secret_key
            - secret:
                name: google-api-creds
                items:
                 - key: gemini-event-analyzer-api-token
                   path: gemini_event_analyzer_api_token
        - name: conf
          projected:
            sources:
              - configMap:
                  name: {{ include "gateway.conf" . }}
              - configMap:
                  name: {{ include "ai-engine.fullname" . }}-conf
      terminationGracePeriodSeconds: {{ .Values.processor.withVideo.terminationGracePeriodSeconds }}
      containers:
        - name: vision-llm-ag-consumer
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.visionLLMAGConsumer.image.repository }}:{{ .Values.visionLLMAGConsumer.image.tag | default .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["python"]
          args: 
            - "-m"
            - "vision.services.src.event_processor.async_main"
            - "--queue-url"
            - {{ .Values.visionLLMAGConsumer.queueUrl | quote }}
            - "--aws-region"
            - {{ .Values.visionLLMAGConsumer.awsRegion | quote }}
            - "--dead-letter-queue-url"
            - {{ .Values.visionLLMAGConsumer.deadLetterQueueUrl | quote }}
          volumeMounts:
            - name: secrets
              mountPath: /secrets
              readOnly: true
            - name: conf
              mountPath: /conf
              readOnly: true
          ports:
            - name: metrics-vllmagc
              containerPort: 8000
              protocol: TCP
          resources:
            {{- toYaml .Values.visionLLMAGConsumer.resources | nindent 12 }}
{{- end }} 
