apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ai-engine.fullname" . }}-gateway
  labels:
    {{- include "ai-engine.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.gateway.replicaCount }}
  {{- end }}
  strategy:
    rollingUpdate:
      maxUnavailable: 2
      maxSurge: 5
  selector:
    matchLabels:
      {{- include "ai-engine.selectorLabels" . | nindent 6 }}
      "deploymentPod": "gateway"
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "ai-engine.selectorLabels" . | nindent 8 }}
        "deploymentPod": "gateway"
    spec:
      {{- with .Values.nonGpuNodeAffinity }}
      affinity:
        nodeAffinity:
        {{- toYaml . | nindent 10 }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "ai-engine.serviceAccountName" . }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: gateway
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.gateway.image.tag | default .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - name: engine-volume
              mountPath: /data
            - name: engine-models-volume
              mountPath: /models
            - name: secrets
              mountPath: /secrets
              readOnly: true
            - name: conf
              mountPath: /conf
              readOnly: true
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 3
            timeoutSeconds: 10
            failureThreshold: 15
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: http
            timeoutSeconds: 10
            failureThreshold: 10
            periodSeconds: 10
          resources:
            {{- toYaml .Values.gateway.resources | nindent 12 }}

        - name: "watchdog"
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.gateway.image.tag | default .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - name: engine-volume
              mountPath: /data
            - name: engine-models-volume
              mountPath: /models
            - name: secrets
              mountPath: /secrets
              readOnly: true
            - name: conf
              mountPath: /conf
              readOnly: true
          {{- if .Values.localStorage.enabled }}
            - mountPath: /media
              name: data
          {{- end }}
          ports:
            - name: metrics
              containerPort: 8100
              protocol: TCP
          command: ["python"]
          args: ["watcher/main.py"]
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 100Mi
      volumes:
        - name: engine-volume
          emptyDir: {}
        - name: engine-models-volume
          emptyDir: {}
        - name: conf
          projected:
            sources:
              - configMap:
                  name: {{ include "gateway.conf" . }}
              - configMap:
                  name: {{ include "ai-engine.fullname" . }}-conf
      {{- if .Values.localStorage.enabled }}
        - name: data
          persistentVolumeClaim:
            claimName: {{ .Release.Name }}-data
            readOnly: false
      {{- end }}
        - name: secrets
          projected:
            sources:
            - secret:
                name: rds-read-auth
                items:
                  - key: username
                    path: rds_read_username
                  - key: password
                    path: rds_read_password
                  - key: host
                    path: rds_read_host
                  - key: database
                    path: rds_read_database
            - secret:
                name: rds-auth
                items:
                  - key: username
                    path: rds_username
                  - key: password
                    path: rds_password
                  - key: host
                    path: rds_host
                  - key: database
                    path: rds_database
            - secret:
                name: pinecone-auth
                items:
                  - key: api-key
                    path: pinecone_api_key
            - secret:
                name: eagleeye-auth
                items:
                  - key: eagleeye_client_id
                    path: eagleeye_client_id
                  - key: eagleeye_client_secret
                    path: eagleeye_client_secret
            - secret:
                name: api-auth
                items:
                  # username is unused. But this secret is of type
                  # "basic-auth" for which either of username or password
                  # field needs to be present.
                  - key: username
                    path: api_username
                  - key: auth0_client_secret
                    path: auth0_client_secret
            {{- if .Values.cloud.enabled }}
            - secret:
                name: aws-auth
                items:
                  - key: access_key_id
                    path: aws_access_key_id
                  - key: secret_key
                    path: aws_secret_key
            - secret:
                name: jwt-secret-ui
                items:
                  - key: JWT_SECRET
                    path: jwt_secret_ui
            - secret:
                name: twilio-creds
                optional: true
                items:
                  - key: twilio_account_sid
                    path: twilio_account_sid
                  - key: twilio_auth_token
                    path: twilio_auth_token
                  - key: twiml_app_sid
                    path: twiml_app_sid
                  - key: twilio_api_key
                    path: twilio_api_key
                  - key: twilio_api_secret
                    path: twilio_api_secret
            - secret:
                name: aws-cred-consumer
                items:
                  - key: sqs_aws_access_key_id
                    path: sqs_aws_access_key_id
                  - key: sqs_aws_secret_key
                    path: sqs_aws_secret_key
                  - key: sqs_s3_bucket_name
                    path: sqs_s3_bucket_name
            {{- end }}
            {{- if .Values.servicenow.enabled }}
            - secret:
                name: servicenow-creds
                items:
                  {{- range .Values.servicenow.tenants }}
                  - key: {{ . }}_snow_password
                    path: {{ . }}_snow_password
                  {{- end }}
            {{- end }}
            {{- if not .Values.localStorage.enabled }}
            - secret:
                name: cloudfront-creds
                items:
                  - key: private_key.pem
                    path: cf_private_key.pem
            {{- end}}
            - secret:
                name: rabbitmq-default-user
                optional: true
                items:
                  - key: username
                    path: rabbitmq_username
                  - key: password
                    path: rabbitmq_password
            - secret:
                name: everbridge
                optional: true
                items:
                  - key: everbridge_user
                    path: everbridge_user
                  - key: everbridge_password
                    path: everbridge_password
            - secret:
                name: tableau-password
                optional: true
                items:
                  - key: tableau_password
                    path: tableau_password
            - secret:
                name: intercom-creds
                items:
                  - key: secret_key
                    path: intercom_secret_key

      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
