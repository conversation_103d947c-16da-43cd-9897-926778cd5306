ingress:
  hosts:
    - host: "staging.i.hakimo.ai"

  tls:
    - hosts:
        - staging.i.hakimo.ai
ormIngress:
  hosts:
    - host: "staging-public.i.hakimo.ai"

  tls:
    - hosts:
        - staging-public.i.hakimo.ai
global:
  gpuCount: 1

nonGpuNodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: nvidia.com/gpu.count
            operator: NotIn
            values:
              - "1"

anomalous-alarms-service:
  enabled: false
confOverrides:
  ALARM_TALKDOWN_LATENCY_SEC: 1800 # This is the max latency allowed between the alarm creation time and the time of automatic talkdwon. Currently 1800 seconds
  rabbitmq:
    enabled: true
  redis:
    enabled: true
    serviceName: redis
  sqs:
    enabled: true
  MONITORING_QUEUE:
    rabbitmq:
      enabled: true
    sqs:
      enabled: true
      tenants:
        - "motion04-ofic-stag-test-d489a190d84b"
  PINECONE_ENABLED: true
  PINECONE_INDEX: person-3584-staging
  AUDIO_TALKDOWN_FETCH_ENABLED: true
gateway:
  conf:
    s3Bucket: haie-videos
    s3_talkdown_ingestion_bucket: hakimo-staging-talkdown-ingestion
    ui:
      feedback_url: https://staging-frontend.i.hakimo.ai
      url: https://staging-frontend.i.hakimo.ai/api/graphql
      authUrl: https://dev-fcsxgcsd.us.auth0.com/oauth/token
      auth0_client_id: GVZdZSBgj4IetnX2ZzAG2WQnSlVEHofm
      auth0_frontend_client_id: yYjdpXWddK9jwgm4QGlpiDZUcRqgXJkj
      org_id: org_apwEMKwgTYRWQaUO
    apiAuthentication:
      auth0Domain: dev-0swt1hpa.us.auth0.com
      auth0Audience: http://localhost:4201/api
    cloudfront:
      public_key_id: K1ZNJODHGNHFVR
      url: https://db4bikwl8adqp.cloudfront.net
      origin_s3_bucket: hakimo-livestream-staging

healthEtlPipeline:
  enabled: false

visionETLPipeline:
  enabled: false

serverHealth:
  enabled: false
  tenants: []

log-server:
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 5
    targetCPUUtilizationPercentage: 90
  service:
    name: log-server
  ingress:
    hosts:
      - host: "staging.i.hakimo.ai"
        paths:
          - path: /api/logs
            pathType: Prefix
            backend:
              serviceName: log-server
              servicePort: 8080
    tls:
      - hosts:
          - staging.i.hakimo.ai

# ETL configs in staging are only for Helm validations.
# ETL jobs might fail in staging
etlPipeline:
  enabled: true
  resources:
    limits:
      cpu: 1500m
      memory: 4Gi
    requests:
      cpu: 100m
      memory: 500Mi
  tenants: []
laETLPipeline:
  enabled: true
  tenants: []
  resources:
    limits:
      cpu: 1500m
      memory: 4Gi
    requests:
      cpu: 1000m
      memory: 1Gi

tenantProcessor:
  enabled: false
  tenants: []


ml-service:
  haieConfOverrides:
    PINECONE_ENABLED: true
    PINECONE_INDEX: person-3584-staging

visionEventConsumer:
  enabled: true
  kedaEnabled: false
  replicaCount: 1
  maxReplicas: 10
  scaleupQueueLength: 5
  image:
    repository: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/queue-consumer
  awsRegion: us-west-2
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 100Mi

visionGetAlarmSequentialConsumer:
  enabled: true
  queueUrl: https://sqs.us-west-2.amazonaws.com/695273141991/scan_get_alarm_transactions_staging.fifo
  awsRegion: us-west-2
  deadLetterQueueUrl: https://sqs.us-west-2.amazonaws.com/695273141991/scan_get_alarm_transactions_staging_dlq.fifo
  image:
    repository: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/queue-consumer
    tag: v1.1.0
  replicaCount: 1
  maxReplicas: 10
  scaleupQueueLength: 5
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 100Mi

visionLLMConsumer:
  enabled: true
  kedaEnabled: true
  replicaCount: 6
  maxReplicas: 10
  scaleupQueueLength: 5
  image:
    repository: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/queue-consumer

visionLLMAGConsumer:
  enabled: true
  kedaEnabled: true
  replicaCount: 6
  maxReplicas: 18
  scaleupQueueLength: 5
  image:
    repository: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/queue-consumer

visionEventHttp:
  enabled: true
  image:
    repository: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-vision/http-server
  replicaCount: 1
  httpWorkers: 2
  resources:
    limits:
      cpu: 2000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 1000Mi

locationAlarmHttp:
  enabled: true
  image:
    repository: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-ai-engine
  replicaCount: 1
  resources:
    limits:
      cpu: 1000m
      memory: 4Gi
    requests:
      cpu: 100m
      memory: 100Mi

speakerConsumerV2:
  enabled: true
  queue: integ/audio/sps/audio_consumer.py
  resources:
    limits:
      cpu: 100m
      memory: 500Mi
    requests:
      cpu: 1m
      memory: 10Mi
  tenants:
    - name: "motion04-sps-v2-1"
      speakers:
        - name: "stage-20"
          sqsQName: "Staging_192_168_20_20_80.fifo"
          containerPort: 8101
          waitTimeSecs: 8
        - name: "stage-25"
          sqsQName: "Staging_Onvif.fifo"
          containerPort: 8102
          waitTimeSecs: 8
    - name: "office-sps-v2-2"
      speakers:
        - name: "stage-20"
          sqsQName: "Staging_192_168_20_20_80.fifo"
          containerPort: 8103
          waitTimeSecs: 8
        - name: "stage-25"
          sqsQName: "Staging_Onvif.fifo"
          containerPort: 8104
          waitTimeSecs: 8
    - name: "blr-india-app"
      speakers:
        - name: "bellflower-speaker3-1984"
          sqsQName: "blr_india_app_10_8_0_173_1984.fifo"
          containerPort: 8105
          waitTimeSecs: 8
        - name: "unv-speaker-01"
          sqsQName: "blr_india_app_prologis01_frp_hakimo_ai_8080.fifo"
          containerPort: 8106
          waitTimeSecs: 8
        - name: "bia-141-80"
          sqsQName: "blr_india_app_192_168_3_141_80.fifo"
          containerPort: 8107
          waitTimeSecs: 8
        - name: "bia-12-80"
          sqsQName: "blr_india_app_192_168_3_12_80.fifo"
          containerPort: 8108
          waitTimeSecs: 8
