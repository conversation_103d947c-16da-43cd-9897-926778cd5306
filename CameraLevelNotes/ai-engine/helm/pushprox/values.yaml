# Default values for pushprox.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-utility
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: pushprox-proxy-v0.1.0
# List of all tenants that will connect.
# This is not used, and does not need to trigger a redeploy
# Only used to update prometheus scrape configs
# Please maintain alphabetical order.
# Commented tenants are pilots for which presently there is no active traffic.
tenants:
  - "avondale1"
  - "avondale2"
  - "bartonpilot"
  - "elkgrove"
  - "josephtoyota"
  - "lenlyallchev"
  - "omega"
  - "onetyta1"
  - "onetyta2"
  - "saitemple"
  - "phyundai"
  - "kootkfarms"
  - "hakmotion"
  - "kcerritos"
  - "slvbuick"
  - "zoomhan"
  - "zoomsuz"
  - "zoomhef"
  - "zoomsanj"
  - "sanleandr"
  - "mmotors"
  - "almros"
  - "almgwin"
  - "banngm"
  - "destkn"
  - "jonford"
  - "lexused"
  - "mhfl"
  - "startecpp"
  - "taylor"
  - "shieldss"
  - "encinal"
  - "yodertcic"
  - "yoderwcg"
  - "pacifico"
  - "solarentals"
  - "provpark"
  - "wexleyrg"
  - "beachworld"
  - "chapmanauto"
  - "edison"
  - "urbaneast"

#  - "hakimoiota" disabling it as it creates noise in prod monitoring.

nonGpuNodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: nvidia.com/gpu.count
            operator: DoesNotExist
imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/auth-tls-secret: "backend/ca-secret"
    nginx.ingress.kubernetes.io/auth-tls-verify-depth: "1"
    nginx.ingress.kubernetes.io/proxy-body-size: "60m"
  hosts:
    - host: "metrics.i.hakimo.ai"
      paths:
        - path: /
          pathType: Prefix
          backend:
            serviceName: pushprox
            servicePort: 8080
  tls:
    - hosts:
        - metrics.i.hakimo.ai
      secretName: server-tls
service:
  type: ClusterIP
  port: 8080

resources:
  limits:
    cpu: 2
    memory: 1Gi
  requests:
    cpu: 40m
    memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}
