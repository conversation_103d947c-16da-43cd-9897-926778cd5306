import json
import uuid
from datetime import datetime, timezone

import structlog

import controller as ctrl
from config import backend_config as config
from integ.audio.sps.audio_sqs_publisher import AudioSQSPayloadPublisher
from models_rds.audio_devices import AudioDevices
from models_rds.rds_client import RDSClient
from models_rds.talkdowns import TalkDowns

log = structlog.get_logger("hakimo", module="AudioPublisher")
bucket = "speaker-clips"
axis = "/data/motion/events/audios/psssst.mp3"
acs = "/data/motion/events/audios/trespassing.wav"
onvif = "/data/motion/events/audios/trespassing.wav"
eagleeye_automated_media = "/data/eagle_eye/trespassing.wav"


class TestTalkdowns:
    def __init__(self, sqs_publisher):
        self.sqs_publisher = sqs_publisher
        rds = RDSClient()
        db = rds.db_adapter
        ctrl_map = ctrl.ControllerMap(db)
        self._talkdowns = ctrl_map.talkdowns

    def _log_talkdown_payload(
        self,
    ):
        tenant_id = "accelaz-fresh-start"
        camera_id = "4a6c5597-f57e-4388-9e31-b05caccc672b"
        device_type = "EAGLE_EYE"
        talkdown_type = "automated"
        alarm_id = "8f14d4cf-469c-4824-ac2a-43fa95e842ce"
        location_alarm_id = 1933264
        audio_device_id = 447
        s3_file_path = "devices/axis/trespassing.mov"
        audioDevice = AudioDevices()
        audioDevice.id = 447
        audioDevice.url = "https://media.c001.eagleeyenetworks.com:443/media/streams/audio/1003e5fe/ulaw"
        audioDevice.audio_rtsp_url = "https://media.c001.eagleeyenetworks.com:443/media/streams/audio/1003e5fe/ulaw"
        audioDevice.device_type = "EAGLE_EYE"
        audioDevice.user_name = "root"
        audioDevice.proxy_url = "https://media.c001.eagleeyenetworks.com:443/media/streams/audio/1003e5fe/ulaw"
        audioDevice.queue_url = "https://sqs.us-west-2.amazonaws.com/************/accelaz_fresh_start_1003e5fe.fifo"
        audioDevice.queue_name = "accelaz_fresh_start_1003e5fe.fifo"
        audioDevice.password_file_path = "path/to/password/file"
        audioDevice.password_audio = "/secrets/stage-20-password"
        audioDevice.tenant_id = tenant_id
        audioDevice.is_audio_ingestion_enabled = False
        for iter in range(0, 1):
            # if iter == 0:
            #     talkdown_type = "manual"
            talkdown = TalkDowns()
            payload_id = uuid.uuid4()
            talkdown.talkdown_id = str(payload_id)
            talkdown.tenant_id = tenant_id
            talkdown.audio_device_id = audio_device_id
            talkdown.camera_id = camera_id
            talkdown.raw_alarm_id = alarm_id
            talkdown.location_alarm_id = location_alarm_id
            talkdown.talkdown_type = talkdown_type
            talkdown.talkdown_state = "IN_QUEUE"
            talkdown.status = "PENDING"
            talkdown_details = {}
            if talkdown_type == "manual":
                conf = config.gateway()
                s3_bucket = conf["cloudfront"]["origin_s3_bucket"]
                talkdown_details = {
                    "audio_file_bucket": s3_bucket,
                    "audio_file_path": s3_file_path,
                }
            else:
                normalized_device_type = device_type
                log.debug(
                    "Normalized device type",
                    normalized_device_type=normalized_device_type,
                )
                if normalized_device_type == "AXIS":
                    s3_file_path = axis
                elif normalized_device_type == "ACS":
                    s3_file_path = acs
                elif normalized_device_type == "ONVIF":
                    s3_file_path = onvif
                elif normalized_device_type == "EAGLEEYE_AUTOMATED":
                    s3_file_path = eagleeye_automated_media
                talkdown_details = {
                    "audio_file_bucket": bucket,
                    "audio_file_path": s3_file_path,
                }
            talkdown.actual_talkdown_path = json.dumps(talkdown_details)
            talkdown.request_published_utc = datetime.now(timezone.utc)
            self._talkdowns.add_talkdown(talkdown)
            log.info(payload_id)
            ## Publish audio
            start_time = str(
                datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")
            )
            add_audio_start_time_utc = int(
                datetime.now(timezone.utc).timestamp() * 1000
            )
            escalation_id = 1
            self.sqs_publisher.process_and_publish_audio(
                payload_id,
                audioDevice,
                camera_id,
                "100749bb",
                alarm_id,
                location_alarm_id,
                talkdown_type,
                s3_file_path,
                eagleeye_automated_media,
                audioDevice.queue_name,
                add_audio_start_time_utc,
                audioDevice.device_type,
                start_time,
                escalation_id,
            )


if __name__ == "__main__":
    testTalkdowns = TestTalkdowns(AudioSQSPayloadPublisher())
    testTalkdowns._log_talkdown_payload()
