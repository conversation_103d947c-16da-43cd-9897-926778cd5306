import os
import time
from datetime import datetime, timezone

import structlog

from common_utils.rpc import Rpc
from common_utils.time_utils import utc_to_milliseconds
from integ.audio.sps.speaker_behavior import Speaker<PERSON>ehavior
from integ.audio.sps.speaker_playback_utils import (
    convert_to_mp3,
    download_mp3_talkdown_audio_file_from_s3,
    get_audio_local_file,
    record_alarm_talkdown_time_difference,
    record_talkdown_metrics,
)
from integ.audio.sps.speaker_response import SpeakerResponse
from integ.unv.audio_api import UnvAudioAPI

log = structlog.get_logger("hakimo", module="SpeakerPlaybackBehaviors")
unv_automated_media = "/mnt/data/unv/trespassing.mp3"


class UnvSpeakerBehavior(SpeakerBehavior):
    def __init__(self):
        self.s3_bucket = "speaker-clips"
        self.s3_bucket_url = (
            "https://speaker-clips.s3.us-west-2.amazonaws.com/"
        )
        self.s3_bucket_path = "devices/unv/"
        self.auto_talkdown_clip = "trespassing.mp3"
        self.audio_file_extension = ".mp3"

    def play_audio(self, payload) -> SpeakerResponse:
        audio_payload = {}
        audio_payload["camera_client_id"] = payload.get("camera_client_id")
        audio_payload["client_audio_device_id"] = payload.get(
            "client_audio_device_id"
        )
        audio_payload["type"] = payload.get("device_type")
        audio_payload["url"] = payload.get("url")
        audio_payload["talkdown_type"] = payload.get("talkdown_type")
        audio_payload["tenant_id"] = payload.get("tenant_id")
        audio_payload["local_speaker_url"] = payload.get("local_speaker_url")
        audio_payload["s3_file_path"] = payload.get("s3_file_path")
        audio_payload["clip_name"] = payload.get("clip_name")
        audio_payload["alarm_created_time"] = payload.get("alarm_created_time")
        audio_payload["username"] = payload.get("username", "admin")
        audio_payload["password_file"] = payload.get("password_file")

        log.info(
            "Adding audio_payload for UNV Speakers",
            audio_payload=audio_payload,
        )
        rpc_obj = Rpc(
            {
                "url": payload.get("url"),
                "digest_auth": {
                    "username": audio_payload["username"],
                    "password_file": audio_payload["password_file"],
                },
            }
        )
        client = UnvAudioAPI(rpc_obj)
        talkdown_type = payload.get("talkdown_type")
        if talkdown_type == "manual":
            log.info(
                "Processing manual talkdown",
                action="manual_talkdown_initiated",
                device_type=payload["device_type"],
            )
            tmp_audio_file = get_audio_local_file(payload)
            audio_local_file_path = convert_to_mp3(tmp_audio_file)
            audio_payload["clip_name"] = os.path.basename(
                audio_local_file_path
            )
            log.info(
                "Uploading Media Clip",
                audio_local_file_path=audio_local_file_path,
            )
            if client.upload_audio(audio_file_path=audio_local_file_path):
                log.info(
                    "Media Clip uploaded successfully",
                    audio_local_file_path=audio_local_file_path,
                )
            else:
                log.error("Failed to upload audio")
                is_success = False
                comments_message = "Talkdown Failed"
                record_alarm_talkdown_time_difference(payload, "speaker")
                return SpeakerResponse(
                    is_success=is_success, message=comments_message
                )
        else:
            log.info(
                "Processing automated talkdown",
                action="automated_talkdown_initiated",
                device_type=payload["device_type"],
            )
            predefined_clip_name = os.path.basename(unv_automated_media)
            clip_name = payload.get("clip_name")
            if clip_name and clip_name != predefined_clip_name:
                s3_file_path = f"{self.s3_bucket_path}{clip_name}"
                log.debug(
                    "Downloading audio file from S3",
                    s3_file_path=s3_file_path,
                )
                audio_local_file_path = (
                    download_mp3_talkdown_audio_file_from_s3(
                        self.s3_bucket, s3_file_path
                    )
                )
            else:
                audio_local_file_path = unv_automated_media
                log.debug(
                    "Using predefined audio file",
                    audio_local_file_path=audio_local_file_path,
                )
            log.info(
                "Uploading Media Clip",
                audio_local_file_path=audio_local_file_path,
            )
            if client.upload_audio(audio_file_path=audio_local_file_path):
                log.info(
                    "Media Clip uploaded successfully",
                    audio_local_file_path=audio_local_file_path,
                )
            else:
                log.error("Failed to upload audio")
                is_success = False
                comments_message = "Talkdown Failed"

        if client.play_media_clip(audio_payload["clip_name"]):
            log.info("Media Clip played successfully")
            is_success = True
            comments_message = "Talkdown Successful"
        else:
            log.error(
                "Failed to play media clip",
                clip_name=audio_payload["clip_name"],
            )
            is_success = False
            comments_message = "Talkdown Failed"
        record_alarm_talkdown_time_difference(payload, "speaker")
        time.sleep(10)  # Wait for the audio to play

        if client.delete_audio_file(audio_payload["clip_name"]):
            log.info(
                "Audio file deleted successfully",
                clip_name=audio_payload["clip_name"],
            )
        else:
            log.error(
                "Failed to delete audio file from device",
                clip_name=audio_payload["clip_name"],
            )
        return SpeakerResponse(is_success=is_success, message=comments_message)

    def call_record_audio_talkdown_metrics(
        self,
        payload,
        is_success,
        start_time,
        add_audio_start_time,
        http_status,
        rtp_url,
    ):
        end_time = time.time()
        device_type = payload.get("device_type")
        tenant_id = payload.get("tenant_id")
        time_taken = end_time - start_time
        utc_now = datetime.now(timezone.utc)
        add_audio_end_time_utc = utc_to_milliseconds(utc_now)
        add_audio_time_taken = add_audio_end_time_utc - add_audio_start_time
        add_audio_time_taken_secs = add_audio_time_taken / 1000

        record_talkdown_metrics(
            payload,
            is_success,
            time_taken,
            add_audio_time_taken_secs,
            http_status,
        )
        if is_success:
            log.info(
                "UNV audio send operation completed",
                tenant_id=tenant_id,
                device_type=device_type,
                talkdown_type=device_type,
                http_status=http_status,
                url=rtp_url,
                time_taken=time_taken,
                e2e_time_taken=add_audio_time_taken_secs,
                action="automated_talkdown_completed",
            )
        else:
            log.info(
                "UNV audio send operation Failed.",
                tenant_id=tenant_id,
                device_type=device_type,
                talkdown_type=device_type,
                http_status=http_status,
                is_success=is_success,
                url=rtp_url,
                time_taken=time_taken,
                e2e_time_taken=add_audio_time_taken_secs,
                action="automated_talkdown_completed",
            )
            return is_success
