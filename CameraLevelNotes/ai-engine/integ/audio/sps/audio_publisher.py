import json
import uuid
from datetime import datetime, timezone

import structlog

import controller as ctrl
from config import backend_config as config
from models_rds.audio_devices import AudioDevices
from models_rds.rds_client import RDSClient
from models_rds.talkdowns import TalkDowns

log = structlog.get_logger("hakimo", module="AudioPublisher")
bucket = "speaker-clips"
axis = "devices/axis/trespassing.mov"
acs = "devices/acs/JC.mp3"
onvif = "devices/onvif/trespassing.wav"
vds = "devices/onvif/trespassing.mp3"
unv = "devices/unv/trespassing.mp3"


class AudioPublisher:
    def __init__(self, sqs_publisher):
        self.sqs_publisher = sqs_publisher
        rds = RDSClient()
        db = rds.db_adapter
        ctrl_map = ctrl.ControllerMap(db)
        self._talkdowns = ctrl_map.talkdowns

    def _log_talkdown_payload(
        self,
        payload_id,
        audio_device: AudioDevices,
        camera_id,
        alarm_id,
        location_alarm_id,
        talkdown_type,
        s3_file_path,
        state,
        escalation_id,
    ):
        talkdown = TalkDowns()
        talkdown.talkdown_id = str(payload_id)
        talkdown.tenant_id = audio_device.tenant_id
        talkdown.audio_device_id = audio_device.id
        talkdown.camera_id = camera_id
        talkdown.raw_alarm_id = alarm_id
        talkdown.location_alarm_id = location_alarm_id
        talkdown.escalation_id = escalation_id
        talkdown.talkdown_type = talkdown_type
        talkdown.talkdown_state = "IN_QUEUE"
        talkdown.status = "PENDING"
        if not state:
            talkdown.talkdown_state = "FAILED_TO_PUBLISH_TO_SQS"
            talkdown.status = "FAILED"
        talkdown_details = {}
        if talkdown_type == "manual":
            conf = config.gateway()
            s3_bucket = conf["cloudfront"]["origin_s3_bucket"]
            talkdown_details = {
                "audio_file_bucket": s3_bucket,
                "audio_file_path": s3_file_path,
            }
        else:
            normalized_device_type = audio_device.device_type.strip().upper()
            log.debug(
                "Normalized device type",
                normalized_device_type=normalized_device_type,
            )
            if normalized_device_type == "AXIS":
                s3_file_path = axis
            elif normalized_device_type == "ACS":
                s3_file_path = acs
            elif normalized_device_type == "ONVIF":
                s3_file_path = onvif
            elif normalized_device_type == "VDS":
                s3_file_path = vds
            elif normalized_device_type == "GO2RTC_ONVIF":
                s3_file_path = onvif
            elif normalized_device_type == "UNV":
                s3_file_path = unv
            talkdown_details = {
                "audio_file_bucket": bucket,
                "audio_file_path": s3_file_path,
            }
        talkdown.actual_talkdown_path = json.dumps(talkdown_details)
        talkdown.request_published_utc = datetime.now(timezone.utc)
        self._talkdowns.add_talkdown(talkdown)

    def process_and_publish_audio(
        self,
        audio_device: AudioDevices,
        camera_id,
        camera_client_id,
        alarm_id,
        location_alarm_id,
        talkdown_type,
        s3_file_path,
        audio_local_file_path,
        rabbitmq_queue_name,
        sqs_queue_name,
        add_audio_start_time_utc,
        camera_integration_type,
        alarm_created_time,
        escalation_id,
        payload_id,
    ) -> bool:
        """Process audio file and publish the payload to RabbitMQ and SQS."""
        # TODO: Remove - initializing pyaload_id for backward compatibility
        if not payload_id:
            payload_id = uuid.uuid4()
        log.info(
            "Publishing audio payload to sqs",
            payload_id=payload_id,
            sqs_queue_name=sqs_queue_name,
            tenant_id=audio_device.tenant_id,
            camera_id=camera_client_id,
            proxy_url=audio_device.proxy_url,
            device_type=audio_device.device_type,
            talkdown_type=talkdown_type,
            alarm_id=alarm_id,
            audio_device_url=audio_device.url,
            alarm_created_time=alarm_created_time,
            escalation_id=escalation_id,
        )
        stat = self.sqs_publisher.process_and_publish_audio(
            payload_id,
            audio_device,
            camera_id,
            camera_client_id,
            alarm_id,
            location_alarm_id,
            talkdown_type,
            s3_file_path,
            audio_local_file_path,
            sqs_queue_name,
            add_audio_start_time_utc,
            camera_integration_type,
            alarm_created_time,
            escalation_id,
        )
        self._log_talkdown_payload(
            payload_id,
            audio_device,
            camera_id,
            alarm_id,
            location_alarm_id,
            talkdown_type,
            s3_file_path,
            stat,
            escalation_id,
        )
        return stat
