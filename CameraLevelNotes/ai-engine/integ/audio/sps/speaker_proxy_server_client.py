# import json
import asyncio
import threading
from datetime import datetime, timezone

import structlog

import controller as ctrl
from common_utils.io_helpers import read_file
from common_utils.metrics_definitions import HAKIMO_ALARM_AUDIO_TIME_DIFFERENCE
from config import backend_config as config
from integ.audio.sps.audio_talkdown_fetcher import AudioTalkdownFetcher
from integ.audio.sps.axis_audio_talkdown_fetcher import (
    AxisAudioTalkdownFetcher,
)
from integ.audio.sps.speaker_playback_strategies import (
    ACSSpeakerBehavior,
    ACSSpeakerBehaviorVPN,
    AXISSirenSpeakerBehavior,
    AXISSpeakerBehavior,
    EagleEyeSpeakerBehavior,
    ONVIFSpeakerBehavior,
    SIPSpeakerBehavior,
)
from integ.audio.sps.speaker_playback_strategy_dvs import DvsSpeakerBehavior
from integ.audio.sps.speaker_playback_strategy_onvif_go2rtc import (
    Go2rtcOnvifSpeakerBehavior,
)
from integ.audio.sps.speaker_playback_strategy_unv import Unv<PERSON>peaker<PERSON>ehavior
from interfaces.audio_fetcher import AudioFetcher
from models_rds.rds_client import RDSClient

log = structlog.get_logger("hakimo", module="SpeakerProxyServerClientV2")
talkdown_directory = "talkdown_audio_files"


def validate_and_get(payload, keys):
    """
    Validates that the keys are present in the payload and returns their values.
    """
    values = {}
    for key in keys:
        value = payload.get(key)
        if value is None:
            raise KeyError(f"{key} not found in payload")
        values[key] = value
    return values


def get_audio_password_from_secrets(path: str) -> str:
    passwd = read_file(path)
    return passwd


class SpeakerProxyServerClient:
    def __init__(self):
        self.behaviors = {
            "ACS": ACSSpeakerBehavior(),
            "AXIS": AXISSpeakerBehavior(),
            "ACS_VPN": ACSSpeakerBehaviorVPN(),
            "AXIS_SIREN": AXISSirenSpeakerBehavior(),
            "ONVIF": ONVIFSpeakerBehavior(),
            "SIP": SIPSpeakerBehavior(),
            "EAGLE_EYE": EagleEyeSpeakerBehavior(),
            "GO2RTC_ONVIF": Go2rtcOnvifSpeakerBehavior(),
            "DVS": DvsSpeakerBehavior(),
            "UNV": UnvSpeakerBehavior(),
        }
        rds = RDSClient()
        db = rds.db_adapter
        ctrl_map = ctrl.ControllerMap(db)
        self._talkdowns = ctrl_map.talkdowns
        self._tenant = ctrl_map.tenant

    def check_alarm_time_auto_talkdown_difference(self, payload) -> bool:
        alarm_created_time = datetime.utcnow().replace(microsecond=0)
        if payload.get("alarm_created_time"):
            alarm_created_time = payload.get("alarm_created_time")
            alarm_created_time = datetime.strptime(
                alarm_created_time, "%Y-%m-%d %H:%M:%S"
            )
        current_time = datetime.utcnow()
        current_time = current_time.replace(microsecond=0)
        max_time_difference = config.HAIE.ALARM_TALKDOWN_LATENCY_SEC
        current_time_difference = (
            current_time - alarm_created_time
        ).total_seconds()
        if current_time_difference > max_time_difference:
            log.warning(
                f"Current time and alarm time difference is more than {max_time_difference} seconds.",
                alarm_created_time=alarm_created_time.strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
                current_time=current_time.strftime("%Y-%m-%d %H:%M:%S"),
                max_time_difference=max_time_difference,
                time_difference=current_time_difference,
            )
            log.warning(
                "Talkdown Discarded due to alarm and talkdown time difference.",
                alarm_created_time=alarm_created_time.strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
                current_time=current_time.strftime("%Y-%m-%d %H:%M:%S"),
                max_time_difference=max_time_difference,
                time_difference=current_time_difference,
            )
            HAKIMO_ALARM_AUDIO_TIME_DIFFERENCE.labels(
                tenant=payload.get("tenant_id"),
                device_type=payload.get("device_type"),
                is_success=False,
            ).observe(current_time_difference)
            return False
        return True

    def is_talkdown_allowed(self, payload) -> bool:
        if payload.get("talkdown_type") == "automated":
            is_alarm_talkdown_difference_allowed = (
                self.check_alarm_time_auto_talkdown_difference(payload)
            )
            if is_alarm_talkdown_difference_allowed:
                return True
            return False
        return True

    def process_payload(self, payload):
        # data = json.loads(payload)
        return self._send_audio_payload(payload)

    def discard_talkdown(self, payload_id, state, custom_message=None):
        self._log_talkdown(payload_id, state, custom_message)

    def _log_talkdown(self, payload_id, state, custom_message=None):
        talkdown = self._talkdowns.get_talkdown(payload_id)
        if not talkdown:
            log.warning(
                "Talkdown not found in database",
                talkdown_id=payload_id,
                state=state,
                custom_message=custom_message,
            )
            return
        talkdown.status = "SUCCESS" if state else "FAILED"
        talkdown.talkdown_state = "PROCESSED"
        talkdown.custom_message = custom_message
        talkdown.request_completed_utc = datetime.now(timezone.utc)
        log.debug("Updating talkdown", talkdown_id=payload_id)
        self._talkdowns.update_talkdown(talkdown)

    def _send_audio_payload(self, payload) -> bool:
        device_type = payload.get("device_type")
        log.info("Sending audio payload to device", device_type=device_type)
        response = False
        if device_type in self.behaviors:
            talkdown_id = payload.get("payload_id")
            is_allowed = self.is_talkdown_allowed(payload)
            if is_allowed:
                # TODO : Change : Repurposed password_file_path for access token for Eagle eye integ v1
                if device_type.lower() == "eagle_eye":
                    tenant_config = self._tenant.get_config(
                        tenant_id=payload.get("tenant_id")
                    )
                    payload["password_file_path"] = tenant_config.oAuthConfig[
                        "access_token"
                    ]
                if config.HAIE.AUDIO_TALKDOWN_FETCH_ENABLED and payload.get(
                    "is_audio_ingestion_enabled", False
                ):
                    if (
                        device_type == "AXIS"
                        and config.HAIE.AXIS_AUDIO_TALKDOWN_FETCH_ENABLED
                    ):
                        self._axis_prepare_to_fetch_talkdown(payload)
                    else:
                        self._prepare_to_fetch_talkdown(payload)
                is_vpn_enabled = payload.get("is_vpn_enabled")
                if device_type == "ACS" and is_vpn_enabled:
                    device_type = "ACS_VPN"
                speaker_response = self.behaviors[device_type].play_audio(
                    payload
                )
                self._log_talkdown(
                    talkdown_id,
                    speaker_response.get_is_success(),
                    speaker_response.get_message(),
                )
                response = speaker_response.get_is_success()
            else:
                self._log_talkdown(
                    talkdown_id,
                    True,
                    "talkdown is dropped due to timeout",
                )
                log.warning(
                    "Talkdown not raised. Payload has not been processed",
                    tenant_id=payload.get("tenant_id"),
                    alarm_id=payload.get("alarm_id"),
                    talkdown_type=payload.get("talkdown_type"),
                    url=payload.get("url"),
                    local_speaker_url=payload.get("local_speaker_url"),
                )
                response = True
        else:
            log.error("Unsupported device type", device_type=device_type)
            self._log_talkdown(payload, False, "Unsupported device type")
        return response

    def _prepare_to_fetch_talkdown(self, payload):
        """
        Prepares to fetch talkdown audio from the specified camera.

        1. Finds rtsp_url, username and password for the proximity camera
        2. Creates AudioTalkdownFetcher instance
        """
        try:
            keys = [
                "audio_rtsp_url",
                "prox_camera_username",
                "prox_camera_password",
                "tenant_id",
                "payload_id",
            ]
            values = validate_and_get(payload, keys)
            audio_rtsp_url = values["audio_rtsp_url"]
            prox_camera_username = values["prox_camera_username"]
            prox_camera_password = values["prox_camera_password"]
            tenant_id = values["tenant_id"]
            talkdown_id = values["payload_id"]

            extracted_password = get_audio_password_from_secrets(
                prox_camera_password
            )
            audio_fetcher = AudioTalkdownFetcher(
                audio_rtsp_url,
                prox_camera_username,
                extracted_password,
                talkdown_directory,
                tenant_id,
                talkdown_id,
            )
            self._fetch_talkdown(audio_fetcher)
        except KeyError as e:
            log.error("Missing key in payload", error=e)
        except Exception as e:
            # Handle exception during the setup
            log.error("Error in _prepare_to_fetch_talkdown", error=e)

    def _axis_prepare_to_fetch_talkdown(self, payload):
        """
        Prepares to fetch talkdown audio from the axis speaker.

        1. Finds axis url, username and password
        2. Creates AxisAudioTalkdownFetcher instance
        """
        try:
            keys = [
                "username",
                "password_file",
                "url",
                "tenant_id",
                "payload_id",
            ]
            values = validate_and_get(payload, keys)
            username = values["username"]
            audio_password_file = payload.get("password_file")
            password = get_audio_password_from_secrets(audio_password_file)
            tenant_id = values["tenant_id"]
            audio_api_url = values["url"]
            talkdown_id = values["payload_id"]
            audio_fetcher = AxisAudioTalkdownFetcher(
                audio_api_url,
                username,
                password,
                talkdown_directory,
                tenant_id,
                talkdown_id,
            )
            self._fetch_talkdown(audio_fetcher)
        except KeyError as e:
            log.error("Missing key in payload", error=e)
        except Exception as e:
            # Handle exception during the setup
            log.error("Error in _axis_prepare_to_fetch_talkdown", error=e)

    def _fetch_talkdown(self, audio_fetcher: AudioFetcher):
        """
        Fetches talkdown audio using the provided AudioTalkdownFetcher instance.

        1. Generates a filename for the talkdown audio file.
        2. Constructs the output path for saving the talkdown audio file.
        3. Defines a helper function to asynchronously fetch the talkdown audio.
        4. Runs the helper function asynchronously in a new event loop.
        5. Starts a new thread to run the asynchronous operation.
        """

        async def _helper():
            await audio_fetcher.fetch()

        def async_run():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(_helper())

        # Run the async_run function in a new thread
        thread = threading.Thread(target=async_run)
        thread.start()
