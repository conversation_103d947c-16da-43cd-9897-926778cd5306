import base64
import os
import subprocess
import tempfile
import uuid
from datetime import datetime

import structlog
from botocore.exceptions import ClientError

from common_utils.io_helpers import read_file
from common_utils.metrics_definitions import (
    HAKIMO_AUDIO_TALKDOWN_COUNTER,
    HAKIMO_TALKDOWN_SPEAKER_E2E_LATENCY,
    HAKIMO_TALKDOWN_SPEAKER_LATENCY,
    HAKIMO_TOTAL_TIME_BETWEEN_ALARM_TALKDOWN,
)
from common_utils.s3_utils import get_s3_client
from config import backend_config as config

log = structlog.get_logger("hakimo", module="SpeakerPlaybackBehaviors")


def get_audio_password_from_secrets(path: str) -> str:
    passwd = read_file(path)
    return passwd


def save_audio_file(file_path, data):
    """Save binary data to an MP3 file."""
    with open(file_path, "wb") as file:
        file.write(data)


def get_audio_local_file(data) -> str:
    # Assuming 'mp3_data' is the key for the Base64-encoded MP3 data
    audio_data_base64 = data["audio_data"]
    audio_data = base64.b64decode(audio_data_base64)
    # Assuming there's a field for the file name or generating a unique one
    device_type = data.get("device_type")
    file_name = data.get("file_name", f"received_file_{uuid.uuid4()}.mov")
    if device_type == "ACS":
        file_name = data.get("file_name", f"received_file_{uuid.uuid4()}.mp3")
    if (
        device_type == "EAGLE_EYE"
        or device_type == "GO2RTC_ONVIF"
        or device_type == "VDS"
    ):
        file_name = data.get("file_name", f"received_file_{uuid.uuid4()}.wav")
    directory_path = "/tmp"  # Specify your desired directory
    audio_local_file_path = f"{directory_path}/{file_name}"
    save_audio_file(audio_local_file_path, audio_data)
    return audio_local_file_path


def record_talkdown_metrics(
    payload,
    is_success,
    time_taken_secs,
    add_audio_time_taken_secs,
    http_status: str = "UNKNOWN",
):
    tenant_id = payload.get("tenant_id", "unknown")
    device_type = payload.get("device_type", "unknown")
    talkdown_type = payload.get("talkdown_type", "unknown")

    # Assuming the Prometheus metrics have been defined elsewhere as per previous examples
    HAKIMO_AUDIO_TALKDOWN_COUNTER.labels(
        tenant=tenant_id,
        device_type=device_type,
        playback_type=talkdown_type,
        is_success=is_success,
        http_status=http_status,
    ).inc()

    HAKIMO_TALKDOWN_SPEAKER_LATENCY.labels(
        tenant=tenant_id,
        talkdown_type=talkdown_type,
        device_type=device_type,
        is_success=is_success,
    ).observe(time_taken_secs)

    HAKIMO_TALKDOWN_SPEAKER_E2E_LATENCY.labels(
        tenant=tenant_id,
        talkdown_type=talkdown_type,
        device_type=device_type,
        is_success=is_success,
    ).observe(add_audio_time_taken_secs)


def record_alarm_talkdown_time_difference(payload, device):
    if payload.get("alarm_created_time"):
        alarm_created_time = payload.get("alarm_created_time")
        alarm_created_time = datetime.strptime(
            alarm_created_time, "%Y-%m-%d %H:%M:%S"
        )
    else:
        alarm_created_time = datetime.utcnow().replace(microsecond=0)
    current_time = datetime.utcnow()
    current_time = current_time.replace(microsecond=0)
    current_time_difference = (
        current_time - alarm_created_time
    ).total_seconds()
    HAKIMO_TOTAL_TIME_BETWEEN_ALARM_TALKDOWN.labels(
        tenant=payload.get("tenant_id"),
        device_type=payload.get("device_type"),
        is_success=False,
        talkdown_type=payload.get("talkdown_type"),
        device=device,
    ).observe(current_time_difference)
    log.info(
        "Recorded the metrics for total time taken from the alarm generation to completion of talkdown",
        tenant_id=payload.get("tenant_id"),
        url=payload.get("url"),
        device_type=payload.get("device_type"),
        talkdown_type=payload.get("talkdown_type"),
        device=device,
    )


def copy_audio_file_to_s3(file_path, bucket_name, s3_file_path):
    try:
        # aws_access_key = read_file("/secrets/aws_access_key_id")
        # aws_secret = read_file("/secrets/aws_secret_key")
        access_key = read_file(config.HAIE.SQS_AWS_ACCESS_KEY_ID, missing="")
        secret_key = read_file(config.HAIE.SQS_AWS_SECRET_KEY, missing="")
    except FileNotFoundError as fe:
        log.error(
            "Missing aws creds. Cannot download audio file from s3.",
            exc_info=fe,
        )
        return None

    # s3 = get_s3_client(aws_access_key, aws_secret)
    s3 = get_s3_client(access_key, secret_key)
    log.debug(
        "Uploading audio file to s3 bucket.",
        s3_file_path=s3_file_path,
        bucket_name=bucket_name,
    )
    try:
        s3.upload_file(
            Filename=file_path, Bucket=bucket_name, Key=s3_file_path
        )
    except ClientError as ce:
        log.error(
            "Failed to upload audio file to s3 bucket",
            s3_file_path=s3_file_path,
            bucket_name=bucket_name,
            exc_info=ce,
        )
        return None
    return s3_file_path


def download_automated_talkdown_audio_file_from_s3(bucket_name, s3_file_path):
    try:
        access_key = read_file(config.HAIE.SQS_AWS_ACCESS_KEY_ID, missing="")
        secret_key = read_file(config.HAIE.SQS_AWS_SECRET_KEY, missing="")
    except FileNotFoundError as fe:
        log.error(
            "Missing aws creds. Cannot download audio file from s3.",
            exc_info=fe,
        )
        return None

    s3 = get_s3_client(access_key, secret_key)
    local_file_name = tempfile.mktemp()
    log.debug(
        "Downloading audio file from s3 bucket",
        s3_file_path=s3_file_path,
        bucket_name=bucket_name,
    )

    try:
        s3.download_file(
            Bucket=bucket_name, Key=s3_file_path, Filename=local_file_name
        )
        return local_file_name
    except ClientError as ce:
        log.error(
            "Failed to download audio file from s3 bucket",
            s3_file_path=s3_file_path,
            bucket_name=bucket_name,
            exc_info=ce,
        )
        return None


def convert_audio_file_to_wav(file_path):
    """
    Converts an audio file to WAV format using ffmpeg.
    Supports multiple input formats such as MP3, AAC, etc.

    :param file_path: Path to the input audio file.
    :return: Path to the converted WAV file or None if conversion fails.
    """
    if not os.path.isfile(file_path):
        log.error("File not found", input_file=file_path)
        return None

    # Determine the output WAV file path
    wav_file_path = os.path.splitext(file_path)[0] + ".wav"

    try:
        # Run ffmpeg to convert the file
        result = subprocess.run(
            [
                "ffmpeg",
                "-y",
                "-i",
                file_path,
                "-acodec",
                "pcm_s16le",
                "-ar",
                "16000",
                "-ac",
                "1",
                wav_file_path,
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )

        # Check if conversion was successful
        if result.returncode != 0:
            log.error("ffmpeg failed with message", message=result.stderr)
            return None

        log.info(
            "Audio file Conversion successful",
            input_file=file_path,
            output_file=wav_file_path,
        )
        return wav_file_path
    except FileNotFoundError:
        log.error("ffmpeg is not installed or not found in PATH.")
        return None
    except Exception as e:
        log.error("Unexpected error", exception=e)
        return None


def convert_to_mp3(input_file, output_file=None, quality=2):
    """
    Converts a media file to MP3 format using ffmpeg.

    Parameters:
    - input_file: str, path to the input media file.
    - output_file: str or None, optional path for the output MP3 file.
    - quality: int, audio quality (0 = best, 9 = worst). Default is 2.

    Returns:
    - output_file: str, path to the resulting MP3 file.
    """
    if not os.path.isfile(input_file):
        raise FileNotFoundError(f"Input file '{input_file}' not found.")

    if output_file is None:
        base, _ = os.path.splitext(input_file)
        output_file = base + ".mp3"

    command = [
        "ffmpeg",
        "-y",  # Overwrite without asking
        "-i",
        input_file,  # Input file
        "-vn",  # Disable video
        "-acodec",
        "libmp3lame",
        "-q:a",
        str(quality),
        output_file,
    ]

    try:
        subprocess.run(
            command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )
        print(f"Converted '{input_file}' to '{output_file}'")
    except subprocess.CalledProcessError as e:
        print(f"Error converting file: {e.stderr.decode().strip()}")
        raise

    return output_file


def download_mp3_talkdown_audio_file_from_s3(s3_bucket, s3_file_path):
    """
    Downloads an audio file from a given payload and converts it to MP3 format.

    :param s3_bucket: S3 bucket name where the audio file is stored.
    :param s3_file_path: Path to the audio file in the S3 bucket.
    :raises FileNotFoundError: If the audio file does not exist in the specified S3 bucket.
    :raises Exception: If there is an error during the conversion process.
    :return: Path to the converted MP3 file or None if conversion fails.
    """
    audio_local_file_path = download_automated_talkdown_audio_file_from_s3(
        s3_bucket, s3_file_path
    )
    if not audio_local_file_path:
        log.error("Failed to retrieve audio local file path.")
        return None
    new_file_name = os.path.basename(s3_file_path)
    try:
        converted_file_path = convert_to_mp3(
            audio_local_file_path, new_file_name
        )
        log.info(
            "Audio file converted successfully",
            converted_file_path=converted_file_path,
        )
        return converted_file_path
    except Exception as e:
        log.error("Error converting audio file", exception=e)
        return None
