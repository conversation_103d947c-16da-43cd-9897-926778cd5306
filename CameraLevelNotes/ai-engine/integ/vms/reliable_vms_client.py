import datetime
import os
import os.path as osp
import time
import typing

import pytz
import structlog

from common_utils.rpc import RpcException
from common_utils.tracer import trace_method
from config import appliance_config as config
from ingestion_processor.disk_cleanup import DiskCleanup
from integ.milestone.utils.milestone_video_exception import (
    MilestoneVideoException,
)
from integ.vms.interface import VmsClient
from integ.vms.video_interval import VideoInterval
from integ.vms.video_scraper import VideoScraper

log = structlog.get_logger("hakimo", module="Reliable VMS Client")


class ReliableVmsClient:
    def __init__(self, vms_client: VmsClient):
        self._vms_client = vms_client
        self.video_clip_output_path = config.HIP.VIDEO_OUTPUT_PATH
        self._disk_cleanup = DiskCleanup()
        os.makedirs(self.video_clip_output_path, exist_ok=True)

    @trace_method
    def get_video_reliably(  # pylint: disable=too-many-arguments
        self,
        alarm_uuid: str,
        camera_info: dict,
        start_time_msec: float,
        end_time_msec: float,
        **kwargs: str,
    ) -> typing.Tuple[str, float, float]:
        """Returns the concatenated video scrape for an alarm between a requested start and end time.
        This function returns the on-disk dumped video file containing all concatenated video scrapes
        along with the "actual" start time of the video,
        the calculated end time(actual_start_time + duration) and an existence flag.
        This method also takes care of calculating the start time for the next video scrape within
        the configured number of attempts and handles all edge cases.

        Args:
            alarm_uuid (str): Hakimo alarm UUID for which video is requested
            camera_info (dict): dictionary that has all necessary details to extract
            video
            start_time_msec (float): requested video start unix timestamp in milliseconds
            end_time_msec (float): requested video end unix timestamp in milliseconds
            **kwargs (str): keyword arguments containing request and task metadata

        Raises:
            FileNotFoundError: video cannot be obtained, even on retry

        Returns:
            [typing.Tuple[str, float, float]]: tuple containing the scraped video filename,
            actual start time of the video (ms), calculated end time of the video (ms).
        """

        fragments = []
        s_date = datetime.datetime.utcfromtimestamp(start_time_msec // 1000)
        s_date = pytz.utc.localize(s_date)
        e_date = datetime.datetime.utcfromtimestamp(end_time_msec // 1000)
        e_date = pytz.utc.localize(e_date)
        required_video_interval = VideoInterval(start=s_date, end=e_date)
        video_out_folder = osp.join(
            self.video_clip_output_path,
            f"{alarm_uuid}_{int(start_time_msec)}_{int(end_time_msec)}",
        )
        os.makedirs(video_out_folder, exist_ok=True)
        vs = VideoScraper(required_video_interval)
        (next_req_interval, status, atmpt) = vs.next_scrape(
            required_video_interval, None
        )
        while True:
            if next_req_interval is None:
                log.debug(
                    "Next interval is None. Breaking...",
                    status=status,
                    attempt=atmpt,
                )
                break
            start_time_msec = next_req_interval.start.timestamp() * 1000
            end_time_msec = next_req_interval.end.timestamp() * 1000
            frag_output_path = osp.join(
                video_out_folder,
                f"{alarm_uuid}_{int(start_time_msec)}_{int(end_time_msec)}_{atmpt}.mp4",
            )
            try:
                video_scrape_result = self._vms_client.get_video_scrape(
                    frag_output_path,
                    camera_info,
                    start_time_msec,
                    end_time_msec,
                    atmpt,
                    {
                        "task_id": kwargs.get("task_id"),
                        "alarm_id": alarm_uuid,
                    },
                )
            except MilestoneVideoException as ms_exc:
                log.warning(
                    "No video from milestone for the requested interval",
                    ex=ms_exc,
                    cam=camera_info["camera_name"],
                    start_time_msec=start_time_msec,
                    end_time_msec=end_time_msec,
                )
                break
            requested_vid_interval = next_req_interval
            log.info(
                f"Attempted video scrape result in {atmpt} attempt for cam {camera_info['camera_name']}",
                video_scrape_result=video_scrape_result,
            )
            if not video_scrape_result.err:  # everything is ok!
                fragments.append(video_scrape_result)

            got = video_scrape_result.interval
            (next_req_interval, status, atmpt) = vs.next_scrape(
                requested_vid_interval, got
            )
            if status == "Done":
                break
            if status == "MaxAttemptsExceeded":
                log.warning(
                    "Could not get video after max attempts",
                    status=status,
                    next_interval=next_req_interval,
                )
                raise RpcException(
                    retryable=True,
                    message="Could not get video after max attempts",
                )

            # Sleep before next iteration if video scrape returned an error
            if video_scrape_result.err:
                log.warning(
                    "Could not get the video scrape from the API. Waiting and going to next attempt",
                    err=video_scrape_result.err,
                )
                time.sleep(2)

        log.debug(
            "All Video Fragments Details",
            fragments=fragments,
            fragment_count=len(fragments),
        )
        if len(fragments) == 0:
            log.error(
                "No video fragments found for the requested interval",
                requested_vid_interval=required_video_interval,
                cam=camera_info["camera_name"],
            )
            raise FileNotFoundError(
                f"No video fragments found for the requested interval for cam {camera_info['camera_name']}: {required_video_interval}"
            )

        fileout = osp.join(
            self.video_clip_output_path,
            f"{alarm_uuid}_{int(start_time_msec)}_{int(end_time_msec)}.mp4",
        )
        vs.finalise_videos(fragments, fileout)
        # Get rid of all fragments
        self._disk_cleanup.move_file_for_cleanup(video_out_folder)
        # final concatenated intervals
        returned_start = min([i.interval.start for i in fragments])
        returned_end = max([i.interval.end for i in fragments])

        if (
            min(
                returned_end.timestamp(),
                required_video_interval.end.timestamp(),
            )
            - max(
                returned_start.timestamp(),
                required_video_interval.start.timestamp(),
            )
        ) <= 0:
            log.error(
                "No overlap between required video interval and final video interval",
                returned_start=returned_start,
                returned_end=returned_end,
                requested_vid_interval=requested_vid_interval,
                required_video_interval=required_video_interval,
            )
            raise FileNotFoundError(
                "No overlap between required video interval and final video interval"
            )

        return (
            fileout,
            returned_start.timestamp() * 1000,
            returned_end.timestamp() * 1000,
        )

    def get_all_cameras_json(self) -> typing.List[typing.Dict]:
        """Simple Wrapper for vms_client's get_all_cameras_json method.

        Returns:
            typing.List[typing.Dict]: List of camera dictionaries
        """
        return self._vms_client.get_all_cameras_json()

    def get_camera_image(
        self, cam_json: typing.Dict, img_time: datetime.datetime
    ) -> typing.Optional[str]:
        """Simple Wrapper for vms_client's get_camera_image method

        Args:
           cam_json (typing.Dict): Camera details
           img_time (datetime.datetime): Time at which to extract image
        Returns:
           str: File path of dumped image, or None if not available
        """
        return self._vms_client.get_camera_image(cam_json, img_time)
