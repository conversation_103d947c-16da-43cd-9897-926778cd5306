import time

import structlog

from common_utils.rpc import Rpc

log = structlog.get_logger("hakimo.integ.unv", module="audio_api")

PLAY_MEDIA_CLIP_MAX_RETRIES = 1


def get_audio_file_id(json_data, filename):
    """
    Given a JSON-like dict and a filename, return the ID of the matching file.

    :param json_data: Dict containing the JSON data structure
    :param filename: Filename string to look for
    :return: Integer ID if found, else None
    """
    try:
        file_info_list = json_data["Response"]["Data"]["FileInfoList"]
        for file_info in file_info_list:
            if file_info.get("FileName") == filename:
                return file_info.get("ID")
    except (KeyError, TypeError):
        pass  # You could log or handle this differently if needed
    return None


class UnvAudioAPI:
    def __init__(self, rpc: Rpc) -> None:
        self.rpc = rpc

    def play_media_clip(self, clip_name: str) -> bool:
        log.debug(
            "Attempting to play media clip",
            clip_id=clip_name,
        )
        for r in range(PLAY_MEDIA_CLIP_MAX_RETRIES):
            log.debug(
                "Attempting to Identify the ID of the uploaded media clip",
                clip_id=clip_name,
                attempt=r,
            )
            response = self.rpc.get(
                "/LAPI/V1.0/Channels/AudioChannel/0/AudioFile/Info",
            )
            if response.ok():
                json_data = response.json()
                file_id = get_audio_file_id(json_data, clip_name)
                if file_id is None:
                    log.error(
                        "Media clip not found in response",
                        clip_id=clip_name,
                        attempt=r,
                        status=404,
                    )
                    continue
            if response.status_code == 400:
                body = response.body()
                if body is not None:
                    body = body.replace("\n", " ").lower()
                    log.debug(body, attempt=r)
                    time.sleep(8)
                continue
            log.debug(
                "Successfully identified the ID of the uploaded media clip",
                clip_id=clip_name,
                file_id=file_id,
                attempt=r,
            )
            log.debug(
                "Attempting to play the media clip",
                clip_id=clip_name,
                file_id=file_id,
                attempt=r,
            )
            # Now play the media clip using the identified file_id
            res = self.rpc.post_json(
                path_="/LAPI/V1.0/Channels/AudioChannel/0/AudioFile/PlayFile",
                payload={
                    "FileID": file_id,
                    "Mode": 0,
                    "Count": 1,
                },
            )
            if res.ok():
                log.debug("Successfully played media clip.", attempt=r)
                return True
            body = res.body()
            if body is not None:
                body = body.replace("\n", " ")
            log.error(
                "Failed to play media clip",
                clip_id=clip_name,
                attempt=r,
                status=res.status_code,
                body=body,
            )
            continue

        # If we reach here, it means we exhausted all retries
        log.error(
            "Failed to play media clip",
            clip_id=clip_name,
            attempt=r,
            status=res.status_code,
        )
        return False

    def upload_audio(self, audio_file_path: str) -> bool:
        """Play any arbitrary audio specified by audio file path.

        Args:
            audio_file_path (str): Path to the audio file to upload
        """
        files = {
            "file": audio_file_path
        }  # Rpc.post will handle opening the file
        res = self.rpc.post(
            payload="",
            path_="/LAPI/V1.0/Channels/AudioChannel/0/AudioFile",
            files=files,
        )
        if not res.ok():
            body = res.body()
            if body is not None:
                body = body.replace("\n", " ").lower()
            log.error(
                "Failed to upload audio",
                body=body,
                audio_file_path=audio_file_path,
                clip_name=audio_file_path.split("/")[-1],
                status=res.status_code,
            )
            return False
        return True

    def delete_audio_file(self, clip_name: str) -> bool:
        log.debug(
            "Attempting to delete audio file",
            clip_name=clip_name,
        )
        response = self.rpc.get(
            "/LAPI/V1.0/Channels/AudioChannel/0/AudioFile/Info",
        )
        if not response.ok():
            log.error(
                "Failed to get audio file info",
                status=response.status_code,
            )
            return False
        json_data = response.json()
        audio_file_id = get_audio_file_id(json_data, clip_name)
        if audio_file_id is None:
            log.error(
                "Audio file not found",
                clip_name=clip_name,
                status=response.status_code,
            )
            return False
        log.debug(
            "Deleting audio file",
            clip_name=clip_name,
            audio_file_id=audio_file_id,
        )
        res = self.rpc.delete(
            path_=f"/LAPI/V1.0/Channels/AudioChannel/0/AudioFile/{audio_file_id}"
        )
        if not res.ok():
            log.error(
                "Failed to delete audio",
                status=res.status_code,
                audio_file_id=audio_file_id,
            )
            return False
        log.debug(
            "Successfully deleted audio file",
            clip_name=clip_name,
            audio_file_id=audio_file_id,
        )
        return True

    def upload_and_play_audio(self, audio_file_path: str) -> bool:
        """Upload and play audio file.

        Args:
            client (UnvAudioAPI): Instance of UnvAudioAPI to interact with the device.
            audio_file_path (str): Path to the audio file to upload and play.
        """
        if not self.upload_audio(audio_file_path):
            log.error("Failed to upload audio")
            return False
        if not self.play_media_clip(audio_file_path.split("/")[-1]):
            log.error("Failed to play media clip")
            return False
        time.sleep(10)  # Wait for the audio to play
        if not self.delete_audio_file(audio_file_path.split("/")[-1]):
            log.error("Failed to delete audio file from device")
            return False
        log.info("Audio file uploaded, played, and deleted successfully")
        return True


if __name__ == "__main__":
    rpc_obj = Rpc(
        {
            "url": "http://prologis1.frp.hakimo.ai:8080",
            "digest_auth": {
                "username": "admin",
                "password": "S3curity!",
            },
        }
    )
    client = UnvAudioAPI(rpc_obj)

    # response = client.upload_audio(
    #     audio_file_path="/Users/<USER>/Downloads/trespassing.mp3"
    # )
    # print(response)
    # response = client.play_media_clip("trespassing.mp3")
    # print(response)
    # response = client.delete_audio_file("trespassing.mp3")
    # print(response)
    resp = client.upload_and_play_audio(
        audio_file_path="/Users/<USER>/Downloads/trespassing.mp3"
    )
    print(resp)
