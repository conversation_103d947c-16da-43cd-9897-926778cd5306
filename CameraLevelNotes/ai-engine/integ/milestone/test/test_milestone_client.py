import datetime
import os.path as osp

import pytest

from common_utils.io_helpers import get_filename_from_cam_details
from config import appliance_config as config
from factory.milestone_api_factory import get_milestone_sdk_ref
from integ.milestone.vms.milestone_client import MilestoneVMSClient


def get_milestone_vms_client():
    sdk = get_milestone_sdk_ref()
    con = MilestoneVMSClient(sdk)
    return con


@pytest.mark.skip("Requires connection to Milestone")
class TestMilestoneVmsClient:
    @pytest.fixture
    def client(self):
        client = get_milestone_vms_client()
        return client

    def test_get_video_scrape(self, client):
        resp = client.get_video_scrape(
            "/tmp/cam01",
            {
                "camera_name": "cam01",
                "client_camera_id": "d8ffd8ad-3439-44c1-add8-8d588de2877c",
            },
            1749204399000,
            1749204419000,
            1,
            {
                "task_id": "test_task_id",
            },
        )
        assert resp is not None

    @pytest.fixture
    def params(self, client):
        camera = client.get_any_cameras_json()
        return camera

    def test_get_any_cameras_json(self, client):
        resp = client.get_any_cameras_json()
        assert resp
        assert len(resp)
        assert "GUID" in resp
        assert "Name" in resp
        assert "FQUIDKind" in resp
        assert "ParentId" in resp

    def test_get_all_cameras_json(self, client):
        cameras = client.get_all_cameras_json()
        # print(cameras)
        assert cameras
        assert len(cameras)
        for camera in cameras:
            assert "GUID" in camera
            assert "Name" in camera
            assert "FQUIDKind" in camera
            assert "ParentId" in camera

    def test_get_camera_image(self, client, params):
        dt = datetime.datetime.now()
        resp = client.get_camera_image(
            params,
            dt,
        )
        assert resp
        file_path = resp
        assert osp.exists(
            osp.join(
                config.HIP.cameraSyncer["imageStore"],
                get_filename_from_cam_details(
                    params.get("GUID"), params.get("Name")
                )
                + ".jpeg",
            )
        )
        assert file_path == osp.join(
            config.HIP.cameraSyncer["imageStore"],
            get_filename_from_cam_details(
                params.get("GUID"), params.get("Name")
            )
            + ".jpeg",
        )
