import datetime
import json
import os
import os.path as osp
import time
import typing

import dateutil.parser
import structlog
from dateutil import tz

from common_utils.ffmpeg_helpers import (
    get_metadata_ffprobe,
    save_stream,
    video_duration_millis,
)
from common_utils.io_helpers import get_filename_from_cam_details
from common_utils.time_utils import parse_millisecs_to_utc
from config import appliance_config as config
from errors.retryable_error import RetryableError
from integ.milestone.milestone_sdk import MilestoneSDK
from integ.milestone.utils.milestone_video_exception import (
    MilestoneVideoException,
)
from integ.vms.interface import VmsClient
from integ.vms.video_interval import VideoInterval
from integ.vms.video_result import VideoResult
from interfaces.server_data import ServerData

log = structlog.get_logger("hakimo", module="milestone/client")


class MilestoneVMSClient(VmsClient):
    INTEGRATION_TYPE = "MILESTONE"

    def __init__(self, milestone_sdk: MilestoneSDK):
        self._milestone_sdk = milestone_sdk

    def get_video_scrape(
        self,
        video_path: str,
        camera_info: dict,
        start_time_msec: float,
        end_time_msec: float,
        attempt: int,
        request_info: dict,
    ) -> VideoResult:
        """
        Returns a simple video scrape from the VMS only.
        """
        st = time.time()
        try:
            camera_name = camera_info["camera_name"]
            camera_id = camera_info["client_camera_id"]

            # making sure camera id is there in the db
            assert camera_id is not None, "Camera Id should be specified"

            stream_start = parse_millisecs_to_utc(start_time_msec)
            stream_end = parse_millisecs_to_utc(end_time_msec)

            log.debug(
                "Getting Milestone video",
                video_local_start_time=start_time_msec,
                video_local_end_time=end_time_msec,
                camera_name=camera_name,
                camera_id=camera_id,
                request_id=request_info.get("task_id"),
            )

            current_time_msec = datetime.datetime.utcnow().timestamp() * 1000
            if end_time_msec - current_time_msec > 0:
                sleep_time_msec = end_time_msec - current_time_msec
                sleep_time_seconds = datetime.timedelta(
                    milliseconds=sleep_time_msec
                ).seconds
                log.info(
                    "Sleeping before querying to Milestone",
                    sleep_time_seconds=sleep_time_seconds,
                )
                time.sleep(sleep_time_seconds)

            # calling the Milestone SDK media API to get video stream
            res = self._milestone_sdk.get_media(
                camera_id=camera_id,
                time_start=stream_start,
                time_end=stream_end,
                request_id=request_info.get("task_id"),
            )
            if res.ok():
                vid_available = True
                # If the response is JSON, check if video_available is False
                try:
                    resp_body = json.loads(res.response.text)
                    vid_available = bool(
                        resp_body.get("video_available", True)
                    )
                except json.JSONDecodeError:
                    log.debug(
                        "Non JSON response from Milestone SDK",
                        status=res.status(),
                        camera_id=camera_id,
                        stream_start=stream_start,
                        stream_end=stream_end,
                        attempt=attempt,
                    )
                if not vid_available:
                    raise MilestoneVideoException(
                        f"Video not available from Milestone SDK RPC call. state: {res.status()}, body: {resp_body}"
                    )
                    # raise FileNotFoundError(
                    #     f"Video not available from Milestone SDK RPC call. state: {res.status()}, Resp: {res.json()}"
                    # )
            elif not res.ok():
                log.error(
                    "Unable to scrap video fragment from Milestone SDK",
                    code=res.status(),
                    err=res.body(),
                )
                raise RetryableError(
                    "Unable to scrape video fragment from Milestone SDK",
                )

            stream_start = dateutil.parser.parse(stream_start).replace(
                tzinfo=tz.tzutc()
            )

            save_stream(res.response, video_path)

            video_metadata = get_metadata_ffprobe(video_path)
            log.info(
                "Milestone Video Metadata",
                metadata=video_metadata,
                filename=video_path,
                attempt=attempt,
            )

            # getting creation timestamp from the ffprobe metadata
            actual_start_time_msec = self._get_video_scrape_actual_start(
                start_time_msec, video_metadata
            )
            actual_start_time_sec = actual_start_time_msec / 1000
            log.debug(
                "Actual start time of the video scrape",
                actual_start=actual_start_time_sec,
            )

            duration, err = video_duration_millis(video_path)
            log.debug("Duration of the video", duration=duration)
            if duration < 0:
                log.warning(
                    "Duration of scrape is 0",
                    duration=duration,
                    err=err,
                    returned_start_utc=actual_start_time_sec,
                )

            calculate_end_sec = actual_start_time_sec + (duration / 1000)
            log.debug(
                "Milestone Video Scrape Details",
                returned_start_epoch=actual_start_time_sec * 1000,
                requested_start_epoch=start_time_msec,
                filename=video_path,
                duration=duration,
                final_end=calculate_end_sec,
            )

            got_interval = VideoInterval(
                start=datetime.datetime.utcfromtimestamp(
                    actual_start_time_sec
                ),
                end=datetime.datetime.utcfromtimestamp(calculate_end_sec),
            )
            req_interval = VideoInterval(
                start=datetime.datetime.utcfromtimestamp(
                    start_time_msec / 1000
                ),
                end=datetime.datetime.utcfromtimestamp(end_time_msec / 1000),
            )
            return VideoResult(
                requested=req_interval,
                interval=got_interval,
                filename=video_path,
                err=err,
            )
        finally:
            log.debug(
                "Milestone video processing time",
                processing_time=time.time() - st,
            )

    def _get_video_scrape_actual_start(
        self, requested_start: float, ffprobe_metadata: typing.Dict
    ) -> float:
        """
        Returns the actual start time(in milliseconds) of the video scrape from the ffprobe metadata.
        If ffprobe metadata is not available, actual start time defaults to the requested one.

        Args:
            requested_start(float): Requested start time of the video scrape in milliseconds
            ffprobe_metadata(dict): all format data and tags coming from ffprobe as a dict

        Returns:
            (float): Actual start time of the video from ffprobe metadata, defaults to requested start
            if not available.
        """
        actual_start_time = requested_start
        m_data = ffprobe_metadata.get("data", None)
        if m_data and "format" in m_data:
            m_data_format = m_data["format"]
            if "tags" in m_data_format:
                creation_time = m_data_format["tags"].get("creation_time")
                if creation_time is not None:
                    log.debug(
                        "Creation time available from ffprobe tags",
                        creation_time=creation_time,
                    )
                    actual_start_time = dateutil.parser.parse(
                        creation_time
                    ).timestamp()
                else:
                    log.debug(
                        "No creation_time in ffprobe tags",
                    )
            else:
                log.debug(
                    "No tags data in ffprobe data",
                )
        else:
            log.debug("No format data in ffprobe output")
        return actual_start_time

    def get_any_cameras_json(self) -> typing.Dict:
        camerasInJson = self.get_all_cameras_json()
        cameraItem = None
        for item in camerasInJson:
            cameraItem = item
            break
        return cameraItem

    def get_all_cameras_json(self) -> typing.List[typing.Dict]:
        cameras = self._milestone_sdk.get_cameras()
        if not cameras.ok():
            raise RetryableError(
                "Could not get CameraItem Details from Milestone",
                err=cameras.status(),
            )

        camerasInJson = cameras.json()
        log.warning("print ms response", json=cameras.json())
        return camerasInJson.get("CameraItems")

    def get_camera_image(
        self, cam_json: typing.Dict, img_time: datetime.datetime
    ) -> typing.Optional[str]:
        camera_guid = cam_json.get("GUID")
        # Better to use latest image than from a random time before
        r_resp = self._milestone_sdk.get_camera_image(camera_guid, None)
        if not r_resp.ok():
            message = (
                "Count not get image from Camera:{0} in milestone".format(
                    camera_guid
                )
            )
            raise FileNotFoundError(message)
        os.makedirs(config.HIP.cameraSyncer["imageStore"], exist_ok=True)
        file_path = osp.join(
            config.HIP.cameraSyncer["imageStore"],
            get_filename_from_cam_details(
                cam_json.get("GUID"), cam_json.get("Name")
            )
            + ".jpeg",
        )
        req = r_resp.response
        with open(file_path, "wb") as f:
            for chunk in req.iter_content():
                f.write(chunk)
        return file_path

    def get_all_servers_info(self) -> typing.List[ServerData]:
        """Returns a list of JSONs. The list must be complete, and must contain
        data for all servers in the ACS/VMS. This data is directly consumed
        by the gateway, and so additional VMS specific processing cannot be done.

        Returns:
            List[Dict]: List of all servers, represented as JSONs
        """
        return self._milestone_sdk.get_all_servers()

    def sync_camera_health_status(self, cam_json: typing.Dict) -> typing.Dict:
        return cam_json
