import time
import typing
from datetime import datetime

import dateutil
import pytz
from flask import Flask
from prometheus_flask_exporter import PrometheusMetrics

import errors
from config import appliance_config as config
from factory.prowatch_sql_factory import get_prowatch_sql_client_ref
from integ.common.runner import AcsEventProcessor
from integ.prowatch_sql.prowatch_sql_acs import log
from integ.prowatch_sql.pw_callback_server import prowatch_event_blueprint
from integ.prowatch_sql.utils.pw_event_list import (
    PROWATCH_SQL_ALARM_BLACKLIST,
    PROWATCH_SQL_EVENT_WHITELIST,
)

FETCH_RECORDS_COUNT = 100
ACS_ALARM_LISTENER_PORT = 9001


class ProwatchSqlEventProcessor(AcsEventProcessor):
    def __init__(self, consumer, evt_server) -> None:
        self.event_consumer = consumer
        self.evt_server = evt_server

        self.pw = get_prowatch_sql_client_ref()
        self._conf = config.HIP.prowatch
        local_tz = datetime.now().astimezone().tzinfo
        self._timezone = self._conf.get("pw_server_timezone", local_tz)
        self._ingest_alarms = self._conf.get("ingest_alarms", False)
        # This returns the datetime specified in the checkpoint file.
        # If LAST_CHECKPOINT key is not there in the state file, we use the deployment start date
        self._last_checkpoint = self.pw.get_last_checkpoint_time()

    def backfill_device(self, dev, start: datetime, end: datetime):
        # backfill not required. its a stateful system
        pass

    def process_alarms(self):
        """
        Processes and returns the alarms(ie hakimo incidents) from the ACS
        """
        self.evt_server.run(
            host="0.0.0.0",
            port=ACS_ALARM_LISTENER_PORT,
            debug=False,
            use_reloader=False,
        )

    def alarm_ack_(self, alarm_json: typing.Dict):
        """
        Sends the alarm acknowledgement and context payloads to the gateway
        for updating the alarms in Hakimo db coming from ACS (Prowatch)
        """
        log.info("Consuming alarm ack from ACS")
        self.event_consumer.consume_acs_alarm_update(alarm_json)

    def process_events(self):
        if not self._ingest_alarms:
            self.process_pw_events()
        else:
            self.process_pw_events_and_alarms()

    def segregate_pw_events_alarms(
        self, pw_data: typing.Dict
    ) -> typing.Tuple[
        typing.Optional[typing.Dict], typing.Optional[typing.Dict]
    ]:
        evnt_data, alarm_data = None, None
        if str(pw_data.get("EVNT_DESCRP")) in PROWATCH_SQL_EVENT_WHITELIST:
            evnt_data = {
                "RID": str(pw_data.get("RID")),
                "EVNT_DESCRP": str(pw_data.get("EVNT_DESCRP")),
                "LOGDEVID": str(pw_data.get("LOGDEVID")),
                "DESCRP": str(pw_data.get("DESCRP")),
                "LOCATION": str(pw_data.get("LOCATION")),
                "PANEL_DESCRP": str(pw_data.get("PANEL_DESCRP")),
                "EVNT_DAT": str(pw_data.get("EVNT_DAT")),
                "CARDNO": str(pw_data.get("CARDNO")),
                "BADGENO": str(pw_data.get("BADGENO")),
                "FNAME": str(pw_data.get("FNAME")),
                "LNAME": str(pw_data.get("LNAME")),
                "MI": str(pw_data.get("MI")),
                "TSTAMP": str(pw_data.get("TSTAMP")),
                "ALARM": str(pw_data.get("ALARM")),
                "REC_DAT": str(pw_data.get("REC_DAT")),
                "PANEL_ID": str(pw_data.get("PANEL_ID")),
            }
        if (pw_alarm_desc := str(pw_data.get("EvDesc2"))) and (
            str(pw_data.get("EvType", "")).lower()
            not in PROWATCH_SQL_ALARM_BLACKLIST
        ):
            alarm_data = {
                "EvDate": str(pw_data.get("EvDate", "")),
                "UID_BIN": str(pw_data.get("UID_BIN")),
                "RID_BIN": str(pw_data.get("RID_BIN")),
                "RespNote": str(pw_data.get("RespNote")),
                "RespDate": str(pw_data.get("RespDate")),
                "EvType": str(pw_data.get("EvType")),
                "EvDesc": str(pw_data.get("EvDesc")),
                "EvDesc2": pw_alarm_desc,
                "isAlarm": True,
                "REC_DAT": str(pw_data.get("REC_DAT")),
            }
        return evnt_data, alarm_data

    def _get_last_event_rec_dat(
        self, pw_raw_data: typing.List[typing.Dict]
    ) -> str:
        if not pw_raw_data or len(pw_raw_data) < 1:
            return str(self._last_checkpoint)

        # The query returns records in ascending order,`
        # so last object is read from the list to update the checkpoint timestamp
        last_evt_ts = None
        tz_obj = pytz.timezone(self._timezone)
        current_time = datetime.now(tz_obj).replace(tzinfo=None)
        for pw_raw_dataum in reversed(pw_raw_data):
            if datetime.fromisoformat(pw_raw_dataum["REC_DAT"]) > current_time:
                log.warning(
                    "Skipping as REC_DAT in the last record is greater than current time",
                    rec_dat=datetime.fromisoformat(pw_raw_dataum["REC_DAT"]),
                    current_time=current_time.isoformat(),
                )
            else:
                log.debug(
                    "Found REC_DAT within current time",
                    rec_dat=pw_raw_dataum["REC_DAT"],
                    current_time=current_time.isoformat(),
                )
                last_evt_ts = pw_raw_dataum["REC_DAT"]
                break

        if last_evt_ts is None:
            log.warning(
                "No valid REC_DAT found in the last record, using last checkpoint",
                last_checkpoint=self._last_checkpoint.isoformat(),
            )
            return self._last_checkpoint.isoformat()
        else:
            return last_evt_ts

    def process_pw_events_and_alarms(self):
        retry_attempt = 0
        while True:
            try:
                new_events_alarms = self.pw.get_new_events_and_alarms(
                    last_checkpoint=self._last_checkpoint,
                    num_records=FETCH_RECORDS_COUNT,
                )
            except errors.RetryableError as err:
                retry_attempt += 1
                if retry_attempt >= 15:
                    log.error(
                        "Failed to get alarms from Prowatch DB despite retries.",
                        error=str(err),
                        retry_attempt=retry_attempt,
                    )
                    raise

                log.warning(
                    "Error while getting alarms from Prowatch DB. Retrying.",
                    error=str(err),
                    retry_attempt=retry_attempt,
                )
                time.sleep(2)
                continue
            else:
                # on success reset any previous retry_attempt
                retry_attempt = 0
            for ev in new_events_alarms:
                try:
                    pw_evnt, pw_alm = self.segregate_pw_events_alarms(ev)
                    if pw_evnt is not None:
                        self.event_consumer.consume_v2(pw_evnt)
                    if pw_alm is not None:
                        self.event_consumer.consume_v2(pw_alm)
                except Exception as e:
                    log.exception(
                        "Event and Alarm Alarm ingestion failed", exc_info=e
                    )
                    break

            last_evt_ts = self._get_last_event_rec_dat(new_events_alarms)
            self.pw.update_last_checkpoint_time(last_evt_ts)
            self._last_checkpoint = dateutil.parser.parse(last_evt_ts)
            log.debug(
                "Relayed new events and alarms",
                count=len(new_events_alarms),
                last_checkpoint=self._last_checkpoint,
            )
            time.sleep(1)

    def process_pw_events(self):
        retry_attempt = 0
        while True:
            try:
                new_events = self.pw.get_new_events(
                    num_records=FETCH_RECORDS_COUNT
                )
            except errors.RetryableError as err:
                retry_attempt += 1
                if retry_attempt >= 15:
                    log.error(
                        "Failed to get alarms from Prowatch DB despite retries.",
                        error=str(err),
                        retry_attempt=retry_attempt,
                    )
                    raise

                log.warning(
                    "Error while getting alarms from Prowatch DB. Retrying.",
                    error=str(err),
                    retry_attempt=retry_attempt,
                )
                time.sleep(2)
                continue
            else:
                # on success reset any previous retry_attempt
                retry_attempt = 0

            successful_events = []
            for ev in new_events:
                try:
                    self.event_consumer.consume_v2(ev)
                    successful_events.append(ev)
                except Exception as e:
                    log.exception("Alarm ingestion failed", exc_info=e)
                    break
            last_evt_ts = self._get_last_event_rec_dat(successful_events)
            self.pw.update_last_checkpoint_time(last_evt_ts)
            self._last_checkpoint = dateutil.parser.parse(last_evt_ts)
            if len(new_events) == len(successful_events):
                log.debug(
                    "Count of new events",
                    count=len(new_events),
                    last_checkpoint=self._last_checkpoint,
                )
            else:
                log.warning(
                    "New events but some failed ingestion",
                    count=len(new_events),
                    sucessful_count=len(successful_events),
                    last_checkpoint=self._last_checkpoint,
                )

            # TODO: remove this after validations
            # successful_alarms = []
            # try:
            #     alms = self.pw.get_event_alarms(successful_events)
            #     for a in alms:
            #         a["isAlarm"] = True
            #         self.event_consumer.consume_v2(a)
            #         successful_alarms.append(a)
            # except Exception as e:
            #     log.error("Error getting alarms from events", exc_info=e)
            # log.debug("Count of alarms", count=len(successful_alarms))
            time.sleep(1)

    def preprocess_alarm(
        self, alarm_payload: typing.Dict, backfill: bool = False
    ) -> typing.Optional[typing.Dict]:
        return alarm_payload


def get_prowatch_event_processor(consumer) -> ProwatchSqlEventProcessor:
    app = Flask(__name__)
    metrics = PrometheusMetrics(app)
    pw_evt_processor = ProwatchSqlEventProcessor(
        consumer=consumer, evt_server=app
    )
    pw_evt_blueprint = prowatch_event_blueprint(pw_evt_processor)
    app.register_blueprint(pw_evt_blueprint, url_prefix="/prowatch/api")

    return pw_evt_processor


# if __name__ == "__main__":
#     app = Flask(__name__)
#     metrics = PrometheusMetrics(app)
#     pwsql = ProwatchSqlEventProcessor(
#         consumer=AlarmsToStdout(), evt_server=app
#     )
#     pw_evt_blueprint = prowatch_event_blueprint(pwsql)
#     app.register_blueprint(pw_evt_blueprint, url_prefix="/prowatch/api")
#     pwsql.process_events()
