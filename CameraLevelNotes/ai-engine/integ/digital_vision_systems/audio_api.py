import time

import structlog

from common_utils.rpc import Rpc

log = structlog.get_logger("hakimo.integ.go2rtc_onvif", module="audio_api")

PLAY_MEDIA_CLIP_MAX_RETRIES = 10


class VisionDetectionSystemsAudioAPI:
    def __init__(self, rpc: Rpc) -> None:
        self.rpc = rpc

    def play_media_clip(self, clip_name: str = "userfile1") -> bool:
        for r in range(PLAY_MEDIA_CLIP_MAX_RETRIES):
            res = self.rpc.get(
                "api/play",
                params={
                    "action": "start",
                    "file": clip_name,
                    "mode": "once",
                },
            )
            if res.ok():
                log.debug("Successfully played media clip.", attempt=r)
                return True

            if res.status_code == 400:
                body = res.body()
                if body is not None:
                    body = body.replace("\n", " ").lower()
                    log.debug(body, attempt=r)
                    time.sleep(8)
                continue

            log.error(
                "Failed to play media clip",
                clip_id=clip_name,
                attempt=r,
                status=res.status_code,
            )
            return False

        if r >= PLAY_MEDIA_CLIP_MAX_RETRIES:
            log.error(
                "A clip is already playing, failed to play media clip despite retries.",
                clip_id=clip_name,
            )
        return False

    def upload_audio(self, audio_file_path: str) -> bool:
        """Play any arbitrary audio specified by audio file path.

        Args:
            audio_file_path (str): Path to the audio file to upload
        """
        files = {
            "upload1": audio_file_path
        }  # Rpc.post will handle opening the file
        res = self.rpc.post(
            payload="",
            path_="cgi-bin/mediaupload",
            params={"idx": 0},
            files=files,
        )
        if not res.ok():
            log.error(
                "Failed to upload audio",
                status=res.status_code,
            )
            return False
        return True

    def upload_and_play_audio(self, audio_file_path: str) -> bool:
        """Upload and play audio file.

        Args:
            audio_file_path (str): Path to the audio file to upload and play
        """
        if not self.upload_audio(audio_file_path):
            log.error(
                "Failed to upload audio file", audio_file_path=audio_file_path
            )
            return False

        if not self.play_media_clip("userfile1"):
            log.error("Failed to play uploaded audio clip")
            return False

        return True


if __name__ == "__main__":
    rpc_obj = Rpc(
        {
            "url": "http://************:8089/",
        }
    )
    client = VisionDetectionSystemsAudioAPI(rpc_obj)

    response = client.upload_audio(
        audio_file_path="/Users/<USER>/Downloads/trespassing.wav"
    )
    print(response)
    response = client.play_media_clip("userfile1")
    print(response)
