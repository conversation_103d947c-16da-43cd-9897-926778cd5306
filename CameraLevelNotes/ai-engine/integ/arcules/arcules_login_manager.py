import typing

import structlog
from requests import Response, Session

from common_utils.login_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from common_utils.request_capsule import RequestCapsule
from common_utils.rpc import RpcException

log = structlog.get_logger("hakimo", module="Arcules Login Manager")


class ArculesLoginManager(LoginManager):
    def __init__(self):
        self.session_state: typing.Optional[str] = None

    def check_session_expired(self, response: Response) -> bool:
        if response.status_code in [401, 403]:
            self.session_state = None
            log.info(
                "Login failure",
                arcules_status_code=response.status_code,
                arcules_resp_json=response.json(),
            )
            return True
        return False

    def login(
        self,
        sess: Session,
        login_info: typing.Dict[str, typing.Any],
        req_capsule: RequestCapsule,
    ) -> RequestCapsule:
        if self.session_state is None:
            # Either does not exist or was invalidated
            payload = {
                "email": login_info["email"],
                "password": login_info["password"],
            }
            res = sess.post(login_info["url"] + "login", json=payload)
            if not res.ok:
                log.error("Failed to login to Arcules API")
                raise RpcException(
                    retryable=True,
                    message="Failed to login to Arcules API: " + res.text,
                )

        token = res.json()["jwtToken"]
        self.session_state = token
        sess.headers.update({"Authorization": f"Bearer {token}"})
        return req_capsule
