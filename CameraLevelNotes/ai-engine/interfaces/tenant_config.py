import json
import re
import typing
from dataclasses import asdict, dataclass, field
from typing import Dict, List

import structlog
from dacite import from_dict

from interfaces.group_priority import GroupPriority
from interfaces.source_systems import ACSSource

log = structlog.get_logger("hakimo", module="tenant_config")


class WebhookIngestionConfig:
    enabled: bool = False
    siteId: typing.Optional[str] = None


@dataclass
class TenantConfig:  # pylint: disable=too-many-instance-attributes
    registerAcsCustomEvents: bool = True
    employeeAnonymizeByCardnum: bool = False
    employeeAddPhoneNumLastName: bool = False
    # Name of the employee property whose value should be used for in UI for display,
    # and which is present in the Employee data obtained from ACS.
    customAcsEvents: dict = field(default_factory=dict)
    alerting_configs: dict = field(default_factory=dict)
    tableauUsername: str = ""
    maxLivestreamCount: int = 10
    hlsEnabled: bool = False
    hlsV2Enabled: bool = False
    servicenowConfig: dict = field(default_factory=dict)
    # Source system configs.
    genetecConfig: dict = field(default_factory=dict)
    lenelOAConfig: dict = field(default_factory=dict)
    lenelOGConfig: dict = field(default_factory=dict)
    prowatch: dict = field(default_factory=dict)
    s2Config: dict = field(default_factory=dict)
    velocityDbConfig: dict = field(default_factory=dict)
    ccureConfig: dict = field(default_factory=dict)
    tdsConfig: dict = field(default_factory=dict)
    source_system_mapping: dict = field(default_factory=dict)
    hideAlarmTypes: List[str] = field(default_factory=list)
    showAlarmTypes: List[str] = field(default_factory=list)
    motionConfig: dict = field(default_factory=dict)
    internalSlackEmail: typing.Optional[str] = None
    checkVectorDb: typing.Optional[bool] = None
    useEnsembleDetector: typing.Optional[bool] = None
    useSegmentor: typing.Optional[bool] = None
    useLLMClassifier: typing.Optional[bool] = None
    useLLMClassifierVehicles: typing.Optional[bool] = None
    useHigherLevelDetector: typing.Optional[bool] = None
    filterStaticObjects: typing.Optional[bool] = None
    filterNonMotionObjects: typing.Optional[bool] = None
    filterShortTracks: typing.Optional[bool] = None
    rescaleBoxes: typing.Optional[bool] = None
    neighbourhoodClassifierRadius: typing.Optional[float] = None
    neighbourhoodClassifierNeonCounts: typing.Optional[int] = None
    useVectorGrouping: typing.Optional[bool] = None
    vectorGroupingTime: typing.Optional[int] = None
    vectorGroupingDist: typing.Optional[float] = None
    multiCameraVectorGrouping: typing.Optional[str] = None
    multiCameraVectorGroupingDist: typing.Optional[float] = None
    addToNeighbouringCameras: typing.Optional[bool] = None
    twilioCallerId: typing.Optional[str] = None
    addToMonitoringQueue: typing.Optional[bool] = True
    # siteArmDelayMins is used for email based arming from alarm.com
    # This would add delay in minutes before it arms the site
    siteArmDelayMins: typing.Optional[int] = 0
    enableCombinedTeacherTrainedModel: typing.Optional[bool] = False
    useReducedDHOThreshold: typing.Optional[bool] = False
    useFaceRecognition: typing.Optional[bool] = False
    allPatches: typing.Optional[bool] = False
    faceRecognitionProfileSize: float = 0.5
    faceRecognitionNBiggestBoxes: int = 5
    skipMLProcessing: typing.Optional[bool] = False
    dontUseEndEvents: typing.Optional[bool] = False
    """
    Following dict is saved for webhookIngestionConfig enable/disable
    webhookIngestionConfig: {
        "enabled": true,
        "siteId": "Y6 SiteId"
    }
    """
    webhookIngestionConfig: dict = field(default_factory=dict)
    # Key to ingest nonDoor events like Panel Not Communicating etc.
    ingestNonDoorEvents: bool = False
    emailReportConfigs: dict = field(default_factory=dict)
    """
    (@TODO: update this)
    oauthConfig dict stores the following 
    oAuthConfig: {}
    """
    oAuthConfig: typing.Optional[dict] = None

    def __post_init__(self):
        self.source_system_mapping: Dict[str, Dict] = {
            ACSSource.PROWATCH.value: self.prowatch,
            ACSSource.S2.value: self.s2Config,
            ACSSource.LENEL_OPENACCESS.value: self.lenelOAConfig,
            ACSSource.GENETEC.value: self.genetecConfig,
            ACSSource.CCURE.value: self.ccureConfig,
            ACSSource.VELOCITY_SQL.value: self.velocityDbConfig,
            ACSSource.TDS.value: self.tdsConfig,
            # keep for backwards compatibility
            "Lenel OnGuard 7.6": self.lenelOGConfig,
            ACSSource.LENEL_ONGUARD.value: self.lenelOGConfig,
            ACSSource.MOTION.value: self.motionConfig,
        }

    def is_write_to_acs_enabled(self, source_system):
        """Return True if writing to ACS is enabled for given source system, else False."""
        try:
            config = self.source_system_mapping[source_system]
            return config.get("writeToAcs", False)
        except KeyError:
            log.warning(
                "Tenant Configuration not defined for source system.",
                source_system=source_system,
            )
            return False

    def to_json(self) -> str:
        return json.dumps(asdict(self))

    @staticmethod
    def from_json(config_str: str):
        return from_dict(TenantConfig, json.loads(config_str))


@dataclass
class AlarmNotifierPolicy:
    disabled: bool = False
    alarm_types: typing.Optional[typing.List[str]] = None
    # Location IDs
    locations: typing.Optional[typing.List[int]] = None
    doors: typing.Optional[typing.List[str]] = None
    notifyEmails: typing.Optional[typing.List[str]] = None
    notifyTextNumbers: typing.Optional[typing.List[str]] = None
    tapThreshold: float = 50

    def __post_init__(self):
        if (
            self.alarm_types is None
            and self.doors is None
            and self.locations is None
        ):
            self.disabled = True
            log.error("Invalid tenant notifier policy", policy=asdict(self))
        if self.notifyEmails is None and self.notifyTextNumbers is None:
            log.warning("Notifier policy with no contact information")

    def to_json(self) -> str:
        return json.dumps((asdict(self)))

    def is_applicable(
        self,
        alarm_type: str,
        location_id: typing.Optional[str],
        door_uuid: typing.Optional[str],
    ) -> bool:
        if self.disabled:
            return False
        alarm_type_applicable = (
            self.alarm_types is None or alarm_type in self.alarm_types
        )
        locations_applicable = (
            self.locations is None or location_id in self.locations
        )
        door_applicable = self.doors is None or door_uuid in self.doors

        return all(
            [alarm_type_applicable, locations_applicable, door_applicable]
        )


@dataclass
class TenantAlarmProcessingConfig:
    enableTailgating: bool = True
    enableUnauthorizedEntryAlarm: bool = True
    tailgatingTAP: int = 70
    useEscortStatus: bool = False
    escortTailgatingTAP: int = 70
    alarmMap: dict = field(default_factory=dict)
    alarmSOP: dict = field(default_factory=dict)
    simulateTailgating: bool = False
    simulateUnauthorizedEntry: bool = False
    notifierPolicies: typing.List[AlarmNotifierPolicy] = field(
        default_factory=list
    )
    locationAlarmAddToPendingOnly: bool = True
    # emails to which motion alert should be sent to.
    motionAlertEmails: List[str] = field(default_factory=list)
    motionAlertPhoneNumbers: List[str] = field(default_factory=list)
    resolveSecurityMotion: bool = False
    resolveNeonSecurityMotion: bool = False
    resolveGeminiSecurityMotion: bool = False

    def to_json(self) -> str:
        return json.dumps(asdict(self))

    @staticmethod
    def from_json(config_str: str):
        return from_dict(TenantAlarmProcessingConfig, json.loads(config_str))

    def get_sop(self, alarm_str: str) -> typing.Optional[typing.Dict]:
        """Return the SOP for a given internal alarm type from the tenant
        config. If not found, return None (which should be assumed to be
        default config)
        """
        sop = self.alarmSOP.get(alarm_str)
        if sop is not None:
            return sop
        for alarm_regex, sop in self.alarmSOP.items():
            if re.fullmatch(
                alarm_regex,
                alarm_str,
            ):
                return sop
        return None


@dataclass
class LLMAlarmAnalyzerConfig:
    enabled: bool = True
    async_processing: bool = False
    analyzeLocationAlarm: bool = True
    resolveRawAlarm: bool = False
    escalateRawAlarm: bool = False
    resolveLocationAlarm: bool = False
    sendInlineVideo: bool = True
    sendInlineFrames: bool = False
    slowDownVideoFlag: bool = False
    temperature: float = 0.1
    updateLocationAlarmTap: bool = False
    usePreviousLocationAlarmExplanation: bool = False


@dataclass
class AlarmProcessingConfig(TenantAlarmProcessingConfig):
    """
    Behavioral Context:
    1. priority: AI-engine uses the configured priority for the alarm
        1. If enableForAllAlarmType is true, then configured priority is applied to all alarms (for the door(s))
        2. If alarm_types are specified in the same config, then priority works only on this list of alarm types (for the door(s))
        3. If alarm_types are NOT specified in the same config, then same as [1]
    2. processAlarmVideo: This enables or disables AI processing on ALL alarm types from the door.
        1. alarm_types are NOT considered here at all
    3. enableForAllAlarmType: Flag to enable priority on all alarm types. Defaults to true
    4. alarm_types: Sub set of tenant's alarm types, on which priority is applied. Notes:
        1. priority computation is applied on these alarm types (see ml_module/tap_generator/__init__.py L147:L158]
        2. request_video config is applied to these alarm types (see <======> Add here <=======>
    5. request_video: Flag to send the video request for the alarm or not
        1. If alarm_types are specified, then this flag is applied on this list
        2. If alarm_types are NOT specified, then this flag is applied to all alarm types (irrespective of enableForAllAlarmType flag)
    6. everbridge_config: For everbridge incident creation. See notion for dict details
    7. zoom_config:  For Zoom Chatbot integration. See notion for dict details
    8. llmAlarmAnalyzerConfig: For LLM Alarm Analyzer
    9. llmEventAnalyzerConfig: For LLM Event Analyzer
    """

    priority: GroupPriority = GroupPriority.default
    processAlarmVideo: bool = True
    enableForAllAlarmType: bool = True
    alarm_types: typing.Optional[typing.List[str]] = None
    request_video: bool = True
    everbridge_config: typing.Optional[Dict] = None
    zoom_config: typing.Optional[Dict] = None
    llmAlarmAnalyzerConfig: typing.Optional[LLMAlarmAnalyzerConfig] = None
    llmEventAnalyzerConfig: typing.Optional[Dict] = None
