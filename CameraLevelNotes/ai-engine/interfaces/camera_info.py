"""Camera info class for representing the camera for various VMS"""

from typing import Any, Dict


class CameraInfo:  # pylint: disable=too-many-instance-attributes
    """CameraInfo class for representing the VMS client camera.
    Must have a camera name
    """

    def __init__(  # pylint: disable=too-many-arguments
        self,
        camera_id: str,
        camera_name: str,
        client_camera_id: str,
        server_url: str,
        camera_timeszone: str,
        raw_camera_info: Dict[str, Any],
        rtsp_url: str,
        integration_type: str,
        cam_site_id: str,
        tenant_id: str,
        livestream_url: str,
    ):
        if not isinstance(camera_name, str) or camera_name == "":
            raise ValueError("Camera Name should be a non-empty string. ")
        self._cam_id = camera_id
        self._cam_name = camera_name
        self._client_camera_id = client_camera_id
        self._server_url = server_url
        self._cam_timezone = camera_timeszone
        self._raw_cam_info = raw_camera_info
        self._rtsp_url = rtsp_url
        self._integration_type = integration_type
        self._cam_site_id = cam_site_id
        self._tenant_id = tenant_id
        self.livestream_url = livestream_url

    @property
    def camera_id(self) -> str:
        """Camera ID property for the camera

        Returns:
            [str]: CameraId for the camera
        """
        return self._cam_id

    @property
    def camera_name(self) -> str:
        """Camera Name property for the camera

        Returns:
            [str]: Camera Name for the Camera
        """
        return self._cam_name

    @property
    def client_camera_id(self) -> str:
        """Client Camera Id property for the camera

        Returns:
            [str]: Client Camera Id for the Camera
        """
        return self._client_camera_id

    @property
    def server_url(self) -> str:
        """URL for the server this camera is from

        Returns:
            [str]:  Server url
        """
        return self._server_url

    @property
    def camera_timezone(self) -> str:
        """Camera Timezone property for the camera

        Returns:
            [str]: Timezone for the camera
        """
        return self._cam_timezone

    @property
    def raw_camera_info(self) -> Dict[str, Any]:
        """Camera Raw info in JSON Format

        Returns:
            [JSON]: Raw info in json format
        """
        return self._raw_cam_info

    @property
    def tenant_id(self) -> str:
        """Camera Tenant id

        Returns:
            [JSON]: Tenant id to which camera belongs
        """
        return self._tenant_id

    @property
    def integration_type(self) -> str:
        """Camera's Integration Type

        Returns:
            [JSON]: Integration type of the camera
        """
        return self._integration_type

    @property
    def cam_site_id(self) -> str:
        """Camera's Site Id

        Returns:
            [JSON]: Site id to which the camera belongs
        """
        return self._cam_site_id

    @property
    def rtsp_url(self) -> str:
        """Camera's RTSP Url

        Returns:
            [JSON]: RTSP URL for the camera
        """
        return self._rtsp_url
