import datetime
import re
import typing

import structlog

from controller import ControllerMap
from controller.alarm.alarm_controller import ALARM_TYPE_MOTION
from interfaces.alarm import (
    ALARM_TYPE_INTEGRATED_MOTION,
    SERVER_DISCONNECTED_ALARM_TYPE,
    Alarm,
)
from models_rds.rds_client import RDSClient

log = structlog.get_logger("hakimo", module="AlarmMapper")


# TODO: Remove these hardcoded tenant ids and drive it via a config for showing all alarms
SHOW_ALARMS_FROM_ALL_DOORS_TENANT_WHITELIST = [
    "robinhoo-0669-11ec-b6fb-9ba27bd86654",
    "godaddy0-00ee-4e26-80d8-cf5cd1a7c61c",
    "elevance-health-7adf71c1da2d43c1",
    "elevance-prod",
]


class AlarmMapper:
    def __init__(self, control_map: typing.Optional[ControllerMap]):
        self._controller = control_map
        if self._controller is not None:
            self._rds_client = RDSClient(self._controller.db)
        self._ag_re_pattern = re.compile(
            self.get_ag_regex(),
            flags=re.IGNORECASE,
        )
        self._open_door_pattern = re.compile(
            self._get_open_door_regex(),
            flags=re.IGNORECASE,
        )

    def get_end_event_regex(self, alarm: Alarm) -> str:
        """Get the regex pattern for the end events for
        the alarm type of a given alarm

        Args:
            alarm ([Alarm]): alarm object for which to return
                end event regex

        Returns:
            alarm_str ([str]): regex for the end event
        """
        internal_alarm_type = self.get_internal_alarm_type(alarm)
        if internal_alarm_type in [
            "Door Forced Open",
            "Door Held Open",
        ]:
            return (
                rf"{internal_alarm_type} (Canceled|Restored)|"
                r"Door Close|Portal restored|Secured|Door Closed Again"
            )
        if internal_alarm_type.startswith("Access Granted"):
            return r"Door Close"
        raise NotImplementedError(f"{internal_alarm_type} not supported.")

    def get_internal_alarm_type_no_config(
        self, alarm_type: str
    ) -> typing.Optional[str]:
        alarm_str: typing.Optional[str] = None
        if self.ag_re_pattern.fullmatch(alarm_type):
            alarm_str = "Access Granted"
        elif self.open_door_pattern.match(alarm_type):
            alarm_str = "Open Door Command Issued"
        # TODO: Make ACS specific classes
        # velocity sql related
        elif alarm_type == "Force":
            return "Door Forced Open"
        elif alarm_type == "DOTL":
            return "Door Held Open"
        # TDS related
        elif alarm_type == "Badge Unauthorised":
            return "Invalid Access Level"
        elif alarm_type == "Door Open Too Long":
            return "Door Held Open"
        # This is S2 related
        elif alarm_type == "Access denied":
            alarm_str = "Invalid Access Level"
        elif alarm_type == "Portal held open":
            alarm_str = "Door Held Open"
        elif alarm_type == "Portal forced open":
            alarm_str = "Door Forced Open"
        # Genetec
        elif alarm_type == "Door Open While Lock Secure":
            alarm_str = "Door Forced Open"
        elif alarm_type == "Door Opened For Too Long":
            alarm_str = "Door Held Open"
        elif alarm_type in (
            "Access Invalid Pin",
            "Access Valid Card Invalid Pin",
            "Access Denied By Access Rule",
            "Access Insufficient Privileges",
            "Access Antipassback Violation",
            "Access Out Of Schedule",
            "Access No Access Rule Assigned",
            "Access Absent Supervisor",
            "Access Escort Required",
            "Access Second Cardholder Required",
            "Access Escort Not Supported",
            "Companion Visitor Denied",
            "Interlock",
            "Access Card And Pin Timeout",
            "Access Valid Card Inactive Pin",
        ):
            alarm_str = "Invalid Access Level"
        elif alarm_type in (
            "Access Unknown Credential",
            "Access Unassigned Credential",
            "Access Expired Credential",
            "Access Stolen Credential",
            "Access Lost Credential",
            "Access Inactive Credential",
            "Access Inactive Person",
        ):
            alarm_str = "Invalid Badge"
        # Avigilon ACM
        elif alarm_type == "Forced door":
            alarm_str = "Door Forced Open"
        elif alarm_type == "Door held open":
            alarm_str = "Door Held Open"
        elif alarm_type == "Local grant":
            alarm_str = "Access Granted"
        elif alarm_type in (
            "Valid card at unauthorized reader",
            "Invalid Card Schedule",
        ):
            return "Invalid Access Level"
        elif alarm_type in (
            "Invalid card format",
            "Deactivated Card Attempt",
            "Unknown card",
        ):
            return "Invalid Badge"
        # Legacy
        elif alarm_type == "DOOR_FORCED":
            alarm_str = "Door Forced Open"
        elif alarm_type == "DOOR_HELD_OPEN":
            alarm_str = "Door Held Open"
        return alarm_str

    def get_internal_alarm_type(self, alarm: Alarm) -> str:
        return self.get_mapped_alarm_type(alarm.alarm_type, alarm.tenant_id)

    def get_end_event_type(self, alarm: Alarm) -> typing.Optional[str]:
        """Returns end event alarm_type_id.

        Args:
            alarm (Alarm):
            rds_client (RDSClient):

        Returns:
            typing.Optional[str]: Alarm Type ID for end event.
        """
        try:
            alarm_str: typing.Optional[str] = self.get_end_event_regex(alarm)
        except NotImplementedError:
            alarm_str = None
        if alarm_str is None:
            return None

        return self._rds_client.get_alarm_type_id_from_alarm_type(
            alarm_str, regex=True
        )

    def get_end_event_time(
        self, alarm: Alarm, end_event_type: str
    ) -> typing.Optional[datetime.datetime]:
        """Get end event time if it exists for the given alarm.

        Args:
            alarm (Alarm):
            rds_client (RDSClient):

        Returns:
            typing.Optional[datetime.datetime]: Canceling event time
        """
        if alarm.door_uuid is None:
            return None

        log.debug(
            "Checking for canceled/restored event",
            event_type=end_event_type,
        )
        end_events = self._rds_client.get_alarm_times_at_door(
            alarm.door_uuid,
            alarm.alarm_time,
            alarm.processing_end_time_utc or alarm.video_end_time_utc,
            alarm_type_id=end_event_type,
        )
        if not end_events:
            return None

        log.debug("Found canceled/restored event", end_events=end_events)
        first_cancel_time = end_events[0]
        return first_cancel_time

    @staticmethod
    def get_ag_regex() -> str:
        """Get regex that identifies access granted events

        Returns:
            [str]: regex for access granted events
        """
        return (
            r".*((access(\s|_)granted)|"
            r"(granted(\s|_)(access|no entry))|"
            r"(access not completed)).*|"
            r"access"
        )

    @property
    def ag_re_pattern(self) -> re.Pattern:
        """Get compiled regex pattern to identify AG events
        Returns:
            [str]: regex for access granted events
        """
        return self._ag_re_pattern

    @staticmethod
    def _get_open_door_regex() -> str:
        """Get regex that identifies access granted events

        Returns:
            [str]: regex for access granted events
        """
        return r"(.*open door command issued.*)"

    @property
    def open_door_pattern(self) -> re.Pattern:
        """Get compiled regex pattern to identify open door command events
        Returns:
            [str]: regex for open door command issued events
        """
        return self._open_door_pattern

    def should_display(self, alarm: Alarm) -> bool:
        # Display alarm if no access to Database
        dcm_cond = True
        if self._controller is not None:
            dcm_cond = bool(
                self._controller.door_camera_params.is_door_camera_mapping_present(
                    alarm.door_uuid, alarm.tenant_id
                )
                or (
                    re.match(
                        "|".join(
                            [
                                "Camera defective",
                                SERVER_DISCONNECTED_ALARM_TYPE,
                                ALARM_TYPE_MOTION,
                                ALARM_TYPE_INTEGRATED_MOTION,
                            ]
                        ),
                        alarm.alarm_type,
                        flags=re.IGNORECASE,
                    )
                )
                or alarm.source_system == "ADT"
                # All alarms regardless of mapping
                or alarm.tenant_id
                in SHOW_ALARMS_FROM_ALL_DOORS_TENANT_WHITELIST
            )

        camera_armed_cond = True
        # TODO: Remove this after validations. We do not show/hide alarms based on camera.is_armed status anymore.
        # If cmaera is camera, the alarms won't be ingested in the first place
        # if (
        #     self._controller is not None
        #     and alarm.source_entity_type == SourceEntityType.CAMERA
        # ):
        #     assert (
        #         alarm.source_entity_id
        #     ), "Source entity ID of alarm must be set if source is camera"
        #     cam = self._controller.camera.get_camera_by_id(
        #         alarm.source_entity_id, alarm.tenant_id
        #     )
        #     if cam is not None and not cam.is_armed:
        #         camera_armed_cond = False

        alarm_type_cond = not re.fullmatch(
            "|".join(
                [
                    self.get_ag_regex(),
                    "Door Open",
                    ".*Close",
                    ".*Canceled",
                    ".*Restored",
                    ".*Communications.*",
                    ".*Generic Event.*",
                    ".*Line Error Active.*",
                    ".*Canceled Line Error.*",
                    ".*Local I/O Executed Function List.*",
                    ".*Relay Contact (Deactivated|Activated).*",
                    ".*Request to Exit - Door (Not )?Used.*",
                    ".*Scheduler Action Executed.*",
                    ".*Video Overflow Started.*",
                    ".*Relocked.*",
                    ".*Reader Mode (Card Only|Unlocked).*",
                    "RQE",
                    "Start of Short Circuit",
                    "End of Short Circuit",
                    "Start of Line Break",
                    "End of Line Break",
                ]
            ),
            alarm.alarm_type,
            flags=re.IGNORECASE,
        )
        if self._controller is not None:
            tenant_config = self._controller.tenant.get_config(alarm.tenant_id)
            if tenant_config is not None:
                if re.fullmatch(
                    "|".join(tenant_config.hideAlarmTypes),
                    alarm.alarm_type,
                    flags=re.IGNORECASE,
                ):
                    alarm_type_cond = False
                elif re.fullmatch(
                    "|".join(tenant_config.showAlarmTypes),
                    alarm.alarm_type,
                    flags=re.IGNORECASE,
                ):
                    alarm_type_cond = True

        return dcm_cond and camera_armed_cond and alarm_type_cond

    def get_mapped_alarm_type(self, alarm_type: str, tenant_id: str) -> str:
        """Returns the internal alarm type mapped from the given type for a tenant.
        Falls back to tenant config if default mapping is not found.
        Args:
            alarm_type (str): The external alarm type to map.
            tenant_id (str): Tenant ID used to fetch configuration.
        Returns:
            str: Mapped internal alarm type or original alarm type if no mapping exists.
        """
        alarm_str = self.get_internal_alarm_type_no_config(alarm_type)
        if alarm_str is not None:
            return alarm_str
        if self._controller is None:
            alarm_str = alarm_type
        else:
            alarm_config = self._controller.tenant.get_config(
                tenant_id, "alarmProcessingConfig"
            )
            # alarm config does not exist
            if not alarm_config:
                alarm_str = alarm_type
            else:
                alarm_map = alarm_config.alarmMap
                alarm_str = alarm_map.get(alarm_type, alarm_type)
        return alarm_str
