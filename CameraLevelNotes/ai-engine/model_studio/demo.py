from pathlib import Path

import gradio as gr

# from ml_module.llm_alarm_analyzer.gemini_analyzer import LLMAlarmAnalyzer
from model_studio.llm_alarm_analyzer import LLMAlarmAnalyzer

gr.set_static_paths("/app/model_studio/static")
# analyzer = LLMAlarmAnalyzer(
#     api_key=Path("/secrets/recommend_demo").read_text()
# )
analyzer = LLMAlarmAnalyzer(api_key_or_path="/secrets/recommend_demo")
example_dir = Path("/app/model_studio/examples")
example_name = [
    example.name.replace("_", " ") for example in example_dir.iterdir()
]
examples = [
    [example / "alarm_video.mp4", (example / "sop.txt").read_text(), ""]
    for example in example_dir.iterdir()
]


def analyze_alarm_video(video_path, sop_text, datetime_input):
    result = analyzer.analyze_alarm(video_path, sop_text, datetime_input)
    return result or "Analysis failed. Please try again."


# Create the Gradio interface
with gr.<PERSON><PERSON>(
    title="Hakimo AI Video Analyzer", css="footer {visibility: hidden}"
) as demo:
    gr.HTML("""
        <div style="display: flex; justify-content: center; align-items: center; margin-bottom: 1rem">
            <img src="/gradio_api/file=model_studio/static/hakimo-full-dark.svg" alt="Hakimo Logo" style="height: 40px;">
        </div>
    """)
    gr.HTML("""
        <div style="text-align: center; margin-bottom: 1rem">
            <h2>Hakimo AI Operator</h2>
        </div>
    """)

    with gr.Row(equal_height=True):
        # Input components
        with gr.Column(scale=5):
            video_input = gr.Video(
                label="Upload Alarm Video", height=300, format="mp4"
            )
        # Output component
        with gr.Column(scale=3):
            datetime_input = gr.DateTime(
                label="Alarm Date and Time",
                value=None,  # Sets default to current date/time
                interactive=True,
            )
            sop_input = gr.Textbox(
                label="Standard Operating Procedure (SOP)",
                placeholder="Enter the SOP text here...",
                lines=5,
            )
    with gr.Row():
        with gr.Column():
            output_text = gr.Textbox(
                label="Analysis Result",
                lines=4,
            )
            analyze_button = gr.Button("Analyze Video")
    with gr.Row():
        example_dataset = gr.Examples(
            examples,
            inputs=[video_input, sop_input, output_text],
            example_labels=example_name,
            label="Scenarios",
            examples_per_page=30,
        )

    # Connect the components
    analyze_button.click(
        fn=analyze_alarm_video,
        inputs=[video_input, sop_input, datetime_input],
        outputs=[output_text],
    )

if __name__ == "__main__":
    demo.launch(
        allowed_paths=["hakimo-full.svg"],
        server_name="0.0.0.0",
        favicon_path="model_studio/static/favicon.ico",
    )
