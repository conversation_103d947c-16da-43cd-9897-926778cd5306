import datetime
import os
import os.path as osp
import typing

import cv2
import numpy as np
import structlog

import controller as ctrl
from common_utils.s3_utils import upload_file_to_s3
from common_utils.typing_helpers import POINT
from config import backend_config as config
from interfaces.alarm import Alarm, AlarmState
from interfaces.camera_details import CameraChangeStatus, CameraStatus
from interfaces.source_systems import SourceEntityType
from ml_module.camera_health_monitoring import LinearityImageQuality
from ml_module.camera_health_monitoring.base import ImageChangeDetector
from ml_module.camera_health_monitoring.gemini_compare import GeminiCompare
from models_rds.cameras import Cameras
from models_rds.rds_client import RDSClient

log = structlog.get_logger("hakimo", module="Angle Change Detection")


# pylint: disable=too-many-instance-attributes
class AngleChangeDetector(ImageChangeDetector):
    def __init__(
        self,
        controller: ctrl.ControllerMap,
        rds_client: RDSClient,
        old: bool = False,
    ):
        if old:
            self._max_features = 500
            # self._good_match_prcnt = 0.50
            self._rel_match_thresh = 0.7
            self._img_border = 0.1
            self._rel_change_tolerance = 0.05
            self._percentage_keypoint_threshold = 0.9
            self._debug = False
            self._output_folder = "/home/<USER>/angle_change/rh_full"
            if self._debug:
                os.makedirs(self._output_folder, exist_ok=True)
            self._quality_check = config.HAIE.camHealthMonitoring["IQACheck"]
            if self._quality_check:
                self._linearityiqa = LinearityImageQuality()
        else:
            self._angle_change_compare = GeminiCompare()
            self._fake_vid_duration = 5
            self._email_cache = []
        self._old = old
        super().__init__(controller, rds_client, by_hour=not old)
        self._special_cameras = {}

    def _get_special_cameras(self, tenant_id: str):
        if tenant_id not in self._special_cameras:
            all_locations = (
                self._controller.locations.get_all_tenant_locations(
                    [tenant_id]
                )
            )
            admin_locations = [
                x for x in all_locations if x.name == "Admin Location"
            ]
            assert len(admin_locations) == 1
            special_location_id = admin_locations[0].id

            all_cameras = self._controller.camera.get_all_cameras_for_tenant(
                tenant_id
            )
            special_cameras = []
            for camera in all_cameras:
                if camera.location_id == special_location_id:
                    special_cameras.append(camera)
            if len(special_cameras) > 1:
                log.warning(
                    "More than one admin location camera found!",
                    tenant_id=tenant_id,
                    camera_ids=[x.uuid for x in special_cameras],
                )
                special_cameras = special_cameras[:1]
            self._special_cameras[tenant_id] = special_cameras

    def _dispatch_comparison(
        self,
        cam: Cameras,
        comparison_result: CameraChangeStatus,
        reason: str,
        tenant_id: str,
        image_path: str,
    ):
        if comparison_result == CameraChangeStatus.NO_KEYPOINTS:
            log.info("No keypoints in image!")
        elif comparison_result == CameraChangeStatus.NOT_ENOUGH_KEYPOINTS:
            log.info("Not enough keypoint matches to check angle change!")
        elif comparison_result == CameraChangeStatus.NOT_ENOUGH_QUALITY:
            log.info("Not enough quality in images to check angle change!")
        elif comparison_result == CameraChangeStatus.ANGLE_CHANGE:
            log.warning(
                "Change in camera angle detected!",
                camera_name=cam.name,
                camera_id=cam.uuid,
                tenant_id=cam.tenant_id,
            )
        elif comparison_result == CameraChangeStatus.NO_ANGLE_CHANGE:
            log.info("No angle change detected!")
        elif comparison_result == CameraChangeStatus.CV_ERROR:
            log.info("Error in Angle calculation module!")
        if not self._old:
            if comparison_result == CameraChangeStatus.ANGLE_CHANGE:
                self._report_camera_angle_change(cam, reason, image_path)

    def detect_change(
        self, image_path_1: str, image_path_2: str
    ) -> typing.Tuple[CameraChangeStatus, str]:
        if self._old:
            return self._detect_change_old(image_path_1, image_path_2)
        res = self._angle_change_compare.ask_gemini(image_path_1, image_path_2)
        if res[0]:
            return CameraChangeStatus.NO_ANGLE_CHANGE, "No change"
        log.info(
            "Found change in camera angle",
            reason=res[1],
            image_path_1=image_path_1,
            image_path_2=image_path_2,
        )
        return CameraChangeStatus.ANGLE_CHANGE, res[1]

    def _detect_change_old(
        self, image_path_1: str, image_path_2: str
    ) -> typing.Tuple[CameraChangeStatus, str]:
        image_1 = cv2.imread(image_path_1)
        image_2 = cv2.imread(image_path_2)
        reason = "Not available"
        if image_1 is None:
            log.error("Corrupt reference image")
            return CameraChangeStatus.CORRUPT_IMAGE, reason
        if image_2 is None:
            log.error("Corrupt target image")
            return CameraChangeStatus.CORRUPT_IMAGE, reason
        if image_1.shape != image_2.shape:
            log.warning(
                "Cannot compare images of different resolution",
                image_1_resolution=image_1.shape,
                image_1_path=image_path_1,
                image_2_resolution=image_2.shape,
                image_2_path=image_path_2,
            )
            return CameraChangeStatus.DIFF_SIZE, reason
        if self._quality_check:
            image_quality = self._get_image_quality(image_1, image_2)
            if (
                image_quality
                < config.HAIE.camHealthMonitoring["LINEARITY_IQA_THRESHOLD"]
            ):
                return CameraChangeStatus.NOT_ENOUGH_QUALITY, reason
        keypoints1, descriptors1 = self._get_kp_descriptors(image_1)
        keypoints2, descriptors2 = self._get_kp_descriptors(image_2)
        if not keypoints1 or not keypoints2:
            return CameraChangeStatus.NO_KEYPOINTS, reason
        matches = self._match_keypoints(descriptors1, descriptors2)

        # Extract location of good matches
        points1 = np.zeros((len(matches), 2), dtype=np.float32)
        points2 = np.zeros((len(matches), 2), dtype=np.float32)

        for idx, match in enumerate(matches):
            points1[idx, :] = keypoints1[match.queryIdx].pt
            points2[idx, :] = keypoints2[match.trainIdx].pt
        # Find homography
        try:
            h, _ = cv2.findHomography(points1, points2, cv2.USAC_MAGSAC)
        except cv2.error:
            return CameraChangeStatus.CV_ERROR, reason
        if h is None or len(matches) <= 10:
            ret = CameraChangeStatus.NOT_ENOUGH_KEYPOINTS
        # Use homography
        else:
            ret = self._check_angle_change(h, image_1)
        self._dump_debug_data(
            ret,
            image_1,
            keypoints1,
            image_2,
            keypoints2,
            matches,
            osp.splitext(osp.basename(image_path_1))[0],
        )

        return ret, reason

    def _get_image_quality(
        self,
        image_ref: np.ndarray,
        image_tgt: np.ndarray,
    ) -> float:
        """Given two images, calculate their quality with the Linearity IQA
        Model and return the lesser of the two quality values.
        """
        result = self._linearityiqa.predict(
            [
                cv2.cvtColor(image_ref, cv2.COLOR_BGR2RGB),
                cv2.cvtColor(image_tgt, cv2.COLOR_BGR2RGB),
            ]
        )
        min_quality = np.min(result)
        return min_quality

    def _match_keypoints(
        self, descriptors1: np.ndarray, descriptors2: np.ndarray
    ) -> typing.List[cv2.DMatch]:
        """Given two sets of descriptors, match them and return the
        matches satisfying some criteria (currently implements returning the top
        x% of matches found)

        Args:
            descriptors1 ([np.ndarray])
            descriptors2 ([np.ndarray])

        Returns:
            typing.List[cv2.DMatch]: List of matches found
        """
        # Match features.
        bf = cv2.BFMatcher()
        all_matches = bf.knnMatch(descriptors1, descriptors2, k=2)
        matches = []
        for match in all_matches:
            if len(match) != 2:
                continue
            m, n = match
            if m.distance < self._rel_match_thresh * n.distance:
                matches.append(m)
        # matcher = cv2.DescriptorMatcher_create(
        #     cv2.DESCRIPTOR_MATCHER_BRUTEFORCE_HAMMING
        # )
        # matches = matcher.match(descriptors1, descriptors2, None)

        # # Sort matches by score
        # matches.sort(key=lambda x: x.distance, reverse=False)
        # # Remove not so good matches
        # numGoodMatches = int(len(matches) * self._good_match_prcnt)
        # matches = matches[:numGoodMatches]
        return matches

    # pylint: disable=too-many-arguments
    def _dump_debug_data(
        self,
        ret: typing.Optional[bool],
        image_1: np.ndarray,
        keypoints1: typing.Sequence[cv2.KeyPoint],
        image_2: np.ndarray,
        keypoints2: typing.Sequence[cv2.KeyPoint],
        matches: typing.Sequence[cv2.DMatch],
        filename: str,
    ):
        """Dumps debugging data and visualizations out to disk

        Args:
            ret (typing.Optional[bool]): Output of angle change detection
            image_1 (np.ndarray): Reference image
            keypoints1 (typing.Sequence[cv2.KeyPoint]): Keypoints
            image_2 (np.ndarray): Comparison image
            keypoints2 (typing.Sequence[cv2.KeyPoint]): Keypoints of 2nd image
            matches (typing.Sequence[cv2.DMatch]): Matched keypoints
        """
        if self._debug:
            imMatches = cv2.drawMatches(
                image_1, keypoints1, image_2, keypoints2, matches, None
            )
            if ret is None:
                color = (255, 0, 0)
            elif ret:
                color = (0, 0, 255)
            else:
                color = (0, 255, 0)
            with np.printoptions(precision=3, suppress=True):
                imMatches = cv2.putText(
                    imMatches,
                    f"Has changed: {ret}",
                    (10, int(0.2 * imMatches.shape[0])),
                    cv2.FONT_HERSHEY_COMPLEX,
                    0.7,
                    color,
                    thickness=2,
                )
            cv2.imwrite(
                osp.join(
                    self._output_folder,
                    f"matches_{ret}_{filename}.jpg",
                ),
                imMatches,
            )

    def _check_angle_change(self, h: np.ndarray, image: np.ndarray) -> bool:
        """Checks whether a given homography results in a significant
        change in the view of the camera. It takes the four corners of the
        camera image, applies the homography to each of the four points,
        and then is able to check the relative and absolute difference
        of the projected points. If this exceeds a threshold, the angle
        is deemed to have changed, and True is returned, else False

        Args:
            h (np.ndarray): Homography matrix 3x3
            image (np.ndarray)

        Returns:
            [bool]: True if angle change detected, False otherwise
        """
        check_points = np.array(
            [
                (0, 0),
                (0, image.shape[0]),
                (image.shape[1], image.shape[0]),
                (image.shape[1], 0),
            ]
        )
        tolerance = self._rel_change_tolerance * np.array(
            image.shape[:2][::-1]
        )
        count = 0
        for point in check_points:
            out_point = np.array(self.apply_homography(h, point))
            if np.any(np.abs(out_point - point) > tolerance):
                count += 1
        percentage_keypoints_changed = count / len(check_points)
        if percentage_keypoints_changed >= self._percentage_keypoint_threshold:
            return CameraChangeStatus.ANGLE_CHANGE
        return CameraChangeStatus.NO_ANGLE_CHANGE

    def _get_kp_descriptors(
        self, image: np.ndarray
    ) -> typing.Tuple[typing.List[cv2.KeyPoint], np.ndarray]:
        """Get keypoints and feature descriptors for a given image

        Args:
            image (np.ndarray):

        Returns:
            typing.Tuple[typing.List[cv2.KeyPoint], np.ndarray]: List of keypoints
            and the corresponding descriptors
        """
        imGray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # kp_detector = cv2.ORB_create(self._max_features)
        kp_detector = cv2.AKAZE_create()
        kp, desc = kp_detector.detectAndCompute(imGray, None)
        keypoints = []
        descriptors = []
        if kp is None or desc is None:
            return keypoints, descriptors
        for kp, descr in zip(kp, desc):
            if (
                self._img_border * image.shape[0]
                <= kp.pt[0]
                <= (1 - self._img_border) * image.shape[0]
            ) and (
                self._img_border * image.shape[1]
                <= kp.pt[1]
                <= (1 - self._img_border) * image.shape[1]
            ):
                keypoints.append(kp)
                descriptors.append(descr)
        descriptors = np.array(descriptors, dtype=np.uint8)
        return keypoints, descriptors

    def apply_homography(self, h: np.ndarray, pt: POINT) -> POINT:
        """Given a homography matrix and a 2D point in the image
        apply the homography and return the transformed 2D point

        Args:
            h (np.ndarray): Homography matrix
            pt (POINT)

        Returns:
            POINT: Transformed point
        """
        point = np.array(pt, dtype=np.uint32)
        point = np.append(point, [1])
        point = np.matmul(h, point)
        point[:2] /= point[2]
        return point[0], point[1]

    def _get_video_s3_url_from_image(
        self, image_path: str, tenant_id: str
    ) -> str:
        video_path = image_path.replace(".jpg", ".mp4")
        fourcc = cv2.VideoWriter_fourcc(*"avc1")
        image = cv2.imread(image_path)
        height, width, _ = image.shape
        video_writer = cv2.VideoWriter(
            video_path,
            fourcc,
            1,
            (width, height),
        )
        for _ in range(self._fake_vid_duration):
            video_writer.write(image)
        video_writer.release()
        s3_file = os.path.join(
            "camera_angle_change",
            tenant_id,
            datetime.datetime.now().strftime("%Y-%m-%d"),
            os.path.basename(video_path),
        )
        upload_file_to_s3(
            self._s3_bucket,
            s3_file,
            video_path,
        )
        os.remove(video_path)
        return f"https://{self._s3_bucket}.s3.{config.HAIE.AWS_S3_REGION}.amazonaws.com/{s3_file}"

    def _publish_alarm(
        self,
        alarm_time: datetime.datetime,
        cam: Cameras,
        url: str,
        reason: str,
        camera_name: str,
        camera_uuid: str,
    ):
        alarm = Alarm(
            **{
                "alarm_type": "Camera defective",
                "alarm_time": alarm_time,
                "source_id": "/".join(
                    [
                        "derived",
                        camera_uuid,
                        CameraStatus.DISCONNECTED.name,
                        alarm_time.strftime("%Y-%m-%d %H:%M:%S"),
                    ]
                ),
                "source_system": cam.integration_type,
                "tenant_id": cam.tenant_id,
                "video_path": url,
                "state": AlarmState.PROCESSED,
                "processing_start_timestamp_utc": alarm_time,
                "processing_end_timestamp_utc": alarm_time
                + datetime.timedelta(seconds=self._fake_vid_duration),
            }
        )
        alarm.source_entity_type = SourceEntityType.CAMERA
        alarm.source_entity_id = cam.uuid
        alarm.display = True
        raw_alarm_uuid = self._rds_client.write_update_raw_alarms(alarm)
        self._rds_client.update_raw_alarms_with_video_metadata(
            raw_alarm_uuid,
            alarm_time,
            alarm_time + datetime.timedelta(seconds=self._fake_vid_duration),
            video_path=url,
        )
        self._controller.alarm_media.add_media(
            raw_alarm_uuid, url, media_type="video"
        )
        self._controller.alarm.add_comment(
            alarm.alarm_uuid,
            alarm.tenant_id,
            f"Camera '{camera_name}' angle has been changed. {reason}",
        )
        self._controller.alarm.update_tap(alarm.alarm_uuid, cam.tenant_id, 90)
        angle_change_alarm = self._rds_client.get_alarm_object(
            raw_alarm_uuid, cam.tenant_id
        )
        assert angle_change_alarm is not None
        self._controller.alarm.mark_alarm_pending(angle_change_alarm)

    def _report_camera_angle_change(
        self, cam: Cameras, reason: str, image_path: str
    ):
        """Once a camera is found to be disconnected, generate an alarm
        with necessary details to be inserted into RDS,
        update the status of the camera and add a comment on the alarm
        with the camera name

        Args:
            cam (Cameras): camera ORM object
        """
        alarm_time = datetime.datetime.utcnow()
        self._get_special_cameras(cam.tenant_id)
        original_cam_name = cam.name
        original_cam_uuid = cam.uuid
        if len(self._special_cameras[cam.tenant_id]) > 0:
            cam = self._special_cameras[cam.tenant_id][0]
        url = self._get_video_s3_url_from_image(image_path, cam.tenant_id)
        self._publish_alarm(
            alarm_time, cam, url, reason, original_cam_name, original_cam_uuid
        )
        self._email_cache.append(
            (original_cam_name, reason, str(alarm_time), image_path)
        )

    def check_camera_change(self, tenant_id: str):
        super().check_camera_change(tenant_id)
        cam_names, reasons, image_paths = [], [], []
        alarm_time_to_use = None
        for cam_name, reason, alarm_time, image_path in self._email_cache:
            if alarm_time_to_use is None:
                alarm_time_to_use = alarm_time
            cam_names.append(cam_name)
            reasons.append(reason)
            image_paths.append(image_path)

        if not cam_names:
            return
        batch_size = 35
        for i in range(0, len(cam_names), batch_size):
            batch_cam_names = cam_names[i : i + batch_size]
            batch_reasons = reasons[i : i + batch_size]
            batch_images = image_paths[i : i + batch_size]

            self._send_report_email(
                batch_cam_names,
                batch_reasons,
                alarm_time_to_use,
                batch_images,
                tenant_id,
            )
        self._email_cache = []

    def _send_report_email(
        self,
        camera_names: typing.List[str],
        reasons: typing.List[str],
        alarm_time: str,
        images: typing.List[str],
        tenant_id: str,
    ):
        mailing_list = None
        tenant_config = self._controller.tenant.get_config(tenant_id)
        if tenant_config:
            if "DEVICE_HEALTH" in tenant_config.alerting_configs:
                if (
                    "mailing_list"
                    in tenant_config.alerting_configs["DEVICE_HEALTH"]
                ):
                    mailing_list = tenant_config.alerting_configs[
                        "DEVICE_HEALTH"
                    ]["mailing_list"]

        if mailing_list:
            self._mail_alerter.send_camera_angle_change_mail_alert(
                tenant_id,
                camera_names,
                alarm_time,
                reasons,
                mailing_list,
                images,
            )


if __name__ == "__main__":
    rds_clnt = RDSClient()
    detector = AngleChangeDetector(
        ctrl.ControllerMap(rds_clnt.db_adapter), rds_clnt, False
    )
    detector.check_camera_change("elevance-prod")
    # detector._get_special_cameras("elevance-prod")
    # cam = detector._special_cameras["elevance-prod"][0]
    # detector._report_camera_angle_change(cam, "hi there", "./cat.jpg")
