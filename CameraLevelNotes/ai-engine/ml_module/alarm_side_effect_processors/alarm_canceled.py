import typing

import structlog

import controller as ctrl
from common_utils.typing_helpers import NUMBER
from interfaces.alarm import Alarm, AlarmState
from interfaces.alarm_mapper import AlarmMapper
from interfaces.tags import Tags
from interfaces.tenant_config import AlarmProcessingConfig
from ml_module.alarm_side_effect_processors.base import BaseSideEffectHandler
from ml_module.ml_interfaces.service_response import MLServiceResponse

log = structlog.get_logger("hakimo", module="Alarm Canceled Side Effect")


class AlarmCanceledSideEffect(BaseSideEffectHandler):
    def __init__(self, controller: ctrl.ControllerMap):
        self._controller = controller
        self._alarm_mapper = AlarmMapper(self._controller)

    def handle_side_effect(
        self,
        processing_config: typing.Optional[AlarmProcessingConfig],
        alarm: Alarm,
        new_tap: NUMBER,  # pylint: disable=unused-argument
        new_tags: Tags,  # pylint: disable=unused-argument
        old_tap: typing.Optional[NUMBER],  # pylint: disable=unused-argument
        old_tags: typing.Optional[Tags],  # pylint: disable=unused-argument
        new_state: AlarmState,  # pylint: disable=unused-argument
        ml_service_response: typing.Optional[MLServiceResponse] = None,  # pylint: disable=unused-argument
    ):
        log.info("Inside Alarm Cancel Side Effect", alarm_id=alarm.alarm_uuid)
        if (
            self._alarm_mapper.get_internal_alarm_type(alarm)
            != "Alarm Canceled"
        ) or (alarm.door_uuid is None):
            return
        last_alarms = self._controller.alarm.get_last_n_alarms_before(
            alarm.door_uuid, alarm.alarm_time, self_alarm_uuid=alarm.alarm_uuid
        )
        if len(last_alarms) != 1:
            log.error(
                "Wrong number of previous alarms for alarm canceled",
                num_alarms=len(last_alarms),
            )
            return
        last_alarm_type = self._controller.alarm_types.get_alarm_type_name_from_alarm_type_id(
            last_alarms[0].alarm_type_id
        )
        if processing_config is None:
            return
        sop = processing_config.get_sop(last_alarm_type)
        if sop is None:
            # Do not do anything if not specified
            return
        if (
            self._alarm_mapper.get_mapped_alarm_type(
                str(sop.get("resolvingAlarm")), alarm.tenant_id
            )
            != "Alarm Canceled"
        ):
            # Only reprocess if alarm canceled is the resolving alarm
            return
        # Reprocess the last alarm that came in
        log.info(
            "Reprocessing previous alarm due to Alarm Canceled",
            alarm_id=last_alarms[0].uuid,
        )
        self._controller.alarm.transition_alarm(
            last_alarms[0].uuid, alarm.tenant_id, AlarmState.UNPROCESSED
        )
