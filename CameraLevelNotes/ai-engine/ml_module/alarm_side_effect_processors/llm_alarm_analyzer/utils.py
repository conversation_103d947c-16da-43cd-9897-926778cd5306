import subprocess
import typing
from datetime import datetime, timedelta

import cv2
import numpy as np

import controller as ctrl
from common_utils.time_utils import (
    timedelta_to_str,
)
from common_utils.video_utils import get_video_fps
from controller.alarm.alarm_filters import AlarmFilters
from interfaces.alarm import Alarm
from interfaces.alarm_mapper import AlarmMapper
from interfaces.entities.entity import Entity
from interfaces.tags import Tags
from ml_module.tap_generator._unauthorized_entries.utils import (
    deduplicate_list_with_employee_ids,
)
from ml_module.visualization.drawing_utils import draw_doors
from models_rds.rds_client import RDSClient


class AlarmEventHandler:
    def __init__(self, controller: ctrl.ControllerMap, rds_client: RDSClient):
        self._controller = controller
        self._rds_client = rds_client

    @staticmethod
    def get_split_times(
        ag_times: typing.List[datetime],
        event_times: typing.List[datetime],
        alarm: Alarm,
    ):
        if (
            alarm.video_start_time_utc - alarm.video_end_time_utc
        ).total_seconds() < 60:
            return False, [
                (
                    alarm.video_start_time_utc,
                    alarm.video_end_time_utc,
                )
            ]
        all_times = sorted(ag_times + event_times)
        return True, [
            (
                alarm.video_start_time_utc + timedelta(seconds=st),
                alarm.video_start_time_utc + timedelta(seconds=en),
            )
            for (st, en) in AlarmEventHandler.segment_range(
                (
                    alarm.video_end_time_utc - alarm.video_start_time_utc
                ).total_seconds(),
                [
                    (a - alarm.video_start_time_utc).total_seconds()
                    for a in all_times
                ],
                min_segment_size=20,
                max_segment_size=30,
            )
        ]

    @staticmethod
    def get_cropped_box(tags: Tags) -> typing.Tuple[int, int, int, int, bool]:
        x1, y1, x2, y2 = int(1e6), int(1e6), 0, 0
        if len(tags.entities) == 0:
            return x1, y1, x2, y2, False
        for entity in tags.entities:
            if isinstance(entity, Entity):
                entity_dict = entity.to_json()
            else:
                entity_dict = entity
            track = entity_dict["track"]
            assert isinstance(track, dict)
            for box in track["boxes"]:
                x, y, w, h = box["box"]
                x1 = int(min(x1, x))
                y1 = int(min(y1, y))
                x2 = int(max(x2, x + w))
                y2 = int(max(y2, y + h))
        return (max(x1 - 10, 0), max(y1 - 10, 0), x2, y2, True)

    @staticmethod
    def get_people_start_end(
        tags: Tags, fps: typing.Union[float, int]
    ) -> typing.Tuple[str, str]:
        f1, f2 = int(1e6), 0
        for entity in tags.entities:
            if isinstance(entity, Entity):
                entity_dict = entity.to_json()
            else:
                entity_dict = entity
            track = entity_dict["track"]
            frames = track["frames"]
            f1 = min(min(frames), f1)
            f2 = max(max(frames), f2)
        return timedelta_to_str(timedelta(seconds=f1 / fps)), timedelta_to_str(
            timedelta(seconds=f2 / fps)
        )

    @staticmethod
    def get_event_times(
        alarm: Alarm, video_path: str, tags: Tags
    ) -> typing.List[typing.Tuple[datetime, str, str]]:
        video_start_time = alarm.video_start_time_utc
        event_times = []
        fps = get_video_fps(video_path)
        for entity in tags.entities:
            if isinstance(entity, Entity):
                entity_dict = entity.to_json()
            else:
                entity_dict = entity
            for transition in entity_dict["transitions"]:
                event_times.append(
                    (
                        video_start_time
                        + timedelta(seconds=transition["idx"] / fps),
                        transition["transition_type"],
                        transition["transition_cause"],
                    )
                )
        return sorted(event_times)

    @staticmethod
    def format_words(words):
        if not words:
            return ""
        if len(words) == 1:
            return words[0]
        if len(words) == 2:
            return f"{words[0]} and {words[1]}"
        return f"{', '.join(words[:-1])} and {words[-1]}"

    def get_nearby_alarm_times(self, alarm: Alarm) -> typing.List[datetime]:
        acc_granted_type = self._rds_client.get_alarm_type_id_from_alarm_type(
            AlarmMapper.get_ag_regex() + r"|(Open Door Command Issued.*)",
            regex=True,
        )
        assert alarm.door_uuid is not None
        filters = AlarmFilters(
            utc_time_interval=(
                alarm.video_start_time_utc,
                alarm.video_end_time_utc,
            ),
            display_only=False,
            door_uuid=([alarm.door_uuid], True),
            alarm_type_uuid=(acc_granted_type, True),
            order_desc=False,
        )
        db_rows = self._controller.alarm.fetch_alarms(
            tenant_ids=[alarm.tenant_id], filters=filters, limit=None
        )
        db_rows = deduplicate_list_with_employee_ids(
            db_rows, employee_ids=[row[0].employee_id for row in db_rows]
        )
        return [row[0].alarm_timestamp_utc for row in db_rows]

    @staticmethod
    def split_video_into_segments(
        video_path: str, start_time: float, end_time: float
    ) -> str:
        # use ffmpeg to split the video into new video from start_time to end_time
        output_path = video_path.replace(".mp4", "_split.mp4")
        command = [
            "ffmpeg",
            "-i",
            video_path,
            "-ss",
            str(start_time),
            "-to",
            str(end_time),
            "-c:v",
            "copy",
            "-c:a",
            "copy",
            output_path,
        ]
        subprocess.run(command, check=True)
        return output_path

    @staticmethod
    def crop_video(video_path: str, tags: Tags) -> str:
        x1, y1, x2, y2, do_crop = AlarmEventHandler.get_cropped_box(tags)
        if not do_crop:
            return video_path
        fps = get_video_fps(video_path)
        t1, t2 = AlarmEventHandler.get_people_start_end(tags, fps)
        save_path = "crop_temp.mp4"
        crop_w = x2 - x1
        crop_h = y2 - y1
        command = [
            "ffmpeg",
            "-ss",
            t1,
            "-to",
            t2,
            "-i",
            video_path,
            "-filter:v",
            f"crop={crop_w}:{crop_h}:{x1}:{y1}",
            "-r",
            "1",
            "-c:v",
            "libx264",
            "-preset",
            "ultrafast",
            "-crf",
            "28",
            save_path,
            "-y",
        ]
        subprocess.run(command, check=True)
        return save_path

    @staticmethod
    def annotate_video(alarm: Alarm, video_path: str) -> str:
        vid_cap = cv2.VideoCapture(video_path)
        # annotated_video_path = video_path.replace(".mp4", "_annotated.mp4")
        annotated_video_path = "temp.mp4"
        fps = int(vid_cap.get(cv2.CAP_PROP_FPS))
        width = int(vid_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(vid_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        ret, frame = vid_cap.read()
        fourcc = cv2.VideoWriter_fourcc(*"avc1")
        outfile = cv2.VideoWriter(
            annotated_video_path,
            fourcc,
            1,
            (width, height),
        )
        frame_idx = 0
        while True:
            ret, frame = vid_cap.read()
            if not ret:
                break
            assert alarm.scene_info is not None
            assert alarm.scene_info.door_bbox is not None
            assert alarm.scene_info.door_orientation_point is not None
            if frame_idx % fps == 0:
                draw_doors(
                    frame,
                    alarm.scene_info.door_bbox,
                    cam_position=alarm.scene_info.camera_position,
                    door_orientation_point=alarm.scene_info.door_orientation_point,
                    alpha=0.2,
                )
                outfile.write(frame)
            frame_idx += 1
        return annotated_video_path

    @staticmethod
    def construct_enterprise_text(
        ag_times: typing.List[datetime],
        video_start_time: datetime,
        event_times: typing.List[datetime],
    ) -> str:
        ag_times_str = AlarmEventHandler.format_words(
            [timedelta_to_str(tt - video_start_time) for tt in ag_times]
        )
        event_times_str = AlarmEventHandler.format_words(
            [timedelta_to_str(tt - video_start_time) for tt in event_times]
        )
        return (
            "You are given a video of an alarm created at a site monitored by a remote security operator. "
            "The door of interest is marked in dark blue with a red line at the bottom. "
            "At the bottom of the door is an arrow pointing in the direction of entry. "
            "You are to describe in detail people entering and exiting through the door of interest along with the timestamp of each entry or exit. "
            "Ignore people passing through other doors or walking nearby. "
            f"The access control system has granted access to the door at the following times: {ag_times_str}. "
            f"There are sensors near the door which detected possible entry or exit at the following times: {event_times_str}. "
            "If there are any people tailgating or piggybacking when another person swipes the card and enters, escalate the alarm. "
            "Two or people entering after a single access granted is considered tailgating and needs to be escalated. "
            "Respond in json format with keys 'description' and 'recommendation' where recommendation is either Resolve or Escalate. "
        )

    @staticmethod
    def segment_range(b, events, min_segment_size=20, max_segment_size=30):
        events = sorted(list(set(events)))
        if not events:
            return [
                (i, min(i + max_segment_size, b))
                for i in range(0, b, max_segment_size)
            ]
        distances = np.zeros(b + 1)
        for i in range(b + 1):
            distances[i] = min(abs(i - event) for event in events)
        segments = []
        current_pos = 0
        while current_pos < b:
            best_end = None
            best_score = -1
            for end_pos in range(
                current_pos + min_segment_size,
                min(current_pos + max_segment_size + 1, b + 1),
            ):
                score = distances[end_pos]
                if score > best_score:
                    best_score = score
                    best_end = end_pos
            if best_end is None:
                best_end = min(current_pos + max_segment_size, b)
            segments.append((current_pos, best_end))
            current_pos = best_end
        return segments
