"""Module containing processors for alarm side effects.
Once an alarm is processed, there can be various side effects for that alarm.
For example, for an AG with entry + unauthorized entry, a side effect is to
generate a tailgating alarm.
Similary, based on the tenant config, an Alarm canceled event can resolve a previous
Alarm Active event.

This module contains all logic where the outcome of processing an alarm
can have an effect on other alarms/new alarms.
"""

from .alarm_canceled import AlarmCanceledSideEffect
from .alarm_grouper import AlarmGrouperSideEffect
from .alarm_notifier import AlarmNotifierSideEffect
from .comment_generator import CommentGenerator
from .everbridge_event import EverbridgeEventSideEffect
from .llm_alarm_analyzer import (
    EnterpriseLLMAlarmAnalyzerSideEffect,
    LLMAlarmAnalyzerSideEffect,
    MotionLLMAlarmAnalyzerSideEffect,
)
from .motion_detection import MotionDetectionSideEffect
from .tailgating import TailgatingSideEffect
from .unauthorized_entry import UnauthorizedEntrySideEffect
from .zoom_event import ZoomEventSideEffect
