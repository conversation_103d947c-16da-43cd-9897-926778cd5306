"""MOTPy tracker class, along with any relevant utility methods"""

from typing import Callable, Optional, Sequence, Tuple

import numpy as np
import scipy
import structlog
from motpy import MultiObjectTracker
from motpy.core import Detection
from motpy.metrics import angular_similarity, calculate_iou
from motpy.tracker import (
    EPS,
    BaseMatchingFunction,
    SingleObjectTracker,
    _sequence_has_none,
)
from scipy.spatial.distance import cdist

from ml_module.ml_interfaces.detections import Detections
from ml_module.reid_model import BaseReIDModel
from ml_module.tracker.motpy.motpy_tracker import MOTPyTracker
from ml_module.tracker.utils import add_patches, get_features_from_patches

log = structlog.get_logger("hakimo", module="DeepSORT Tracker")


class EuclideanSimilarity:
    def __init__(self, base_distance: float = 1.6):
        self.base_distance = base_distance

    def __call__(self, features1, features2):
        dist_mat = cdist(features1, features2, metric="euclidean")
        sim_mat = self.base_distance / (dist_mat + 1e-09)
        sim_mat = 2 / (1 + np.exp(-sim_mat)) - 1
        return sim_mat, dist_mat


def cost_matrix_iou_feature(
    trackers: Sequence[SingleObjectTracker],
    detections: Sequence[Detection],
    feature_similarity_fn=EuclideanSimilarity(),
    feature_similarity_beta: Optional[float] = None,
    feature_similarity_multiplier: float = 2.0,
) -> Tuple[
    np.ndarray,
    Optional[np.ndarray],
    np.ndarray,
    np.ndarray,
    Optional[np.ndarray],
]:
    # boxes
    b1 = np.array([t.box() for t in trackers])
    b2 = np.array([d.box for d in detections])

    # box iou
    inferred_dim = int(len(b1[0]) / 2)
    iou_mat = calculate_iou(b1, b2, dim=inferred_dim)
    wh1 = (b1[:, 2:] - b1[:, :2]) / 2

    wh2 = (b2[:, 2:] - b2[:, :2]) / 2
    size = (
        np.matmul(
            wh1.mean(axis=-1, keepdims=True),
            wh2.mean(axis=-1, keepdims=True).T,
        )
        ** 0.5
    )
    center1 = b1[:, :2] + wh1 / 2
    center2 = b2[:, :2] + wh2 / 2
    dist_mat = (
        cdist(center1, center2, metric="euclidean") / size
    )  # / ((np.matmul(b1[:,[2]], b2[:,[2]].T))**0.5)

    # feature similarity
    sim_mat = None
    sim_dist_mat = None
    if feature_similarity_beta is not None:
        # get features
        f1 = [t.feature for t in trackers]
        f2 = [d.feature for d in detections]
        if not (_sequence_has_none(f1) or _sequence_has_none(f2)):
            sim_mat, sim_dist_mat = feature_similarity_fn(f1, f2)
            sim_mat = (
                feature_similarity_beta
                + (1 - feature_similarity_beta) * sim_mat
            )
    cost_mat = (
        -(feature_similarity_multiplier * sim_mat + iou_mat)
        if sim_mat is not None
        else -iou_mat
    )
    return cost_mat, sim_mat, iou_mat, dist_mat, sim_dist_mat


def match_by_cost_matrix(
    trackers: Sequence[SingleObjectTracker],
    detections: Sequence[Detection],
    min_iou: float = 0.1,
    multi_match_min_iou: float = 1.0 + EPS,
    min_sim: float = 0.4,
    max_dist: Optional[float] = None,
    feature_similarity_multiplier: float = 2.0,
    **kwargs,
) -> np.ndarray:
    if len(trackers) == 0 or len(detections) == 0:
        return np.array([])

    (
        cost_mat,
        sim_mat,
        iou_mat,
        dist_mat,
        sim_dist_mat,
    ) = cost_matrix_iou_feature(
        trackers,
        detections,
        feature_similarity_multiplier=feature_similarity_multiplier,
        **kwargs,
    )
    row_ind, col_ind = scipy.optimize.linear_sum_assignment(cost_mat)

    matches = []
    for r, c in zip(row_ind, col_ind):
        if max_dist and dist_mat[r, c] > max_dist:
            continue
        # check linear assignment winner
        if iou_mat[r, c] >= min_iou or (
            sim_mat is not None and sim_mat[r, c] >= min_sim
        ):
            matches.append((r, c))

        # check other high IOU detections
        if multi_match_min_iou < 1.0:
            for c2 in range(iou_mat.shape[1]):
                if c2 != c and iou_mat[r, c2] > multi_match_min_iou:
                    matches.append((r, c2))
    return np.array(matches)


class WeightedMatchingFunction(BaseMatchingFunction):
    def __init__(
        self,
        min_iou: float = 0.1,
        multi_match_min_iou: float = 1.0 + EPS,
        min_sim: float = 0.4,
        max_dist: Optional[float] = None,
        feature_similarity_fn: Callable = EuclideanSimilarity(),
        feature_similarity_beta: Optional[float] = None,
        feature_similarity_multiplier: float = 2.0,
    ) -> None:
        self.min_iou = min_iou
        self.multi_match_min_iou = multi_match_min_iou
        self.min_sim = min_sim
        self.max_dist = max_dist
        self.feature_similarity_fn = feature_similarity_fn
        self.feature_similarity_beta = feature_similarity_beta
        self.feature_similarity_multiplier = feature_similarity_multiplier

    def __call__(
        self,
        trackers: Sequence[SingleObjectTracker],
        detections: Sequence[Detection],
    ) -> np.ndarray:
        return match_by_cost_matrix(
            trackers,
            detections,
            min_iou=self.min_iou,
            multi_match_min_iou=self.multi_match_min_iou,
            min_sim=self.min_sim,
            max_dist=self.max_dist,
            feature_similarity_fn=self.feature_similarity_fn,
            feature_similarity_beta=self.feature_similarity_beta,
            feature_similarity_multiplier=self.feature_similarity_multiplier,
        )


class DeepSORT(MOTPyTracker):
    """Tracker that depends on MOTPy online tracker.
    Additionally implements a ReID based tracklet
    merge on top of the online tracking output
    """

    _TRACKER_TYPE = "deepsort"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        matching_type = kwargs.get("matching_type", "custom_people")
        if matching_type == "custom_people":
            self.matching_fn_kwargs = {
                "min_iou": 0.1,
                "min_sim": 0.4,
                "max_dist": None,
                "feature_similarity_fn": EuclideanSimilarity(
                    base_distance=1.6
                ),
                "feature_similarity_beta": 0,
            }
            self.matching_fn = WeightedMatchingFunction(
                **self.matching_fn_kwargs
            )
        elif matching_type == "enterprise_people":
            self.matching_fn_kwargs = {
                "min_iou": 0.1,
                "min_sim": 0.4,
                "max_dist": None,
                "feature_similarity_fn": EuclideanSimilarity(
                    base_distance=1.6
                ),
                "feature_similarity_beta": 0,
                "feature_similarity_multiplier": 5.0,
            }
            self.matching_fn = WeightedMatchingFunction(
                **self.matching_fn_kwargs
            )
        elif matching_type == "custom_vehicles":
            self.matching_fn_kwargs = {
                "min_iou": 0.3,
                "min_sim": 0.45,
                "max_dist": 2,
                "feature_similarity_fn": EuclideanSimilarity(
                    base_distance=0.8
                ),
                "feature_similarity_beta": 0,
            }
            self.matching_fn = WeightedMatchingFunction(
                **self.matching_fn_kwargs
            )
        else:
            self.matching_fn = None
            self.matching_fn_kwargs = {
                "min_iou": 0.1,
                "feature_similarity_fn": angular_similarity,
                "feature_similarity_beta": 0,
            }

        model_type = kwargs.get("model_type", None)
        if model_type is not None:
            self._reid_model = BaseReIDModel.create(model_type)
        else:
            self._reid_model = None

    def _init_tracker(self, fps: int, processing_fps) -> MultiObjectTracker:
        return MultiObjectTracker(
            dt=0.1 * fps / processing_fps,
            model_spec=self._model_spec,
            matching_fn=self.matching_fn,
            matching_fn_kwargs=self.matching_fn_kwargs,
            tracker_kwargs={"smooth_feature_gamma": 0.0},
        )

    def _preprocess_detections(self, video_path: str, detections: Detections):
        if self._reid_model is not None:
            add_patches(detections, video_path)
            get_features_from_patches(detections, self._reid_model)
        return detections
