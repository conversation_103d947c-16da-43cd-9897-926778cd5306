import os.path as osp
import shutil
import time
from collections import defaultdict
from datetime import datetime
from pathlib import Path
from typing import (
    Any,
    DefaultDict,
    Dict,
    List,
    Optional,
    Sequence,
    Set,
    Tuple,
    Union,
)

import numpy as np
import pytz
import structlog

import controller as ctrl
from common_utils.cloud_config_utils import cloud_config_manager
from common_utils.helper_utils import ObjectStorage
from common_utils.s3_utils import download_file_from_s3, get_s3_path_details
from common_utils.time_utils import convert_timezoned_timestr_to_utc
from common_utils.typing_helpers import NUMBER, POINT
from common_utils.video_utils import get_video_duration
from config import backend_config as config
from interfaces.alarm import (
    ALARM_TYPE_MOTION,
    SERVER_DISCONNECTED_ALARM_TYPE,
    Alarm,
)
from interfaces.camera_details import CameraPosition
from interfaces.scene_info import SceneInfo
from interfaces.tenant_config import AlarmProcessingConfig

log = structlog.get_logger("hakimo", module="ML Module Utils")


def _depth_first_search(adj_list, visited, vertex, result, key):
    # pulled from https://stackoverflow.com/a/42036326
    visited.add(vertex)
    result[key].append(vertex)
    for neighbor in adj_list[vertex]:
        if neighbor not in visited:
            _depth_first_search(adj_list, visited, neighbor, result, key)


def pairs_to_groups(
    edges: List[Tuple[str, str]],
    nodes: Optional[Set] = None,
) -> List[List[str]]:
    """Take a list of Edges i.e. Node pairs and group them into
    identities i.e. a list of lists, with each list the nodes
    in that identity"""
    adj_list = defaultdict(set)
    seen_nodes = set()
    for pair in edges:
        x, y = tuple(pair)
        if nodes is not None:
            if x not in nodes or y not in nodes:
                raise ValueError(f"Invalid edge {x} or {y} not in {nodes}")
        seen_nodes.add(x)
        seen_nodes.add(y)
        adj_list[x].add(y)
        adj_list[y].add(x)

    result: DefaultDict[Any, List] = defaultdict(list)
    visited: Set = set()
    for vertex in adj_list:
        if vertex not in visited:
            _depth_first_search(adj_list, visited, vertex, result, vertex)
    # will be a list of lists, each list in the list are all the track_ids
    # of nodes in an identity
    final_result = list(result.values())
    if nodes is not None:
        for node in set(nodes).difference(seen_nodes):
            final_result.append([node])
        # every node must appear in the output if nodes is provided
        assert sum([len(i) for i in final_result]) == len(nodes)
    return final_result


def check_video_time_match(
    video_path: str, alarm: Alarm, delta: float = 0.01
) -> Tuple[bool, float, float]:
    """Given a video_path and the alarm, check that the downloaded
    video is as long as the video expected by the alarm
    Args:
        video_path ([str]): local path of the video downloaded
        alarms ([Alarm]): Alarm object, should have video_start_time_utc
            and video_end_time_utc
        delta ([float]): fractional percentage of difference allowed to
            still be a match, default is 0.01 -> 1 percent
    Returns:
        is_match ([bool]): whether the length of video matches
            the alarm expected video
        actual_length ([float]): actual length of video seconds
        expected_length ([float]): expected length of video seconds
    """
    actual_length = get_video_duration(video_path)
    expected_length = (
        alarm.video_end_time_utc - alarm.video_start_time_utc
    ).seconds
    difference_video_length = abs(actual_length - expected_length)
    return (
        difference_video_length / (actual_length + 1e-8) < delta,
        actual_length,
        expected_length,
    )


def get_door_bottom(
    door_bbox: Union[Sequence[POINT], np.ndarray],
) -> np.ndarray:
    """Given a door bounding box, will return the bottom of the door
    Assumptions:
        Doors is vertical (camera is not flipped upside down or horizontal)
        Therefore:
            The points with the largest y coordinates correspond to the bottom edge

    Args:
        door_bbox ([list, np.ndarray, tuple]): Door coordinates

    Returns:
        [list]: The coordinates of the two bottom points
    """
    # Sort by y and get the two lowest points
    door_bottom = np.array(
        sorted(door_bbox, key=lambda x: x[1], reverse=True)[:2]
    )
    return door_bottom


def get_door_top(door_bbox: Union[Sequence[POINT], np.ndarray]) -> np.ndarray:
    """Given a door bounding box, will return the top of the door
    Assumptions:
        Doors is vertical (camera is not flipped upside down or horizontal)
        Therefore:
            The points with the smallest y coordinates correspond to the top edge

    Args:
        door_bbox ([list, np.ndarray, tuple]): Door coordinates

    Returns:
        [list]: The coordinates of the two top points
    """
    # Sort by y and get the two lowest points
    door_top = np.array(sorted(door_bbox, key=lambda x: x[1])[:2])
    return door_top


def get_door_orientation(
    door_bbox: Union[Sequence[POINT], np.ndarray],
    camera_position: CameraPosition,
    door_orientation_point: POINT = None,
):
    door_bottom = get_door_bottom(door_bbox)
    bottom_left, bottom_right = (
        min(door_bottom, key=lambda x: x[0]),
        max(door_bottom, key=lambda x: x[0]),
    )
    if camera_position == CameraPosition.SECURE:
        door_sl = bottom_left
        door_sr = bottom_right
    elif camera_position == CameraPosition.UNSECURE:
        door_sl = bottom_right
        door_sr = bottom_left
    else:
        raise NotImplementedError(
            f"Cannot deal with camera position {camera_position}"
        )
    srsl = (door_sr[0] - door_sl[0], door_sr[1] - door_sl[1])
    if door_orientation_point is not None:
        assert isinstance(door_orientation_point, (tuple, list, np.ndarray)), (
            "Door orientation must be list/tuple/np.ndarray"
            f" not {type(door_orientation_point)}"
        )
        assert (
            len(door_orientation_point) == 2
        ), "Door orientation point can only have x and y coordinate"
        assert isinstance(door_orientation_point[0], int) and isinstance(
            door_orientation_point[1], int
        ), "Door orientation point must have integer coordinates"
        # Positive if point is on secure side
        # Negative if point is on unsecure side
        cross_prod = (
            srsl[0] * (door_orientation_point[1] - door_sl[1])
            - (door_orientation_point[0] - door_sl[0]) * srsl[1]
        )
        # We know that the given point (door orientation point), should be
        # on the same side of the door as the camera. If cross_prod < 0 that
        # means that the point is on the unsecure side of the
        # estimated door line. If the camera is also on the unsecure side,
        # nothing further must be done. However, if the camera is on the secure
        # side, that means we have incorrectly estimated the orientation of the door
        # (left vs right side). Hence, we flip it around, so that we correctly have
        # the secure right and secure left points. The same reasoning can be applied
        # for when the cross product is >= 0
        if (cross_prod < 0 and camera_position == CameraPosition.SECURE) or (
            cross_prod >= 0 and camera_position == CameraPosition.UNSECURE
        ):
            srsl = (-srsl[0], -srsl[1])
            door_sl, door_sr = door_sr, door_sl

    return srsl, door_sl, door_sr


def scale_scene_info(scene_info: SceneInfo, video_dim: POINT) -> None:
    """Given the door details, including the door_bbox, door_orientation_point
    and the labelling_resolution, scale the door bbox and the door orientation
    point to match the video dimensions. This ensures that the AI engine is
    robust to changes in resolution. The door_details dict is modified in place!

    Args:
        scene_info (SceneInfo): Scene info object containing all scene details,
        such as door_orientation_point, labelling_resolution. Modified in place
        video_dim (Sequence[Union[int, float]]): Dimension of the video WxH.
        e.g. (1920, 1080) for 1080p
    """
    if scene_info.labelling_resolution is None:
        return
    x_ratio = video_dim[0] / scene_info.labelling_resolution[0]
    y_ratio = video_dim[1] / scene_info.labelling_resolution[1]
    if scene_info.current_resolution == [video_dim[0], video_dim[1]]:
        return
    if x_ratio != 1 or y_ratio != 1:
        log.warning(
            "Labelling resolution mismatch",
            labelling_resolution=scene_info.labelling_resolution,
            video_resolution=video_dim,
        )
    if scene_info.door_bbox is not None:
        scene_info.door_bbox = [
            [int(x * x_ratio), int(y * y_ratio)]
            for x, y in scene_info.door_bbox
        ]
    if scene_info.door_orientation_point is not None:
        scene_info.door_orientation_point = [
            int(scene_info.door_orientation_point[0] * x_ratio),
            int(scene_info.door_orientation_point[1] * y_ratio),
        ]
    if scene_info.dead_zones is not None:
        scene_info.dead_zones = [
            [[x * x_ratio, y * y_ratio] for x, y in poly]
            for poly in scene_info.dead_zones
        ]
    if scene_info.vehicle_parking_zones is not None:
        scene_info.vehicle_parking_zones = [
            [[x * x_ratio, y * y_ratio] for x, y in poly]
            for poly in scene_info.vehicle_parking_zones
        ]
    scene_info.current_resolution = [video_dim[0], video_dim[1]]


def get_door_size(
    door_bbox: Union[Sequence[POINT], np.ndarray],
) -> Tuple[float, float]:
    """Given the door bounding box, find the width and height of the door

    Args:
        door_bbox (Union[Sequence[POINT], np.ndarray]):
            door coordinates

    Returns:
        Tuple[float, float]: Tuple of door width and door height
    """
    door_bottom = get_door_bottom(door_bbox)
    door_top = get_door_top(door_bbox)
    # Find distance between middle of top and bottom for height
    door_height = np.sqrt(
        np.sum((np.mean(door_top, axis=0) - np.mean(door_bottom, axis=0)) ** 2)
    )
    # Find length of bottom
    door_width = np.sqrt(np.sum((door_bottom[0] - door_bottom[1]) ** 2))
    return door_width, door_height


def get_loc_from_door_line(
    position: POINT,
    line_vec: np.ndarray,
    midpoint: POINT,
    origin: POINT,
) -> float:
    """Given a point, determine whether it is on the secure on unsecure side
    of a line. The origin is the start point of the line.
    The line vector is a vector from the origin to the endpoint of the line.
    The midpoint is passed in to prevent recomputation on multiple calls
    It is assumed that if the angle between the position vector and the line_vector
    is positive (the cross product is positive), the position is on the secure side
    and hence the returned distance is non negative. Therefore, the origin must be
    on the left when viewed from the unsecure side.
    """
    orientation_vec = (
        position[0] - origin[0],
        position[1] - origin[1],
    )
    cross_prod = (
        line_vec[0] * orientation_vec[1] - orientation_vec[0] * line_vec[1]
    )
    pos_on_secure_side = True
    if cross_prod < 0:
        pos_on_secure_side = False
    # here it is unsigned
    signed_loc = np.sqrt(
        (position[0] - midpoint[0]) ** 2 + (position[1] - midpoint[1]) ** 2
    )
    if not pos_on_secure_side:
        # Unsecure side distances are all negative
        signed_loc *= -1
    return signed_loc


def sample_items(
    items: List[Any],
    N: int = 5,
    include_end: bool = True,
) -> List[Any]:
    """
    Return a list of items from the input list, sampled evenly along the
    range of the input list. If less than required minimum items passed
    then the first item is repeated first to make up required N

    Args:
      items (typing.List[typing.Any]): The list of items to sample from.
      N (int): The number of items to sample. Defaults to 5
      include_end (bool): Whether to include the last item in the list.
        Defaults to True

    Returns:
      A list of items.
    """
    if not include_end and len(items) > 1:
        items = items[:-1]
    assert len(items) > 0
    num_patches = len(items)
    if N >= num_patches:
        return [items[0]] * (N - num_patches) + items
    sample_indices = np.linspace(0, num_patches, N, endpoint=False).astype(
        np.int32
    )
    return [items[i] for i in sample_indices]


def should_be_processed_by_ai(
    alarm_proc_conf: AlarmProcessingConfig,
    video_details: Dict[str, Any],
    alarm: Alarm,
    internal_alarm_type: str,
) -> bool:
    """
    Return False if alarm video exists, but will not be processed by AI
    Else, return True
    """
    alarm_processing_enabled = alarm_proc_conf.processAlarmVideo

    if video_details.get("local_video_path") is not None:
        # Scene info should never be none, but add here to make mypy happy
        if (alarm.scene_info is not None) and (
            # Regular alarm processing
            (
                # And we should process the alarm but door isn't labelled
                alarm.scene_info.door_bbox is None
                or alarm.scene_info.camera_position == CameraPosition.UNKNOWN
                # Or this alarm type processing is not enabled for the tenant
                or not alarm_processing_enabled
            )
            and alarm.alarm_type != ALARM_TYPE_MOTION
        ):
            return False
        if (
            internal_alarm_type.startswith("Access Granted")
            and not alarm_proc_conf.enableTailgating
        ):
            # Tailgating processing disabled
            return False
    return True


def download_alarm_video(video_path: str, out_dir: str) -> str:
    """Downloads an alarm video (given the video path) and makes it available on local disk.
    Places it in the out_dir folder, and returns the path to the local
    file.
    """
    object_type_storage = config.gateway()["OBJECT_STORAGE"]["type"]
    log.info(
        "Alarm video path",
        video_path=video_path,
        video_storage_type=object_type_storage,
    )
    if ObjectStorage.local == object_type_storage:
        ret_path = osp.join(out_dir, osp.basename(video_path))
        shutil.copy(video_path, ret_path)
        return ret_path
    vid_details = get_s3_path_details(video_path)
    assert vid_details is not None, "Video must exist to be downloaded"
    log.info(
        "Downloading video",
        video_path=video_path,
        video_details=vid_details,
    )
    stime = time.time()
    download_file_from_s3(
        vid_details["bucket_name"],
        vid_details["file_name"],
        out_dir,
    )
    local_video_path = osp.join(
        out_dir,
        osp.basename(vid_details["file_name"]),
    )
    log.debug(
        "Time taken to download S3 video",
        download_time=time.time() - stime,
    )
    return local_video_path


def is_door_associated_alarm(alarm: Alarm) -> bool:
    """
    Returns a bool to specify whether the alarm is associated with a door or not.
    All alarms except "Server disconnected" are assumed to be associated with a door

    Args:
        alarm (Alarm): The alarm object coming from db

    Returns:
        bool: boolean returning true if alarm is associated with a door
    """
    if alarm.door_uuid is None and (
        alarm.alarm_type in [SERVER_DISCONNECTED_ALARM_TYPE]
    ):
        return False
    return True


def get_instance_name(instance_path: Union[str, Path]) -> str:
    return Path(instance_path).stem.split("+")[-1].split("_")[0]


def get_instance_files(dir_path: str, file_ext: str) -> Dict[str, str]:
    tag_files = {
        get_instance_name(filepath): str(filepath)
        for filepath in list(Path(dir_path).rglob(f"*_{file_ext}"))
    }
    return tag_files


def is_automated_talkdown_window_active(
    automated_talkdown_start_time_utc: Optional[str],
    automated_talkdown_end_time_utc: Optional[str],
    query_time: datetime = datetime.utcnow().astimezone(pytz.UTC),
) -> bool:
    if (
        automated_talkdown_start_time_utc is None
        or automated_talkdown_end_time_utc is None
    ):
        return True

    start_time = convert_timezoned_timestr_to_utc(
        automated_talkdown_start_time_utc, "UTC"
    )
    end_time = convert_timezoned_timestr_to_utc(
        automated_talkdown_end_time_utc, "UTC"
    )
    if start_time <= query_time <= end_time:
        return True
    return False


def get_dho_threshold_incl_heuristics(
    controller: ctrl.ControllerMap, door_uuid: str, tenant_id: str
) -> Optional[NUMBER]:
    dho_threshold = controller.door.get_dho_threshold(door_uuid)
    tenant_config = controller.tenant.get_config(tenant_id)
    if tenant_config is not None and tenant_config.useReducedDHOThreshold:
        reduced_thresh = cloud_config_manager.is_enabled(
            f"reducedDHOThreshold.{tenant_id}"
        )
        if not isinstance(reduced_thresh, (int, float)):
            reduced_thresh = config.HAIE.heuristics["reducedDHOThreshold"]

        dho_threshold = (
            dho_threshold or config.HAIE.heuristics["reducedDHOThreshold"]
        )
        dho_threshold = min(
            reduced_thresh,
            dho_threshold,
        )
    return dho_threshold
