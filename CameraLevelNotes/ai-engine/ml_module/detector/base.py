"""Base class for all Detectors"""

import abc
import time
import typing

import cv2
import numpy as np
import structlog
import torch

from common_utils.cuda_utils import gpumat_list_to_torch_batch

# from common_utils.dali import get_batch_from_video_dali
from common_utils.tracer import trace_method
from common_utils.video_utils import (  # get_frames_from_video_decorder,
    get_batch_from_video_cv2,
    get_frames_from_video_cv2,
    get_video_reader,
)
from interfaces.entities.entity_types import EntityType
from ml_module.detection import BaseDetection
from ml_module.ml_interfaces.detections import Detections
from ml_module.reid_model import BaseReIDModel
from ml_module.segmentor import Segmentor

log = structlog.get_logger("hakimo", module="Detector")


class Detector:
    """Base detector class from which all detectors must inherit"""

    _DETECTOR_TYPES = {}
    _DETECTOR_TYPE = "_BASE"

    def __init__(
        self,
        dump_dir: str,
        use_clahe: bool = False,
        use_gpu_decode: bool = False,
        segmentor: typing.Optional[Segmentor] = None,
        filter_in_classes: typing.Optional[typing.List[EntityType]] = None,
        conf_threshold: float = 0.5,
        reid_models: typing.Optional[
            typing.Dict[EntityType, typing.Optional[BaseReIDModel]]
        ] = None,
        **kwargs,
    ):  # pylint: disable=unused-argument
        self._batch_size = 32

        self._conf_threshold = conf_threshold
        self._use_clahe = use_clahe
        self._dump_dir = dump_dir
        self._use_gpu_decode = use_gpu_decode
        self._segmentor = segmentor
        self._filter_in_classes = filter_in_classes
        self._reid_models = reid_models
        self._use_sahi = False

    def __init_subclass__(cls, **kwargs):
        if cls._DETECTOR_TYPE not in cls._DETECTOR_TYPES:
            cls._DETECTOR_TYPES[cls._DETECTOR_TYPE] = cls

        super().__init_subclass__(**kwargs)

    @property
    def segmentor(self) -> typing.Optional[Segmentor]:
        return self._segmentor

    @property
    def dump_dir(self) -> str:
        return self._dump_dir

    @classmethod
    def create(cls, detector_type: str, **kwargs) -> "Detector":
        """Creates a detector and returns it

        Args:
            detector_type ([str]): Type of detector to be returned

        Raises:
            ValueError: For unrecognized detector type

        Returns:
            [Detector]: Child of Detector class, that can detect objects in images
        """
        if (
            isinstance(detector_type, str)
            and detector_type in cls._DETECTOR_TYPES
        ):
            log.debug(
                f"Creating Detector of type {detector_type}", kwargs=kwargs
            )
            return cls._DETECTOR_TYPES[detector_type](**kwargs)
        raise ValueError(f"Invalid detector {detector_type}")

    @staticmethod
    def _load_model(
        batch_size: int,
        img_size: int,
        gpu_path: str,
        cpu_path: str,
        use_gpu: bool,
    ):
        if use_gpu:
            weights_path = gpu_path
        else:
            weights_path = cpu_path
        model = torch.jit.load(weights_path, _extra_files={"config.txt": ""})
        if use_gpu:
            model.half()
            im = torch.zeros(
                (batch_size, 3, img_size, img_size),
                dtype=torch.half,
                device=torch.device("cuda:0"),
            )
            with torch.no_grad():
                for _ in range(2):
                    _ = model(im)
        model.eval()
        return model

    @property
    def conf_threshold(self):
        return self._conf_threshold

    @property
    def batch_size(self):
        return self._batch_size

    # @conf_threshold.setter
    # def conf_threshold(self, x):
    #     if not (x >= 0 and x <= 1):
    #         raise ValueError("Confidence threshold must be between 0 and 1")
    #     self._conf_threshold = x

    @trace_method
    def detect(
        self,
        img: typing.Union[np.ndarray, typing.List[np.ndarray], torch.Tensor],
        conf_threshold: typing.Optional[float] = None,
    ):
        """Class method that produces detections for an image or a batch of images
        img must be a list if batched image recognition is required
        (DETECTOR MUST INTERNALLY HANDLE TRANSFER INTO NUMPY/TENSORFLOW/TORCH)
        Assumed that image is 3D, (H, W, C) and channel order is BGR (openCV default)
        Args:
            img ([list, np.ndarray]): Single image or list of images to run inference
            Note that if it is a list, all images must have the same size!!

        Returns:
            [BaseDetection]: A BaseDetection (see detection.py in ml_module folder) or
                a subclass of BaseDetection
        """
        if isinstance(img, torch.Tensor):
            if self._use_sahi:
                results = self._detect_img_batch_tensor_sahi(
                    img, conf_threshold
                )
            elif img.shape[3] / img.shape[2] > 2:
                results = self._detect_img_batch_tensor_wide_angle(
                    img, conf_threshold
                )
            else:
                results = self._detect_img_batch_tensor(img, conf_threshold)
            return [
                self._filter_in_class_dets(frame_dets)
                for frame_dets in results
            ]
        if isinstance(img, list):
            results = self._detect_img_batch(img, conf_threshold)
            return [
                self._filter_in_class_dets(frame_dets)
                for frame_dets in results
            ]
        result = self._detect_img(img, conf_threshold)
        return self._filter_in_class_dets(result)

    def _filter_in_class_dets(
        self,
        frame_dets: typing.List[BaseDetection],
        include_false_positive: bool = False,
    ) -> typing.List[BaseDetection]:
        filter_in_classes = self._filter_in_classes or []
        if include_false_positive:
            filter_in_classes += [EntityType.POTENTIAL_FALSE_POSITIVE]
        min_classes = 1 if include_false_positive else 0
        if len(filter_in_classes) > min_classes:
            frame_dets = [
                det for det in frame_dets if det.det_type in filter_in_classes
            ]
        return frame_dets

    # pylint: disable=too-many-arguments
    @trace_method
    def detect_video(
        self,
        video_path: str,
        processing_fps: float,
        start_frame: int,
        end_frame: int,
        conf_threshold: typing.Optional[float] = None,
        use_segmentor: bool = False,
        vid_cap: typing.Optional[cv2.VideoCapture] = None,
    ) -> Detections:
        """Method to detect in a video file. Selects frames at processing fps,
        between start and end frame, and then returns the boxes found, along with
        the frame numbers processed.

        Args:
            vid_cap ([cv2.VideoCapture]): video capture object for the video
            processing_fps ([int]): frames per second to process the video at
            start_frame ([int]): Start frame number
            end_frame ([int]): End frame number
        Returns:
            [Detections]: Detections object containing the detections for the
            processed frames in the video
        """
        failed_gpu_decode = False
        time_dict: typing.Dict[str, float] = {"start_time": time.time()}
        try:
            vid_reader = get_video_reader(
                video_path,
                log,
                use_gpu=self._use_gpu_decode,
                vcap=vid_cap,
            )
        except cv2.error as cerr:
            log.error(
                "Falling back to CPU decoding at init",
                video_path=video_path,
                exc_info=cerr,
                inference_time=time.time() - time_dict["start_time"],
            )
            vid_reader = get_video_reader(
                video_path, use_gpu=False, vcap=vid_cap
            )
            failed_gpu_decode = True
        fps = vid_reader.fps
        time_dict["det_time"] = time.time()
        log.debug(
            "Loaded Video",
            fps=fps,
            model=self._DETECTOR_TYPE,
            inference_time=time_dict["det_time"] - time_dict["start_time"],
        )
        frame_idx = start_frame
        detections = Detections(
            video_path=video_path,
        )
        if self._use_gpu_decode and not failed_gpu_decode:
            try:
                for (
                    frames,
                    img_batch,
                    _,
                    offset_img_batch,
                ) in get_batch_from_video_cv2(
                    vid_reader,
                    processing_fps,
                    self._batch_size,
                    offset=bool(use_segmentor and self._segmentor is not None),
                    pad_batch=False,
                ):
                    img_tensor = gpumat_list_to_torch_batch(
                        img_batch, vid_reader.height, vid_reader.width
                    )
                    log.debug("Getting detections for batch")
                    boxes_batch = self.detect(
                        img_tensor, conf_threshold=conf_threshold
                    )
                    boxes_batch = boxes_batch[: len(frames)]
                    if self._reid_models:
                        for (
                            entity_type,
                            reid_model,
                        ) in self._reid_models.items():
                            if reid_model:
                                boxes_batch = reid_model.box_features(
                                    img_tensor, boxes_batch, entity_type
                                )
                    try:
                        if use_segmentor and self._segmentor:
                            if self._segmentor.use_tensor:
                                offset_img_tensor = gpumat_list_to_torch_batch(
                                    offset_img_batch,
                                    vid_reader.height,
                                    vid_reader.width,
                                )
                                (
                                    boxes_batch,
                                    flow_dict,
                                ) = self._segmentor.segment_with_quadrants(
                                    img_tensor, offset_img_tensor, boxes_batch
                                )
                            else:
                                (
                                    boxes_batch,
                                    flow_dict,
                                ) = self._segmentor.segment_with_quadrants(
                                    img_batch[: len(frames)],
                                    offset_img_batch[: len(frames)],
                                    boxes_batch=boxes_batch,
                                )
                            detections.add_flows(flow_dict)
                    except Exception as rex:
                        log.warning(
                            "Failed cv2 GPU segmentor, skipping segmentor",
                            exc_info=rex,
                            video_path=video_path,
                        )
                    detections.extend(frames, boxes_batch)
            except (ImportError, cv2.error) as ierr_rerr:
                log.warning(
                    "Failed cv2 GPU decode",
                    video_path=video_path,
                    exc_info=ierr_rerr,
                    inference_time=time.time() - time_dict["det_time"],
                )
                failed_gpu_decode = True
                detections = Detections(
                    video_path=video_path,
                )
        if not failed_gpu_decode and self._use_gpu_decode:
            time_dict["end_det_time"] = time.time()
            log.debug(
                "Finished detecting",
                gpu_decode=True,
                inference_time=time_dict["end_det_time"]
                - time_dict["det_time"],
            )
        if not self._use_gpu_decode or failed_gpu_decode:
            if failed_gpu_decode:
                vid_reader = get_video_reader(
                    video_path, log, use_gpu=False, vcap=vid_cap
                )
            while True:
                frames, img_batch, frame_idx = get_frames_from_video_cv2(
                    vid_reader,
                    frame_idx,
                    end_frame,
                    processing_fps,
                    num_frames=self._batch_size,
                    use_clahe=self._use_clahe,
                )
                # (
                #     new_frames,
                #     new_img_batch,
                #     new_frame_idx,
                # ) = get_frames_from_video_decorder(
                #     video_path,
                #     new_frame_idx,
                #     end_frame,
                #     processing_fps,
                #     num_frames=self._batch_size,
                # )

                if len(img_batch) == 0:
                    break
                if len({i.shape for i in img_batch}) != 1:
                    # Different sized images do not work
                    log.warning(
                        "Frames of different sizes in video!",
                        video_path=video_path,
                    )
                    img_batch = [
                        cv2.resize(im, (vid_reader.width, vid_reader.height))
                        for im in img_batch
                    ]
                boxes_batch = self.detect(
                    img_batch, conf_threshold=conf_threshold
                )
                detections.extend(frames, boxes_batch)
        if failed_gpu_decode or not self._use_gpu_decode:
            time_dict["end_det_time"] = time.time()
            log.debug(
                "Finished detecting",
                gpu_decode=False,
                inference_time=time_dict["end_det_time"]
                - time_dict["det_time"],
                num_boxes=len(detections.flattened_box_list()),
                num_frames=len(detections.frames),
                len_detections=len(detections),
            )
        return detections

    def reset_state(self):
        pass

    @abc.abstractmethod
    def _detect_img_batch(
        self, img_batch, conf_threshold: typing.Optional[float] = None
    ) -> typing.List[typing.List[BaseDetection]]:
        """Run inference on a batch of images

        Args:
            img_batch ([list]): List of images of !!SAME SIZE!! in BGR format
            to run inference on
        """
        raise NotImplementedError

    @abc.abstractmethod
    def _detect_img(
        self, img, conf_threshold: typing.Optional[float] = None
    ) -> typing.List[BaseDetection]:
        """Run inference on a single image

        Args:
            img ([np.ndarray]): Single image in numpy array,
            of (H, W, C) with BGR channel order
        """
        raise NotImplementedError

    @abc.abstractmethod
    def _detect_img_batch_tensor(
        self,
        img_tensor,
        conf_threshold: typing.Optional[float] = None,
    ) -> typing.List[typing.List[BaseDetection]]:
        raise NotImplementedError

    def _detect_img_batch_tensor_wide_angle(
        self, img_tensor, conf_threshold: typing.Optional[float] = None
    ):
        h, w = img_tensor.shape[2:4]
        assert w / h > 2
        num_windows_h = 1
        num_windows_w = w // h + 1
        overlap_ratio = 0.2
        new_h = h
        new_w = int(w / (num_windows_w - (num_windows_w - 1) * overlap_ratio))
        stride_h = 0
        stride_w = int(new_w * (1 - overlap_ratio))
        results = [[] for i in range(img_tensor.shape[0])]
        for h_idx in range(num_windows_h):
            for w_idx in range(num_windows_w):
                start_h = h_idx * stride_h
                start_w = w_idx * stride_w
                sliced_tensor = img_tensor[
                    ..., start_h : start_h + new_h, start_w : start_w + new_w
                ]
                sliced_detections = self._detect_img_batch_tensor(
                    sliced_tensor, conf_threshold
                )
                for frame_dets in sliced_detections:
                    for box in frame_dets:
                        box._box = [
                            box.x1 + start_w,
                            box.y1 + start_h,
                            box.width,
                            box.height,
                        ]
                results = [
                    result + slice_dets
                    for result, slice_dets in zip(results, sliced_detections)
                ]
        return results

    def _detect_img_batch_tensor_sahi(
        self, img_tensor, conf_threshold: typing.Optional[float] = None
    ):
        log.debug("Using Sahi inference")
        h, w = img_tensor.shape[2:4]
        num_windows_h = 2
        num_windows_w = num_windows_h * round(w / h)
        overlap_ratio = 0.2
        new_h = int(h / (num_windows_h - (num_windows_h - 1) * overlap_ratio))
        new_w = int(w / (num_windows_w - (num_windows_w - 1) * overlap_ratio))
        stride_h = int(new_h * (1 - overlap_ratio))
        stride_w = int(new_w * (1 - overlap_ratio))
        results = [[] for i in range(img_tensor.shape[0])]
        for h_idx in range(num_windows_h):
            for w_idx in range(num_windows_w):
                start_h = h_idx * stride_h
                start_w = w_idx * stride_w
                sliced_tensor = img_tensor[
                    ..., start_h : start_h + new_h, start_w : start_w + new_w
                ]
                sliced_detections = self._detect_img_batch_tensor(
                    sliced_tensor, conf_threshold
                )
                for frame_dets in sliced_detections:
                    for box in frame_dets:
                        box._box = [
                            box.x1 + start_w,
                            box.y1 + start_h,
                            box.width,
                            box.height,
                        ]
                results = [
                    result + slice_dets
                    for result, slice_dets in zip(results, sliced_detections)
                ]
        return results
