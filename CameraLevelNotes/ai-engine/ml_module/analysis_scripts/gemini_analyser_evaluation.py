import argparse
import json
import os
import subprocess
import tempfile
import typing
from datetime import timed<PERSON><PERSON>
from pathlib import Path

import structlog
from tqdm import tqdm

import controller as ctrl
from common_utils.db_pool import db_adapter_pool
from common_utils.logger import log_setup
from config import backend_config as config
from ml_module.llm_alarm_analyzer.analyzer import LLMAlarmAnalyzer
from ml_module.utils import download_alarm_video
from models_rds.raw_alarms import RawAlarms

log = structlog.get_logger("hakimo", module="Location Alarm Analysis")


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "location_alarm_ids",
        help="File containing one location alarm ID per line",
    )
    parser.add_argument(
        "output_folder", help="Output folder to store analysis results"
    )
    parser.add_argument(
        "--n_alarms",
        type=int,
        default=4,
        help="If > 1 it combines n consecutive alarms from same camera",
    )
    return parser.parse_args()


def read_location_alarm_ids(file_path: str) -> typing.List[int]:
    with open(file_path, "r") as f:
        return [int(line.strip()) for line in f if line.strip()]


def convert_to_half_speed(video_path):
    file_dir = os.path.dirname(video_path)
    file_name = os.path.basename(video_path)
    temp_output = os.path.join(file_dir, f"temp_{file_name}")
    try:
        cmd = [
            "ffmpeg",
            "-i",
            video_path,
            "-filter_complex",
            "[0:v]setpts=2.0*PTS[v];[0:a]atempo=0.5[a]",
            "-map",
            "[v]",
            "-map",
            "[a]",
            "-c:v",
            "libx264",  # Re-encode video with h.264
            "-c:a",
            "aac",  # Re-encode audio with AAC
            "-preset",
            "medium",  # Encoding preset (balance between speed and quality)
            "-y",  # Overwrite output file if it exists
            temp_output,
        ]
        subprocess.run(cmd, check=True)
        os.replace(temp_output, video_path)
        return True
    except subprocess.CalledProcessError as e:
        print(f"FFmpeg error: {e}")
        # Clean up temp file if it exists
        if os.path.exists(temp_output):
            os.remove(temp_output)
        return False

    except Exception as e:
        print(f"Error: {e}")
        # Clean up temp file if it exists
        if os.path.exists(temp_output):
            os.remove(temp_output)
        return False


def download_raw_alarm_video(
    output_folder: str,
    loc_alarm_id: int,
    raw_alarm: RawAlarms,
    half_speed: bool = False,
):
    video_dir = Path(output_folder) / str(loc_alarm_id) / str(raw_alarm.uuid)
    video_dir.mkdir(parents=True, exist_ok=True)
    video_path = video_dir / os.path.basename(str(raw_alarm.video_path))
    if not video_path.exists():
        download_alarm_video(raw_alarm.video_path, str(video_dir))
    if half_speed:
        convert_to_half_speed(str(video_path))
    return video_path


def raw_alarm_iterator(
    location_alarm, n: int, output_folder: str, loc_alarm_id: int
) -> typing.Iterator[typing.Dict]:
    for raw_alarm in location_alarm.raw_alarms:
        if not isinstance(raw_alarm, RawAlarms):
            continue
        return_dict = {}
        video_path = download_raw_alarm_video(
            output_folder, loc_alarm_id, raw_alarm
        )
        return_dict["video_path"] = video_path
        return_dict["uuid"] = raw_alarm.uuid
        return_dict["alarm_timestamp_utc"] = raw_alarm.alarm_timestamp_utc
        return_dict["video_start_timestamp_utc"] = (
            raw_alarm.video_start_timestamp_utc
        )
        return_dict["video_end_timestamp_utc"] = (
            raw_alarm.video_end_timestamp_utc
        )
        return_dict["source_entity_id"] = raw_alarm.source_entity_id
        yield return_dict


def concatenate_videos(video_paths: typing.List[str], video_out_path: str):
    if not video_paths:
        raise ValueError("Empty list of video paths provided")

    # Check that all video files exist
    for video_path in video_paths:
        if not os.path.isfile(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")

    # Create a temporary file for the FFmpeg concat demuxer
    with tempfile.NamedTemporaryFile(
        mode="w", suffix=".txt", delete=False
    ) as temp_file:
        concat_file_path = temp_file.name

        # Write file paths to the concat file
        for video_path in video_paths:
            # Use absolute paths to avoid issues with working directory
            abs_path = os.path.abspath(video_path)
            # Escape single quotes and backslashes for FFmpeg
            escaped_path = abs_path.replace("'", "'\\''").replace("\\", "\\\\")
            temp_file.write(f"file '{escaped_path}'\n")

    try:
        # Build FFmpeg command for concatenation
        ffmpeg_cmd = [
            "ffmpeg",
            "-y",  # Overwrite output file if it exists
            "-f",
            "concat",
            "-safe",
            "0",  # Allow absolute paths
            "-i",
            concat_file_path,
            "-c",
            "copy",  # Copy streams without re-encoding (fast)
            video_out_path,
        ]

        print(f"Concatenating {len(video_paths)} videos...")
        _ = subprocess.run(ffmpeg_cmd, check=True, stderr=subprocess.PIPE)

        print(f"Successfully concatenated videos to: {video_out_path}")
        return True

    except subprocess.CalledProcessError as e:
        print(f"Error during FFmpeg concatenation: {e.stderr.decode('utf-8')}")
        return False
    finally:
        # Clean up the temporary concat file
        if os.path.exists(concat_file_path):
            try:
                os.remove(concat_file_path)
            except Exception as e:
                print(
                    f"Warning: Could not remove temporary file {concat_file_path}: {e}"
                )


def raw_alarm_multi_iterator(
    location_alarm, n: int, output_folder: str, loc_alarm_id: int
) -> typing.Iterator[typing.Dict]:
    grouped_raw_alarms = {}
    temp_path = "temp.mp4"
    for raw_alarm in location_alarm.raw_alarms:
        grouped_raw_alarms[raw_alarm.source_entity_id] = (
            grouped_raw_alarms.get(raw_alarm.source_entity_id, [])
            + [raw_alarm]
        )
        raw_alarm.video_path = download_raw_alarm_video(
            output_folder, loc_alarm_id, raw_alarm
        )
    for k in grouped_raw_alarms:
        grouped_raw_alarms[k] = sorted(
            grouped_raw_alarms[k], key=lambda x: x.alarm_timestamp_utc
        )
    for k in grouped_raw_alarms:
        raw_alarms = grouped_raw_alarms[k]
        # iterate through the raw_alarms n at a time
        for i in range(0, len(raw_alarms), n):
            return_dict = {}
            concatenate_videos(
                [raw_alarm.video_path for raw_alarm in raw_alarms[i : i + n]],
                temp_path,
            )
            return_dict["uuid"] = "_".join(
                [raw_alarm.uuid for raw_alarm in raw_alarms[i : i + n]]
            )
            return_dict["source_entity_id"] = raw_alarms[i].source_entity_id
            return_dict["video_path"] = temp_path
            return_dict["alarm_timestamp_utc"] = raw_alarms[
                i
            ].alarm_timestamp_utc
            return_dict["video_start_timestamp_utc"] = raw_alarms[
                i
            ].video_start_timestamp_utc
            return_dict["video_end_timestamp_utc"] = raw_alarms[
                min(i + n - 1, len(raw_alarms) - 1)
            ].video_end_timestamp_utc
            yield return_dict
    os.remove(temp_path)


def download_and_analyze_location_alarms(
    location_alarm_ids: typing.List[int],
    output_folder: str,
    gemini_analyzer: LLMAlarmAnalyzer,
    n: int = 1,
) -> typing.Tuple[typing.List[typing.Dict[str, str]], int, int]:
    """
    Downloads videos for each location alarm and runs analysis
    """
    ra_iter = raw_alarm_iterator if n == 1 else raw_alarm_multi_iterator
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    db_adapter = db_adapter_pool
    controller = ctrl.ControllerMap(db_adapter)

    all_analyses = []
    raw_alarm_level_escalations = 0
    location_alarm_level_escalations = 0

    for loc_alarm_id in tqdm(location_alarm_ids):
        log.info("Processing location alarm", location_alarm_id=loc_alarm_id)

        # Get location alarm details
        location_alarm = controller.location_alarms.get_location_alarm_by_id(
            loc_alarm_id
        )

        if not location_alarm:
            log.error(
                "Location alarm not found", location_alarm_id=loc_alarm_id
            )
            continue

        # Get location details for timezone
        location = controller.locations.get_location_by_id(
            location_alarm.location_id
        )
        if not location:
            log.error(
                "Location not found", location_id=location_alarm.location_id
            )
            continue
        sop = controller.sop.get_sop(
            tenant_id=location.tenant_id, location_id=location.id
        )
        # Get SOP if available
        # sop = location.sop if location else None

        alarm_analyses = []
        alarm_escalation = False
        # Process each raw alarm
        for raw_alarm in location_alarm.raw_alarms:
            assert isinstance(raw_alarm, RawAlarms)
            if not raw_alarm.video_path:
                log.warning(
                    "No video path for raw alarm", raw_alarm_id=raw_alarm.uuid
                )
        for raw_alarm_dict in ra_iter(
            location_alarm, n, output_folder, loc_alarm_id
        ):
            if not raw_alarm_dict["video_path"]:
                log.warning(
                    "No video path for raw alarm",
                    raw_alarm_id=raw_alarm_dict["uuid"],
                )
                continue
            if raw_alarm_dict[
                "alarm_timestamp_utc"
            ] - location_alarm.alarm_time_utc > timedelta(seconds=60 * 5):
                continue
            # Download video to temp location
            video_dir = Path(output_folder) / str(loc_alarm_id)
            video_path = raw_alarm_dict["video_path"]
            try:
                # Download video (you'll need to implement this based on your storage system)
                # download_video(raw_alarm.video_path, video_path)
                camera = controller.camera.get_camera_by_id(
                    raw_alarm_dict["source_entity_id"]
                )
                # Analyze individual video
                analysis_path = video_dir / "analysis.json"
                # if analysis_path.exists():
                if False:
                    with open(analysis_path, "r") as f:
                        saved_analysis = json.load(f)
                        analysis = saved_analysis.get("analysis")
                        recommendation = saved_analysis.get("recommendation")
                else:
                    analysis, recommendation = gemini_analyzer.analyze_alarm(
                        str(video_path),
                        (
                            raw_alarm.video_start_timestamp_utc,
                            raw_alarm.video_end_timestamp_utc,
                        ),
                        sop.sop,
                        raw_alarm.source_entity_id,
                        location.timezone,
                        send_inline_video=True,
                    )
                alarm_escalation = (
                    alarm_escalation or "escalate" in recommendation.lower()
                )
                if analysis and recommendation:
                    alarm_analyses.append(
                        {
                            "text": (
                                f"Camera: {camera.name} "
                                f"Video start time: {raw_alarm.video_start_timestamp_utc} "
                                f"Video end time: {raw_alarm.video_end_timestamp_utc} "
                                f"Video explanation: {analysis} "
                            )
                        }
                    )

                    # Save individual analysis
                    with open(video_dir / "analysis.json", "w") as f:
                        json.dump(
                            {
                                "analysis": analysis,
                                "recommendation": recommendation,
                            },
                            f,
                            indent=2,
                        )

            except Exception as e:
                log.error(
                    "Error processing video",
                    raw_alarm_id=raw_alarm.uuid,
                    error=str(e),
                )
                continue
        raw_alarm_level_escalations += alarm_escalation
        # Run location-level analysis if we have any successful video analyses
        if alarm_analyses:
            summary, recommendation = gemini_analyzer.analyze_location_alarm(
                alarm_analyses, sop, location.timezone
            )

            if summary and recommendation:
                # Save location-level analysis
                with open(
                    Path(output_folder)
                    / str(loc_alarm_id)
                    / "location_analysis.json",
                    "w",
                ) as f:
                    json.dump(
                        {
                            "summary": summary,
                            "recommendation": recommendation,
                            "individual_analyses": alarm_analyses,
                        },
                        f,
                        indent=2,
                    )
                all_analyses.append(
                    {
                        "location_alarm_id": loc_alarm_id,
                        "summary": summary,
                        "recommendation": recommendation,
                    }
                )
                location_alarm_level_escalations += (
                    "escalate" in recommendation.lower()
                )

    return (
        all_analyses,
        raw_alarm_level_escalations,
        location_alarm_level_escalations,
    )


def main():
    log_setup()
    args = parse_args()

    # Initialize Gemini analyzer
    gemini_analyzer = LLMAlarmAnalyzer(config.HAIE.GEMINI_ALARM_ANALYZER_KEY)

    # Read location alarm IDs
    location_alarm_ids = read_location_alarm_ids(args.location_alarm_ids)

    # Process alarms
    analyses, raw_alarm_level_escalations, location_alarm_level_escalations = (
        download_and_analyze_location_alarms(
            location_alarm_ids,
            args.output_folder,
            gemini_analyzer,
            args.n_alarms,
        )
    )

    # Save overall results
    with open(Path(args.output_folder) / "all_analyses.json", "w") as f:
        json.dump(analyses, f, indent=2)

    log.info(
        "Analysis complete",
        total_alarms=len(analyses),
        raw_alarm_level_escalations=raw_alarm_level_escalations,
        location_alarm_level_escalations=location_alarm_level_escalations,
    )


if __name__ == "__main__":
    main()
