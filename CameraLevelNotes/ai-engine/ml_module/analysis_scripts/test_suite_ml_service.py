from argparse import Argument<PERSON><PERSON><PERSON>
from datetime import datetime, <PERSON><PERSON><PERSON>
from pathlib import Path
from uuid import uuid4

from common_utils.db_pool import db_adapter_pool
from common_utils.redis.redis_publisher_utils import RedisPublisherUtils
from common_utils.s3_utils import upload_file_to_s3
from common_utils.video_utils import get_video_duration
from controller import ControllerMap
from interfaces.alarm import Alarm, AlarmState
from ml_module.analysis_scripts.utils import get_alarms_from_alarm_list
from ml_module.utils import get_instance_files
from models_rds.rds_client import RDSClient


def parse_args():
    parser = ArgumentParser()
    parser.add_argument("alarm_dir", type=str)
    parser.add_argument("alarm_list", type=str)
    parser.add_argument(
        "--source_entity_id",
        type=str,
        default="b1501bdc-5891-47c4-851f-375bd4be23b2",
    )
    parser.add_argument(
        "--tenant_id", type=str, default="elevance-health-7adf71c1da2d43c1"
    )
    parser.add_argument("--add-to-queue", action="store_true")
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    alarm_list = get_alarms_from_alarm_list(args.alarm_list)
    alarm_videos = get_instance_files(args.alarm_dir, "*.mp4")
    db = db_adapter_pool
    rds_client = RDSClient(db)
    ctrl = ControllerMap(db)
    redis_pub_client = RedisPublisherUtils()
    for alarm_uuid in alarm_list:
        if alarm_uuid not in alarm_videos:
            print("Skipping", alarm_videos)
            continue
        alarm_video_file = alarm_videos[alarm_uuid]
        s3_path = f"test-suite/{Path(alarm_video_file).name}".replace(
            ".mp4", f"{str(uuid4())}.mp4"
        )
        upload_file_to_s3("haie-videos", s3_path, alarm_video_file)
        url = f"https://haie-videos.s3-us-west-2.amazonaws.com/{s3_path}"
        alarm_timestamp = datetime.now()
        alarm = Alarm(
            **{
                "alarm_time": alarm_timestamp,
                "alarm_type": "Door Forced Open",
                "source_entity_id": args.source_entity_id,
                "source_entity_type": "DOOR",
                "source_system": "Prowatch",
                "source_id": str(uuid4()),
                "door_uuid": args.source_entity_id,
                "tenant_id": args.tenant_id,
            }
        )
        video_duration = get_video_duration(alarm_video_file)
        raw_alarm_uuid = rds_client.write_update_raw_alarms(alarm)
        print(raw_alarm_uuid, video_duration)
        rds_client.update_raw_alarms_with_video_metadata(
            raw_alarm_uuid,
            alarm_timestamp,
            alarm_timestamp + timedelta(seconds=video_duration),
            video_path=url,
        )
        ctrl.alarm.update_alarm_processed_times(
            alarm,
            alarm_timestamp,
            alarm_timestamp + timedelta(seconds=video_duration),
        )
        ctrl.alarm_media.add_media(raw_alarm_uuid, url, media_type="video")
        ctrl.alarm.transition_alarm(
            raw_alarm_uuid,
            args.tenant_id,
            AlarmState.UNPROCESSED,
        )
        if args.add_to_queue:
            redis_pub_client.add_message_to_queue(alarm)
