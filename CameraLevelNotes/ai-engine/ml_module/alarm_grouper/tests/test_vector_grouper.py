import datetime
import typing

import pytest
import structlog

from controller import ControllerMap
from controller.alarm.alarm_filters import AlarmFilters
from controller.embeddings.tests.mock_pinecone import Index, create_index, init
from interfaces.entities.person import Person
from interfaces.location_alarms import LocationAlarmStatus
from interfaces.tags import Tags
from interfaces.tenant_config import TenantConfig
from ml_module.alarm_grouper.grouper import AlarmGrouper, _AlarmStrategyAction
from ml_module.alarm_grouper.tests.test_motion_data import AlarmGrouperMockData
from ml_module.ml_interfaces.alarm_grouper import AlarmGroupEventType
from ml_module.ml_service.utils import add_entities_to_pinecone
from ml_module.track import BaseDetection, BaseTrack
from models_rds.raw_alarms import RawAlarms
from models_rds.rds_client import RDSClient

log = structlog.get_logger("hakimo", module="TestVectorGrouper")


class TestVectorGrouper(AlarmGrouperMockData):
    @pytest.fixture(scope="session")
    def pinecone_index(self):
        init()
        create_index("mock_index", 4)
        return Index("mock_index")

    @pytest.fixture
    def controller(
        self, rds_client: RDSClient, pinecone_index: Index
    ) -> ControllerMap:
        ctrl = ControllerMap(rds_client.db_adapter, pinecone_index)
        ctrl.pinecone._embedding_dim = 4
        return ctrl

    @pytest.fixture
    def alarm_grouper(self, controller: ControllerMap) -> AlarmGrouper:
        return AlarmGrouper(controller)

    @pytest.mark.parametrize(
        "tenant_id,location_alarm_resolutions,expected_result",
        [
            ("test_tenant1", True, 3),
            ("test_tenant2", True, 4),
            ("test_tenant5", False, 2),
            ("test_tenant5", [False, True, True], 1),
        ],
    )
    def test_single_entity_grouping(
        self,
        alarm_grouper: AlarmGrouper,
        controller: ControllerMap,
        rds_client: RDSClient,
        tenant_id: str,
        location_alarm_resolutions: typing.Union[typing.List[bool], bool],
        expected_result: typing.Optional[int],
    ):
        # Populate db
        location = controller.locations.get_all_tenant_locations([tenant_id])
        log.debug("Location", location=location, tenant_id=tenant_id)
        t_config: TenantConfig = controller.tenant.get_config(tenant_id)
        log.debug("Tenant Config", t_config=t_config)
        alarms = controller.alarm.fetch_alarms(
            [tenant_id], AlarmFilters(order_desc=False), limit=4
        )
        log.debug("Alarms", alarms=alarms)
        alarms = controller.alarm.by_ids(
            [f"ra{i}-motion-t{tenant_id[-1]}" for i in range(1, 5)]
        )
        log.debug("Alarms", alarms=alarms)

        for i, alarm in enumerate(alarms):
            raw_alarm_object = rds_client.get_alarm_object(
                alarm.uuid, tenant_id
            )
            entities = [
                Person(
                    person_id=f"person-{i}",
                    track=BaseTrack(
                        frame_idx=list(range(5)),
                        boxes=[
                            BaseDetection.create(
                                "person",
                                [0, 0, 10, 10],
                                conf=0.9,
                                feature=[0, 1, 2, 3],
                            )
                            for _ in range(5)
                        ],
                        track_id=f"person-{i}",
                    ),
                )
                for _ in range(1)
            ]
            ml_uuid = controller.alarm.update_tap(alarm.uuid, tenant_id, 90)
            controller.ml_output.update_ml_output(
                ml_uuid, processed_frames=list(range(5))
            )
            controller.ml_output.add_entities(ml_uuid, entities=entities)
            tags = Tags(entities, {"tags": []})
            tags.add_video_tag("PERSON", 100)
            controller.alarm.update_video_tags(tenant_id, ml_uuid, tags)
            add_entities_to_pinecone(
                raw_alarm_object, entities, controller, t_config.checkVectorDb
            )

        alarms = controller.alarm.by_ids(
            [f"ra{i}-motion-t{tenant_id[-1]}" for i in range(1, 5)]
        )
        log.debug("Alarms", alarms=alarms)
        for idx, alarm in enumerate(alarms[:-1]):
            controller.location_alarms.create_new_location_alarm(
                alarm, location[0].id
            )
            location_alarm = (
                controller.location_alarms.get_location_alarms_for_raw_alarm(
                    alarm.uuid
                )[0]
            )
            if (
                isinstance(location_alarm_resolutions, list)
                and location_alarm_resolutions[idx]
            ) or (
                isinstance(location_alarm_resolutions, bool)
                and location_alarm_resolutions
            ):
                controller.location_alarms.update_location_alarm_status(
                    location_alarm,
                    LocationAlarmStatus.RESOLVED,
                    update_text="Person Detected - Authorized employee",
                )
        alarm_grouper._group_motion_alarm_event(
            alarms[-1], AlarmGroupEventType.UPDATE_ML_OUTPUT
        )
        assert (
            expected_result
            == controller.location_alarms.get_location_alarms_for_raw_alarm(
                alarms[-1].uuid
            )[0].int_id
        )

    @pytest.mark.parametrize(
        "num_entities, tenant_id, expected_result, expected_tap",
        [
            ([1, 2, 2, 4], "test_tenant1", 4, 90),
            ([1, 1, 1, 2], "test_tenant1", 4, 90),
            ([2, 2, 2, 2], "test_tenant1", 3, 91),
            ([1, 2, 2, 2], "test_tenant1", 3, 91),
            ([1, 1, 2, 2], "test_tenant1", 4, 90),
            ([1, 1, 1, 0], "test_tenant1", 4, 90),
            ([1, 0, 0, 1], "test_tenant1", 4, 90),
            ([[1, 2], [1, 2], [1, 2], [1, 2]], "test_tenant1", 3, 91),
            ([[1, 2], [1, 2], [1, 2], [2, 3]], "test_tenant1", 4, 90),
            ([[1, 2, 3], [1, 2], [1, 3], [2, 3]], "test_tenant1", 3, 91),
            ([[], [], [], [1, 2, 3]], "test_tenant1", 4, 90),
            ([[1, 2, 3], [], [], [1, 2, 3]], "test_tenant1", 4, 90),
            ([[1, 2], [], [], [1, 2, 3]], "test_tenant1", 4, 90),
            ([[1, 2, 3], [1], [], [1]], "test_tenant1", 2, 91),
            ([[2, 3], [3, 1], [1, 3], [2]], "test_tenant1", 4, 90),
            ([[2, 3], [1, 3], [3, 1], [2, 2]], "test_tenant1", 4, 90),
            ([[2, 3, 1], [3, 2], [3, 1], [2, 2]], "test_tenant1", 2, 91),
            ([1, 1, 1, 1], "test_tenant3", 3, 92),
            ([[1], [2], [1], [1]], "test_tenant3", 3, 92),
            ([[1], [2], [3], [1]], "test_tenant3", 4, 90),
            ([[1, 2], [2], [1], [1]], "test_tenant3", 3, 92),
            ([[1, 2], [1, 2], [1], [1, 2]], "test_tenant3", 3, 92),
            ([[1], [2], [1, 2], [1]], "test_tenant3", 3, 92),
            ([[1], [2], [1, 2], [1, 2]], "test_tenant3", 3, 92),
            ([1, 1, 1, 1], "test_tenant4", 2, 92),
            ([[1], [2], [1], [1]], "test_tenant4", 4, 90),
            ([[1], [1], [2], [1]], "test_tenant4", 2, 92),
        ],
    )
    def test_n_entity_grouping(
        self,
        alarm_grouper: AlarmGrouper,
        controller: ControllerMap,
        rds_client: RDSClient,
        num_entities: typing.List[typing.Union[int, typing.List[int]]],
        tenant_id: str,
        expected_result: typing.Optional[int],
        expected_tap: float,
    ):
        # Populate db
        location = controller.locations.get_all_tenant_locations([tenant_id])
        log.debug("Location", location=location, tenant_id=tenant_id)
        t_config: TenantConfig = controller.tenant.get_config(tenant_id)
        log.debug("Tenant Config", t_config=t_config)
        alarms = controller.alarm.fetch_alarms(
            [tenant_id], AlarmFilters(order_desc=False), limit=4
        )
        log.debug("Alarms", alarms=alarms)
        alarms = controller.alarm.by_ids(
            [f"ra{i}-motion-t{tenant_id[-1]}" for i in range(1, 5)]
        )
        log.debug("Alarms", alarms=alarms)

        for i, alarm in enumerate(alarms):
            raw_alarm_object = rds_client.get_alarm_object(
                alarm.uuid, tenant_id
            )
            controller.pinecone.remove_alarm_embeddings(
                [alarm.uuid], alarm.source_entity_id
            )
            entity_index = (
                range(num_entities[i])
                if isinstance(num_entities[i], int)
                else num_entities[i]
            )
            entities = [
                Person(
                    person_id=f"person-{i}-{j}",
                    track=BaseTrack(
                        frame_idx=list(range(5)),
                        boxes=[
                            BaseDetection.create(
                                "person",
                                [0, 0, 10, 10],
                                conf=0.9,
                                feature=[10 * emb, 1, 2, 3],
                            )
                            for _ in range(5)
                        ],
                        track_id=f"person-{i}-{j}",
                    ),
                )
                for j, emb in enumerate(entity_index)
            ]
            ml_uuid = controller.alarm.update_tap(alarm.uuid, tenant_id, 90)
            controller.ml_output.update_ml_output(
                ml_uuid, processed_frames=list(range(5))
            )
            controller.ml_output.add_entities(ml_uuid, entities=entities)
            tags = Tags(entities, {"tags": []})
            tags.add_video_tag("PERSON", 100)
            controller.alarm.update_video_tags(tenant_id, ml_uuid, tags)
            add_entities_to_pinecone(
                raw_alarm_object, entities, controller, t_config.checkVectorDb
            )

        alarms = controller.alarm.by_ids(
            [f"ra{i}-motion-t{tenant_id[-1]}" for i in range(1, 5)]
        )
        log.debug("Alarms", alarms=alarms)
        for alarm in alarms[:-1]:
            controller.location_alarms.create_new_location_alarm(
                alarm, location[0].id
            )
            location_alarm = (
                controller.location_alarms.get_location_alarms_for_raw_alarm(
                    alarm.uuid
                )[0]
            )
            controller.location_alarms.update_location_alarm_status(
                location_alarm,
                LocationAlarmStatus.RESOLVED,
                update_text="Person Detected - Authorized employee",
            )
        alarm_grouper._group_motion_alarm_event(
            alarms[-1], AlarmGroupEventType.UPDATE_ML_OUTPUT
        )
        assert (
            expected_result
            == controller.location_alarms.get_location_alarms_for_raw_alarm(
                alarms[-1].uuid
            )[0].int_id
        )
        tap, _ = controller.alarm.get_tap_tags(
            alarms[-1].uuid, alarms[-1].tenant_id
        )
        assert tap == expected_tap

    @pytest.mark.parametrize(
        "relevant_location_alarms,raw_alarm,expected_location_alarm_id, location_id",
        [
            (
                None,
                RawAlarms(
                    alarm_timestamp_utc=datetime.datetime(
                        2023, 1, 1, 10, 30, 0
                    )
                ),
                0,
                33,
            ),
            (
                None,
                RawAlarms(
                    alarm_timestamp_utc=datetime.datetime(2023, 1, 6, 10)
                ),
                None,
                35,
            ),
            (
                None,
                RawAlarms(
                    alarm_timestamp_utc=datetime.datetime(2023, 1, 1, 11, 1, 5)
                ),
                0,
                33,
            ),
            (
                None,
                RawAlarms(
                    alarm_timestamp_utc=datetime.datetime(2023, 1, 1, 12, 1, 5)
                ),
                0,
                33,
            ),
        ],
    )
    def test_get_relevant_escalated_location_alarm(
        self,
        relevant_location_alarms,
        raw_alarm,
        expected_location_alarm_id,
        location_id,
        controller,
    ):
        alarm_grouper = AlarmGrouper(controller=controller)

        ret = alarm_grouper._get_relevant_location_alarm(
            relevant_location_alarms,
            raw_alarm,
            datetime.timedelta(seconds=60),
            location=location_id,
        )
        print(ret)
        if expected_location_alarm_id is None:
            assert ret.action == _AlarmStrategyAction.CREATE_NEW
        else:
            assert ret.alarm.int_id == expected_location_alarm_id
            assert ret.action == _AlarmStrategyAction.ADD_TO_EXISTING
