import bisect
import datetime
import typing
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum

import numpy as np
import structlog

import controller as ctrl
from common_utils.metrics_definitions import (
    MOTION_LOCATION_ALARM_COUNTER,
    MOTION_RAW_ALARM_LOCATION_COUNTER,
)
from config import backend_config as config
from config import ml_config
from controller.location_alarms.location_alarm_filters import (
    LocationAlarmFilters,
)
from interfaces.alarm import ALARM_TYPE_INTEGRATED_MOTION, ALARM_TYPE_MOTION
from interfaces.location_alarms import LocationAlarmStatus
from interfaces.source_systems import ACSSource
from interfaces.tags import Tags, TagTypes
from interfaces.tenant_config import TenantAlarmProcessingConfig, TenantConfig
from ml_module.ml_interfaces.alarm_grouper import AlarmGroupEventType
from models_rds.cameras import Cameras
from models_rds.location_alarms import LocationAlarms
from models_rds.locations import Locations
from models_rds.raw_alarms import RawAlarms

from .utils import match_embeddings

log = structlog.get_logger("hakimo.ml_module", module="Alarm Grouper")


class _AlarmStrategyAction(Enum):
    # Merge two location alarms
    MERGE = "merge"
    # Create new location alarm
    CREATE_NEW = "new"
    # Add to existing location alarm
    ADD_TO_EXISTING = "add"


@dataclass
class _AlarmStrategy:
    action: _AlarmStrategyAction
    alarm: typing.Optional[LocationAlarms]
    mark_pending: bool = True


class AlarmGrouper:
    def __init__(self, controller: ctrl.ControllerMap):
        self._ctrl = controller
        _motion_alarm_grouping_time = 86400
        if "MOTION_ALARM_GROUPING_TIME_SECS" in config.HAIE:
            _motion_alarm_grouping_time = (
                config.HAIE.MOTION_ALARM_GROUPING_TIME_SECS
            )
        self._motion_alarm_grouping_time = datetime.timedelta(
            seconds=_motion_alarm_grouping_time
        )
        _motion_vector_grouping_time = 420
        if "MOTION_VECTOR_GROUPING_TIME_SECS" in config.HAIE:
            _motion_vector_grouping_time = (
                config.HAIE.MOTION_VECTOR_GROUPING_TIME_SECS
            )
        self._motion_vector_grouping_time = datetime.timedelta(
            seconds=_motion_vector_grouping_time
        )
        _escalation_grouping_time = 7200
        if "ESCALATION_GROUPING_TIME" in config.HAIE:
            _escalation_grouping_time = config.HAIE.ESCALATION_GROUPING_TIME
        self._escalation_grouping_time = datetime.timedelta(
            seconds=_escalation_grouping_time
        )

    def new_event(self, alarm_id: str, update_type: AlarmGroupEventType):
        alarm = self._ctrl.alarm.by_id(alarm_id)
        if alarm is None:
            raise ValueError("Invalid alarm ID")
        alarm_type = (
            self._ctrl.alarm_types.get_alarm_type_name_from_alarm_type_id(
                alarm.alarm_type_id
            )
        )

        if (
            alarm_type in [ALARM_TYPE_MOTION, ALARM_TYPE_INTEGRATED_MOTION]
            and alarm.source_system == ACSSource.MOTION
        ):
            self._group_motion_alarm_event(alarm, update_type)
        else:
            self._group_alarm_event(alarm, update_type)

    def _group_motion_alarm_event(
        self, raw_alarm: RawAlarms, update_type: AlarmGroupEventType
    ) -> None:
        """Processes a new event for a motion alarm, such as an update in TAP"""
        if update_type == AlarmGroupEventType.NEW_RAW_ALARM:
            # Right now nothing to be done on new alarm
            return
        if update_type == AlarmGroupEventType.UPDATE_ML_OUTPUT:
            if raw_alarm.true_alarm_probability < 50:
                # no action for resolved alarms
                return
            camera = self._ctrl.camera.get_camera_by_id(
                raw_alarm.source_entity_id
            )
            if camera is None:
                raise ValueError(
                    f"No camera found for motion alarm {raw_alarm.source_entity_id}"
                )
            location = self._ctrl.locations.get_location_by_id(
                camera.location_id
            )
            if location is None:
                raise ValueError(f"Location not set for camera {camera.uuid}")
            # Check if raw_alarm is already mapped
            if (
                location_alarm
                := self._ctrl.location_alarms.get_location_alarms_for_raw_alarm(
                    raw_alarm.uuid
                )
            ):
                log.debug(
                    "Alarm already mapped to location alarm",
                    location_alarm=location_alarm,
                )
                return
            _, tags = self._ctrl.alarm.get_tap_tags(
                raw_alarm.uuid, raw_alarm.tenant_id
            )
            if tags is not None and any(
                [
                    TagTypes.VIDEO_CORRUPT in tags,
                    TagTypes.VIDEO_UNAVAILABLE in tags,
                    TagTypes.MORE_VIDEO_UNAVAILABLE in tags,
                ]
            ):
                log.debug("Video issues, not adding to location alarm")
                return
            tenantConf: TenantConfig = self._ctrl.tenant.get_config(
                camera.tenant_id, config_col="config"
            )
            # We need location alarms that are within grouping_time of this alarm
            # And are pending
            grouping_time = (
                datetime.timedelta(seconds=tenantConf.vectorGroupingTime)
                if tenantConf.vectorGroupingTime
                else self._motion_vector_grouping_time
            )

            filters = LocationAlarmFilters(
                location_ids=[location.id],
                latest_event_time_start=raw_alarm.alarm_timestamp_utc
                - grouping_time,
                utc_time_interval=(
                    None,
                    raw_alarm.alarm_timestamp_utc + grouping_time,
                ),
                tenant_ids=[location.tenant_id],
            )
            alarmProcessingConf: TenantAlarmProcessingConfig = (
                self._ctrl.tenant.get_config(
                    camera.tenant_id, config_col="alarmProcessingConfig"
                )
            )

            relevant_location_alarms = (
                self._ctrl.location_alarms.get_location_alarms(filters)
            )
            log.info(
                "Relevant Alarms",
                relevant_location_alarms=[
                    (r.int_id, r.current_status)
                    for r in relevant_location_alarms
                ],
            )
            dedup_start_time = datetime.datetime.now()

            matching_result = self.find_raw_alarm_with_closest_embedding(
                raw_alarm,
                relevant_location_alarms,
                tenantConf,
                tags,
                camera,
            )
            same_camera, best_matching_alarm, all_matches = False, None, None

            if matching_result is not None:
                same_camera, best_matching_alarm, all_matches = matching_result
                comment_str = "; ".join(
                    [
                        f"{track_id}:>"
                        + ",".join(
                            [f"{match[0]}:{match[1]}" for match in matches]
                        )
                        for track_id, matches in all_matches.items()
                    ]
                )
                if same_camera:
                    self._ctrl.alarm.update_tap(
                        raw_alarm.uuid,
                        raw_alarm.tenant_id,
                        91,
                        new_ml_outputs=False,
                    )
                    self._ctrl.alarm.add_comment(
                        raw_alarm.uuid,
                        raw_alarm.tenant_id,
                        f"Resolved due to Location Alarms:= {comment_str}",
                    )
                else:
                    self._ctrl.alarm.update_tap(
                        raw_alarm.uuid,
                        raw_alarm.tenant_id,
                        92,
                        new_ml_outputs=False,
                    )
                    self._ctrl.alarm.add_comment(
                        raw_alarm.uuid,
                        raw_alarm.tenant_id,
                        f"Resolution due to Neighbouring Location Alarms:= {comment_str}",
                    )

            total_dedup_time = (
                datetime.datetime.now() - dedup_start_time
            ).total_seconds()

            log.info(
                "Matching Result",
                same_camera=same_camera,
                best_matching_alarm=best_matching_alarm.int_id
                if best_matching_alarm
                else None,
                all_matches=all_matches,
                total_dedup_time=total_dedup_time,
            )

            # If DEDUP takes time, recollect relevant location alarms in case a new alarm was added in parallel
            if total_dedup_time > 0.5:
                filters = LocationAlarmFilters(
                    location_ids=[location.id],
                    latest_event_time_start=raw_alarm.alarm_timestamp_utc
                    - self._motion_alarm_grouping_time,
                    utc_time_interval=(
                        None,
                        raw_alarm.alarm_timestamp_utc
                        + self._motion_alarm_grouping_time,
                    ),
                    tenant_ids=[location.tenant_id],
                )
                relevant_location_alarms = (
                    self._ctrl.location_alarms.get_location_alarms(filters)
                )
            else:
                relevant_location_alarms = [
                    l
                    for l in relevant_location_alarms
                    if l.latest_event_utc
                    >= raw_alarm.alarm_timestamp_utc
                    - self._motion_alarm_grouping_time
                    and l.alarm_time_utc
                    <= raw_alarm.alarm_timestamp_utc
                    + self._motion_alarm_grouping_time
                ]

            if alarmProcessingConf.locationAlarmAddToPendingOnly:
                relevant_location_alarms = [
                    l
                    for l in relevant_location_alarms
                    if l.current_status
                    in [
                        LocationAlarmStatus.PENDING,
                        LocationAlarmStatus.ANALYZING,
                    ]
                ]
            if (
                tenantConf.addToNeighbouringCameras
                and isinstance(camera.camera_raw_info, dict)
                and "neighbouring_camera_ids" in camera.camera_raw_info
                and (
                    neighbouring_camera_ids := camera.camera_raw_info.get(
                        "neighbouring_camera_ids"
                    )
                )
                is not None
                and isinstance(neighbouring_camera_ids, list)
                and len(neighbouring_camera_ids) != 0
                and all(isinstance(n, str) for n in neighbouring_camera_ids)
            ):
                neighbouring_camera_ids.append(raw_alarm.source_entity_id)
                relevant_location_alarms = [
                    i
                    for i in relevant_location_alarms
                    if any(
                        r.source_entity_id in neighbouring_camera_ids
                        for r in i.raw_alarms
                        if isinstance(r, RawAlarms)
                    )
                ]
            log.info(
                "Relevant Alarms",
                relevant_location_alarms=[
                    (r.int_id, r.current_status)
                    for r in relevant_location_alarms
                ],
            )
            location_alarm_strategy = self._get_relevant_location_alarm(
                relevant_location_alarms,
                raw_alarm,
                self._motion_alarm_grouping_time,
                best_match=best_matching_alarm,
                location=location.id,
            )
            loc_alarm_id = None
            if (
                location_alarm_strategy.action
                == _AlarmStrategyAction.CREATE_NEW
            ):
                log.info(
                    "Creating new location alarm",
                    raw_alarm_id=raw_alarm.uuid,
                    location_id=location.id,
                    tenant_id=raw_alarm.tenant_id,
                )
                loc_alarm = self._ctrl.location_alarms.create_new_location_alarm(
                    raw_alarm,
                    location.id,
                    add_to_monitoring_queue=tenantConf.addToMonitoringQueue,
                )
                loc_alarm_id = loc_alarm.int_id
                # Increment metrics for new motion location alarm and raw alarm
                if (
                    MOTION_LOCATION_ALARM_COUNTER
                    and MOTION_RAW_ALARM_LOCATION_COUNTER
                ):
                    try:
                        MOTION_LOCATION_ALARM_COUNTER.labels(
                            tenant=raw_alarm.tenant_id,
                            location_id=str(location.id),
                        ).inc()
                        MOTION_RAW_ALARM_LOCATION_COUNTER.labels(
                            tenant=raw_alarm.tenant_id,
                            location_id=str(location.id),
                            action_type="create",
                        ).inc()
                    except Exception as e:
                        log.warning(
                            "Failed to increment motion alarm metrics",
                            error=str(e),
                        )
            elif (
                location_alarm_strategy.action
                == _AlarmStrategyAction.ADD_TO_EXISTING
            ):
                # TODO: increase TAP here?
                assert location_alarm_strategy.alarm is not None
                log.info(
                    "Adding RawAlarm to existing location alarm",
                    raw_alarm_id=raw_alarm.uuid,
                    location_alarm_id=location_alarm_strategy.alarm.int_id,
                    tenant_id=raw_alarm.tenant_id,
                )
                self._ctrl.location_alarms.add_alarm_event(
                    location_alarm_strategy.alarm,
                    raw_alarm,
                    location_alarm_strategy.mark_pending,
                )
                loc_alarm_id = location_alarm_strategy.alarm.int_id
                # Increment metric for raw alarm added to existing location alarm
                if MOTION_RAW_ALARM_LOCATION_COUNTER:
                    try:
                        MOTION_RAW_ALARM_LOCATION_COUNTER.labels(
                            tenant=raw_alarm.tenant_id,
                            location_id=str(location.id),
                            action_type="add",
                        ).inc()
                    except Exception as e:
                        log.warning(
                            "Failed to increment motion alarm metrics",
                            error=str(e),
                        )
            elif location_alarm_strategy.action == _AlarmStrategyAction.MERGE:
                assert location_alarm_strategy.alarm is not None
                log.info(
                    "Merging RawAlarm to existing location alarm",
                    raw_alarm_id=raw_alarm.uuid,
                    location_alarm_id=location_alarm_strategy.alarm.int_id,
                    tenant_id=raw_alarm.tenant_id,
                )
                self._ctrl.location_alarms.add_alarm_event(
                    location_alarm_strategy.alarm,
                    raw_alarm,
                    location_alarm_strategy.mark_pending,
                )
                loc_alarm_id = location_alarm_strategy.alarm.int_id
                # Increment metric for raw alarm merged to existing location alarm
                if MOTION_RAW_ALARM_LOCATION_COUNTER:
                    try:
                        MOTION_RAW_ALARM_LOCATION_COUNTER.labels(
                            tenant=raw_alarm.tenant_id,
                            location_id=str(location.id),
                            action_type="merge",
                        ).inc()
                    except Exception as e:
                        log.warning(
                            "Failed to increment motion alarm metrics",
                            error=str(e),
                        )

            if loc_alarm_id:
                self._ctrl.alarm.add_comment(
                    raw_alarm.uuid,
                    raw_alarm.tenant_id,
                    f"Added to location alarm {loc_alarm_id}",
                )

    def find_raw_alarm_with_closest_embedding(
        self,
        raw_alarm: RawAlarms,
        relevant_location_alarms: typing.Sequence[LocationAlarms],
        tenantConf: TenantConfig,
        tags: typing.Optional[Tags],
        camera: Cameras,
    ) -> typing.Optional[
        typing.Tuple[
            bool,
            typing.Optional[LocationAlarms],
            typing.Dict[str, typing.List[typing.Tuple[int, str]]],
        ]
    ]:
        """
        This function checks for resolved relevant location alarms
        and matches current raw alarm to them

        Args:
            raw_alarm (RawAlarms): current raw alarm
            relevant_location_alarms (typing.List[LocationAlarms]): list of
                location alarms in relevant time window
            tenantConf (TenantConfig): Tenant Config
            tags (typing.List[TagTypes]): tags for current raw alarm

        Returns:
            best_match: Optional tuple of the best match raw alarm,
            location alarm and float of dist of matched embeddings
        """
        # Don't run matching if disabled and if not a Person
        if not (
            tenantConf.useVectorGrouping
            and tenantConf.checkVectorDb
            and (tags is not None and TagTypes.PERSON in tags)
        ):
            log.info("Not matching because not enabled or not person")
            return None

        # If Resolved Alarms available, try to match with them
        location_alarm_updates = (
            self._ctrl.location_alarms.get_location_alarm_updates(
                [
                    location_alarm.int_id
                    for location_alarm in relevant_location_alarms
                ]
            )
        )
        log.debug(
            "Location Alarm Updates",
            laus=[lau.update_text for lau in location_alarm_updates],
        )
        security_resolved = set(
            int(lau.location_alarm_id)
            for lau in location_alarm_updates
            if isinstance(lau.update_text, str)
            and (
                "authorized" in lau.update_text.lower()
                or "security" in lau.update_text.lower()
            )
        )
        log.info(
            "Resolved Location Alarms", security_resolved=security_resolved
        )
        relevant_location_alarms = [
            location_alarm
            for location_alarm in relevant_location_alarms
            if location_alarm.int_id in security_resolved
        ]
        # Atleast 2 resolved alarms
        if len(relevant_location_alarms) < 2:
            log.info("Not enough resolved alarms")
            return None
        # Get processed alarms from the same camera
        relevant_raw_alarms = {
            relevant_raw_alarm.uuid
            for location_alarm in relevant_location_alarms
            for relevant_raw_alarm in location_alarm.raw_alarms
            if isinstance(relevant_raw_alarm, RawAlarms)
            and raw_alarm.source_entity_id
            == relevant_raw_alarm.source_entity_id
            and relevant_raw_alarm.true_alarm_probability > 85
        }
        log.info(
            "Relevant Raw Alarms",
            raw_alarm=raw_alarm,
            camera_id=raw_alarm.source_entity_id,
            relevant_raw_alarms=relevant_raw_alarms,
        )
        neighbouring_raw_alarms = defaultdict(list)
        if tenantConf.multiCameraVectorGrouping is not None:
            if (
                tenantConf.multiCameraVectorGrouping == "neighbours"
                and isinstance(camera.camera_raw_info, dict)
                and "neighbouring_camera_ids" in camera.camera_raw_info
                and (
                    neighbouring_camera_ids := camera.camera_raw_info.get(
                        "neighbouring_camera_ids"
                    )
                )
                is not None
                and isinstance(neighbouring_camera_ids, list)
                and all(isinstance(n, str) for n in neighbouring_camera_ids)
            ):
                log.debug(
                    "Considering as neighbouring_camera_ids",
                    neighbours=neighbouring_camera_ids,
                )
                neighbouring_camera_ids = set(neighbouring_camera_ids)
                for location_alarm in relevant_location_alarms:
                    for relevant_raw_alarm in location_alarm.raw_alarms:
                        if (
                            isinstance(relevant_raw_alarm, RawAlarms)
                            and relevant_raw_alarm.source_entity_id
                            in neighbouring_camera_ids
                            and relevant_raw_alarm.true_alarm_probability > 85
                        ):
                            neighbouring_raw_alarms[
                                relevant_raw_alarm.source_entity_id
                            ].append(relevant_raw_alarm.uuid)
            elif tenantConf.multiCameraVectorGrouping == "site":
                for location_alarm in relevant_location_alarms:
                    for relevant_raw_alarm in location_alarm.raw_alarms:
                        if (
                            isinstance(relevant_raw_alarm, RawAlarms)
                            and relevant_raw_alarm.true_alarm_probability > 85
                        ):
                            neighbouring_raw_alarms[
                                relevant_raw_alarm.source_entity_id
                            ].append(relevant_raw_alarm.uuid)
            log.info(
                "Neighbouring Alarms",
                neighbouring_raw_alarms=neighbouring_raw_alarms,
                config_value=tenantConf.multiCameraVectorGrouping,
            )

        # Get embeddings of above alarms
        embeddings = self._ctrl.pinecone.get_alarm_embeddings(
            list(relevant_raw_alarms) + [raw_alarm.uuid],
            raw_alarm.source_entity_id,
        )
        if embeddings is None:
            log.info("No embeddings found")
            return None
        for cam_id, alarm_uuids in neighbouring_raw_alarms.items():
            cam_embeddings = self._ctrl.pinecone.get_alarm_embeddings(
                alarm_uuids,
                cam_id,
            )
            if cam_embeddings is not None:
                embeddings.update(cam_embeddings)

        embeddings = {
            uuid: track_embeddings
            for uuid, track_embeddings in embeddings.items()
            if len(track_embeddings.keys()) != 0
            and all(len(v) != 0 for v in track_embeddings.values())
        }

        if raw_alarm.uuid not in embeddings:
            log.info("No embeddings for Current Raw Alarm")
            return None

        if len(embeddings[raw_alarm.uuid]) == 0:
            log.info("No embeddings for Current Raw Alarm")
            return None
        log.debug("All embeddings", embeddings=embeddings)

        current_alarm_embeddings = [
            np.array(track_embeddings)
            for track_embeddings in embeddings[raw_alarm.uuid].values()
        ]
        current_alarm_track_ids = list(embeddings[raw_alarm.uuid].keys())

        same_camera_matches: typing.Dict[
            int, typing.List[typing.Optional[str]]
        ] = {}
        same_camera_match_counts = [0] * len(current_alarm_embeddings)
        neighbouring_camera_matches: typing.Dict[
            int, typing.List[typing.Optional[str]]
        ] = {}
        neighbouring_camera_match_counts = [0] * len(current_alarm_embeddings)

        for location_alarm in relevant_location_alarms:
            relevant_raw_alarms = {
                relevant_raw_alarm.uuid
                for relevant_raw_alarm in location_alarm.raw_alarms
                if isinstance(relevant_raw_alarm, RawAlarms)
                and raw_alarm.source_entity_id
                == relevant_raw_alarm.source_entity_id
                and relevant_raw_alarm.true_alarm_probability > 85
                and relevant_raw_alarm.uuid in embeddings
            }
            neighbouring_relevant_raw_alarms = {
                relevant_raw_alarm.uuid
                for relevant_raw_alarm in location_alarm.raw_alarms
                if isinstance(relevant_raw_alarm, RawAlarms)
                and relevant_raw_alarm.uuid in embeddings
                and relevant_raw_alarm.uuid
                in neighbouring_raw_alarms[relevant_raw_alarm.source_entity_id]
                and raw_alarm.source_entity_id
                != relevant_raw_alarm.source_entity_id
                and relevant_raw_alarm.true_alarm_probability > 85
            }
            log.debug(
                "Checking with alarms",
                location_alarm=location_alarm,
                raw_alarms=[
                    (r.uuid, r.source_entity_id)
                    for r in location_alarm.raw_alarms
                    if isinstance(r, RawAlarms)
                ],
                relevant_raw_alarms=relevant_raw_alarms,
                neighbouring_relevant_raw_alarms=neighbouring_relevant_raw_alarms,
            )

            same_camera_matches[location_alarm.int_id] = match_embeddings(
                embeddings,
                current_alarm_embeddings,
                list(relevant_raw_alarms),
                tenantConf.vectorGroupingDist
                or ml_config.MLSERVICE.ALARM_GROUPER.dist,
            )
            neighbouring_camera_matches[location_alarm.int_id] = (
                match_embeddings(
                    embeddings,
                    current_alarm_embeddings,
                    list(neighbouring_relevant_raw_alarms),
                    tenantConf.multiCameraVectorGroupingDist
                    or ml_config.MLSERVICE.ALARM_GROUPER.dist,
                )
            )

            same_camera_match_counts = [
                curr_count + 1 if match is not None else curr_count
                for curr_count, match in zip(
                    same_camera_match_counts,
                    same_camera_matches[location_alarm.int_id],
                )
            ]
            neighbouring_camera_match_counts = [
                curr_count + 1 if match is not None else curr_count
                for curr_count, match in zip(
                    neighbouring_camera_match_counts,
                    neighbouring_camera_matches[location_alarm.int_id],
                )
            ]

        log.debug(
            "Matches Found",
            same_camera_matches=same_camera_matches,
            neighbouring_camera_matches=neighbouring_camera_matches,
            same_camera_match_counts=same_camera_match_counts,
            neighbouring_camera_match_counts=neighbouring_camera_match_counts,
        )

        # For best_matching_alarm to add this to, choose latest alarm with atleast one matching entity
        all_matches: typing.Dict[str, typing.List[typing.Tuple[int, str]]] = {}
        best_matching_alarm = None

        if all(np.array(same_camera_match_counts) >= 2):
            for idx, track_id in enumerate(current_alarm_track_ids):
                all_matches[track_id] = []
                for location_alarm in relevant_location_alarms:
                    if same_camera_matches[location_alarm.int_id][idx]:
                        all_matches[track_id].append(
                            (
                                int(location_alarm.int_id),
                                str(
                                    same_camera_matches[location_alarm.int_id][
                                        idx
                                    ]
                                ),
                            )
                        )
            for location_alarm in relevant_location_alarms:
                if any(same_camera_matches[location_alarm.int_id]):
                    best_matching_alarm = location_alarm
                    break
            return (True, best_matching_alarm, all_matches)

        total_match_counts = [
            same + neighbour
            for same, neighbour in zip(
                same_camera_match_counts, neighbouring_camera_match_counts
            )
        ]

        if all(np.array(total_match_counts) >= 2):
            for idx, track_id in enumerate(current_alarm_track_ids):
                all_matches[track_id] = []
                for location_alarm in relevant_location_alarms:
                    if same_camera_matches[location_alarm.int_id][idx]:
                        all_matches[track_id].append(
                            (
                                int(location_alarm.int_id),
                                str(
                                    same_camera_matches[location_alarm.int_id][
                                        idx
                                    ]
                                ),
                            )
                        )

                    if neighbouring_camera_matches[location_alarm.int_id][idx]:
                        all_matches[track_id].append(
                            (
                                int(location_alarm.int_id),
                                str(
                                    neighbouring_camera_matches[
                                        location_alarm.int_id
                                    ][idx]
                                ),
                            )
                        )
            for location_alarm in relevant_location_alarms:
                if any(same_camera_matches[location_alarm.int_id]) or any(
                    neighbouring_camera_matches[location_alarm.int_id]
                ):
                    best_matching_alarm = location_alarm
                    break
            return (False, best_matching_alarm, all_matches)

        return None

    def _get_relevant_location_alarm(
        self,
        relevant_location_alarms: typing.Sequence[LocationAlarms],
        raw_alarm: RawAlarms,
        motion_grouping_time: datetime.timedelta,
        best_match: typing.Optional[LocationAlarms] = None,
        location: typing.Optional[Locations] = None,
    ) -> _AlarmStrategy:
        if best_match is not None:
            log.info(
                "Best match found",
                best_match=best_match,
            )
            return _AlarmStrategy(
                _AlarmStrategyAction.ADD_TO_EXISTING, best_match, False
            )

        if config.HAIE.ESCALATION_GROUPING and location:
            escalation_location_alarm = (
                self._ctrl.location_incidents.get_pending_escalations(
                    location_id=location
                )
            )
            if (
                escalation_location_alarm
                and isinstance(escalation_location_alarm, LocationAlarms)
                and (
                    raw_alarm.alarm_timestamp_utc
                    - escalation_location_alarm.alarm_time_utc
                )
                < self._escalation_grouping_time
            ):
                log.info(
                    "Escalation Location Alarm",
                    escalation_location_alarm=escalation_location_alarm,
                )
                return _AlarmStrategy(
                    _AlarmStrategyAction.ADD_TO_EXISTING,
                    escalation_location_alarm,
                    False,
                )
            log.info("Escalated Location Alarm not found")
        if not relevant_location_alarms:
            log.info("No relevant location alarms found for grouping")
            return _AlarmStrategy(_AlarmStrategyAction.CREATE_NEW, None, True)
        alarm_times = sorted(
            [i.alarm_time_utc for i in relevant_location_alarms]
        )
        relevant_location_alarms = sorted(
            relevant_location_alarms, key=lambda x: x.alarm_time_utc
        )
        # Get previous and next location alarm
        idx = (
            bisect.bisect_right(
                alarm_times,
                raw_alarm.alarm_timestamp_utc,
            )
            - 1
        )
        previous_alarm = None
        next_alarm = None
        if idx == -1:
            next_alarm = relevant_location_alarms[0]
        else:
            previous_alarm = relevant_location_alarms[idx]
        if idx != len(relevant_location_alarms) - 1:
            # there is a location alarm after
            next_alarm = relevant_location_alarms[idx + 1]
        # if we are here, then there must be at least one alarm to possibly
        # match to
        assert previous_alarm is not None or next_alarm is not None
        if (
            previous_alarm is None
            # next alarm is always not None if previous alarm is None
            # but needed to make mypy happy
            and next_alarm is not None
            and next_alarm.alarm_time_utc - raw_alarm.alarm_timestamp_utc
            <= motion_grouping_time
        ):
            return _AlarmStrategy(
                _AlarmStrategyAction.ADD_TO_EXISTING, next_alarm, True
            )
        if (
            next_alarm is None
            # previous alarm is always not None if next alarm is None
            # but needed to make mypy happy
            and previous_alarm is not None
            and raw_alarm.alarm_timestamp_utc - previous_alarm.latest_event_utc
            <= motion_grouping_time
        ):
            # Most common expected case for in order processing
            return _AlarmStrategy(
                _AlarmStrategyAction.ADD_TO_EXISTING, previous_alarm, True
            )
        if previous_alarm is None or next_alarm is None:
            # This should never happen - because all relevant location
            # alarms must be within threshold
            log.error(
                "Relevant location alarm is outside threshold",
                location_alarm=previous_alarm or next_alarm,
            )
            return _AlarmStrategy(_AlarmStrategyAction.CREATE_NEW, None, True)
        assert previous_alarm is not None
        assert next_alarm is not None
        # here both alarms are close enough. Just add to the next alarm
        # This is a failure of grouping, but best we can do for out of order
        # processing with delays
        log.warning(
            "Out of order processing, adding to next alarm",
            location_alarm_times=alarm_times,
        )
        return _AlarmStrategy(
            _AlarmStrategyAction.ADD_TO_EXISTING, next_alarm, True
        )

    def _group_alarm_event(
        self, alarm: RawAlarms, update_type: AlarmGroupEventType
    ) -> None:
        """Group non motion alarms. Implementation left for the future"""
