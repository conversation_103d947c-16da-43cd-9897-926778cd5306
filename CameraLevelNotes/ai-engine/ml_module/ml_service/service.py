import datetime
import tempfile
import time
import typing

import opentracing
import structlog
from opentracing import Format
from structlog.contextvars import bind_contextvars, unbind_contextvars

import controller as ctrl
import errors
from common_utils.io_helpers import get_constructed_file_name
from config import backend_config as config
from config import ml_config
from interfaces.alarm import ALARM_TYPE_MOTION, PENDING_STATUS, Alarm
from interfaces.alarm_mapper import AlarmMapper
from interfaces.alarm_state import AlarmState
from interfaces.entities.entity import Entity
from interfaces.entities.entity_types import EntityType
from interfaces.tenant_config import TenantConfig
from ml_module.detection_classifier import (
    get_default_classifer,
    get_llm_classifier,
)
from ml_module.detector import (
    get_default_detector,
    get_ensemble_motion_detector,
    get_people_and_vehicle_motion_detector,
    get_people_motion_detector,
    get_people_thermal_motion_detector,
    get_vehicle_motion_detector,
)
from ml_module.direction_classifier import Debug<PERSON>ata<PERSON>umper, DirectionClassifier
from ml_module.face_recognizer import get_default_face_recognizer
from ml_module.face_recognizer.aws_rekognition import (
    AWSRekognitionFaceRecognizer,
)
from ml_module.face_recognizer.base_dlib import (
    DlibFaceRecognizer,
)
from ml_module.ml_interfaces.service_response import MLServiceResponse
from ml_module.ml_service.utils import (
    add_entities_to_pinecone,
    upload_facial_recognition_alarm_video,
)
from ml_module.movement_pattern_analyzer import MovementPatternAnalyzer
from ml_module.tracker import get_default_tracker
from ml_module.tracklet_merger import (
    get_default_tracklet_merger,
    get_default_vehicle_tracklet_merger,
)
from ml_module.utils import download_alarm_video
from ml_module.video_time_analyzer.analyzer import VideoTimeAnalyzer
from models_rds.rds_client import RDSClient

log = structlog.get_logger("hakimo.ml_module", module="ML Service")


class MLService:
    def __init__(
        self,
        db: RDSClient,
        controller: ctrl.ControllerMap,
        direction_classifiers: typing.Sequence[DirectionClassifier],
        motion_direction_classifiers: typing.Sequence[DirectionClassifier],
        thermal_motion_direction_classifiers: typing.Sequence[
            DirectionClassifier
        ],
        combined_motion_direction_classifier: typing.Sequence[
            DirectionClassifier
        ],
        face_recognizer: typing.Optional[
            typing.Union[AWSRekognitionFaceRecognizer, DlibFaceRecognizer]
        ] = None,
    ):
        self._rds_client = db
        self._controller = controller
        self._direction_classifiers = {
            i.target_entity: i for i in direction_classifiers
        }
        self._motion_direction_classifiers = {
            i.target_entity: i for i in motion_direction_classifiers
        }
        self._combined_motion_direction_classifier = {
            i.target_entity: i for i in combined_motion_direction_classifier
        }
        self._thermal_motion_direction_classifiers = {
            i.target_entity: i for i in thermal_motion_direction_classifiers
        }
        self._alarm_mapper = AlarmMapper(controller)
        self._video_analyzer = VideoTimeAnalyzer(
            self._alarm_mapper, controller
        )
        self._movement_pattern_analyzer = MovementPatternAnalyzer(
            self._alarm_mapper, self._video_analyzer
        )
        self.face_recognizer = face_recognizer
        self._lock = False
        # self._segmentor = Segmentor.create(segmentor_type="background_sub")

    def get_entities(
        self,
        alarm_uuid: str,
        tenant_id: str,
        entity_type: typing.Union[
            EntityType, typing.Tuple[EntityType, EntityType]
        ],
    ) -> MLServiceResponse:
        if self._lock:
            log.info(
                "ML service Locked, Client should retry",
                alarm_id=alarm_uuid,
            )
            raise errors.RetryableError(
                "ML service Locked, Client should retry"
            )
        alarm = None
        try:
            self._lock = True
            alarm = self._rds_client.get_alarm_object(alarm_uuid, tenant_id)
            if alarm is None:
                raise ValueError("Invalid alarm ID")
            if alarm.trace_data is not None:
                parent_span = opentracing.tracer.extract(
                    Format.TEXT_MAP, alarm.trace_data
                )
            else:
                parent_span = None
            with opentracing.tracer.start_active_span(
                "get-entities",
                child_of=parent_span,
                tags={
                    "alarm_id": alarm.alarm_uuid,
                    "alarm_type": alarm.alarm_type,
                },
            ):
                bind_contextvars(
                    video_path=alarm.video_path,
                    alarm_type=alarm.alarm_type,
                )
                return self.run_processing(alarm, entity_type)
        finally:
            unbind_contextvars("video_path", "alarm_type")
            self._lock = False

    def run_face_recognition(
        self,
        alarm: Alarm,
        entities: typing.Sequence[Entity],
        video_path: str,
        t_config: TenantConfig,
        tmp_dir: str,
    ) -> bool:
        matched_faces_flag = False
        if (
            t_config
            and isinstance(t_config, TenantConfig)
            and t_config.useFaceRecognition
            and self.face_recognizer
        ):
            start_time = time.time()
            matched_faces = self.face_recognizer.recognize_faces(
                entities,
                biggest_n_boxes=t_config.faceRecognitionNBiggestBoxes or 5,
            )
            if matched_faces:
                matched_faces_flag = True
            time_taken = time.time() - start_time
            self._controller.alarm.add_comment(
                alarm.alarm_uuid,
                alarm.tenant_id,
                f"Time taken to recognize faces: {time_taken}",
            )
            log.debug(
                "Time Taken to recognize faces",
                time_taken=time_taken,
                matched_faces=matched_faces,
            )

            start_time = time.time()
            s3_urls = []
            seen_profile_ids = set()
            for (
                matched_person_id,
                profile_id,
                matched_frame,
                matched_box,
                matched_score,
            ) in matched_faces:
                if profile_id in seen_profile_ids:
                    continue
                seen_profile_ids.add(profile_id)

                person_profile = (
                    self._controller.person_profile.get_person_profile_by_id(
                        profile_id, alarm.tenant_id
                    )
                )
                if person_profile:
                    image_path = download_alarm_video(
                        person_profile.primary_image_url, tmp_dir
                    )
                    s3_urls.append(
                        (
                            f"{person_profile.first_name} {person_profile.last_name}",
                            matched_score,
                            upload_facial_recognition_alarm_video(
                                video_path,
                                image_path,
                                alarm.tenant_id,
                                matched_person_id,
                                matched_box,
                                matched_frame=matched_frame,
                                image_scale=t_config.faceRecognitionProfileSize
                                or 0.5,
                            ),
                        )
                    )

            time_taken = time.time() - start_time
            self._controller.alarm.add_comment(
                alarm.alarm_uuid,
                alarm.tenant_id,
                f"Time taken to create and upload Face Recognition Video: {time_taken}",
            )
            log.debug(
                "Time taken to create and upload Face Recognition Video",
                time_taken=time_taken,
                s3_urls=s3_urls,
            )
            is_dlib = isinstance(self.face_recognizer, DlibFaceRecognizer)
            is_aws = isinstance(
                self.face_recognizer, AWSRekognitionFaceRecognizer
            )
            if s3_urls:
                for name, match_score, s3_url in s3_urls:
                    new_alarm = Alarm(
                        **{
                            "alarm_time": alarm.created_at_utc,
                            "alarm_type": "Face Detected",
                            "source_entity_id": alarm.source_entity_id,
                            "source_entity_type": alarm.source_entity_type,
                            "source_system": "Hakimo AI",
                            "source_id": f"{alarm.source_id}_{int((datetime.datetime.utcnow()).timestamp() * 1000)}",
                            "door_uuid": alarm.door_uuid,
                            "tenant_id": alarm.tenant_id,
                            "display": True,
                            "state": AlarmState.PROCESSED,
                        }
                    )
                    new_raw_alarm_uuid = (
                        self._rds_client.write_update_raw_alarms(
                            new_alarm, PENDING_STATUS
                        )
                    )
                    if new_raw_alarm_uuid:
                        new_raw_alarm = self._rds_client.get_alarm_object(
                            new_raw_alarm_uuid, alarm.tenant_id
                        )
                        if new_raw_alarm:
                            self._rds_client.update_raw_alarms_with_video_metadata(
                                new_raw_alarm_uuid,
                                alarm.video_start_time_utc,
                                alarm.video_end_time_utc,
                                video_path=s3_url,
                            )
                            self._controller.alarm.update_alarm_processed_times(
                                new_alarm,
                                alarm.processing_start_time_utc,
                                alarm.processing_end_time_utc,
                            )
                            self._controller.alarm_media.add_media(
                                new_raw_alarm_uuid,
                                s3_url,
                                media_type="video",
                            )
                            self._controller.alarm.update_tap(
                                new_raw_alarm_uuid, alarm.tenant_id, 90
                            )
                            if is_aws:
                                self._controller.alarm.add_comment(
                                    new_raw_alarm_uuid,
                                    alarm.tenant_id,
                                    f"Restricted individual '{name}' identified with a similarity score of {match_score:.2f}%. Alert raised for further action.",
                                )
                            elif is_dlib:
                                self._controller.alarm.add_comment(
                                    new_raw_alarm_uuid,
                                    alarm.tenant_id,
                                    f"Restricted individual '{name}' identified with a distance metric of {match_score:.2f}. Alert raised for further action.",
                                )
                            else:
                                self._controller.alarm.add_comment(
                                    new_raw_alarm_uuid,
                                    alarm.tenant_id,
                                    f"Restricted individual '{name}' identified with a match metric of {match_score}. Alert raised for further action.",
                                )

        return matched_faces_flag

    def run_processing(
        self,
        alarm: Alarm,
        entity_type: typing.Union[
            EntityType, typing.Tuple[EntityType, EntityType]
        ],
    ) -> MLServiceResponse:
        motion = None
        matched_faces_flag = False
        with tempfile.TemporaryDirectory() as tmp_dir:
            video_path = download_alarm_video(alarm.video_path, tmp_dir)
            interfaces_prefix = get_constructed_file_name(
                alarm.alarm_time, alarm.tenant_id, ""
            )
            t_config = self._controller.tenant.get_config(
                tenant_id=alarm.tenant_id
            )
            if alarm.alarm_type == ALARM_TYPE_MOTION:
                # Motion processing
                if alarm.is_thermal:
                    dc = self._thermal_motion_direction_classifiers[
                        entity_type
                    ]
                    assert (
                        len(self._thermal_motion_direction_classifiers) > 0
                    ), "need thermal motion direction classifiers for thermal alarm"
                else:
                    if (
                        t_config
                        and isinstance(t_config, TenantConfig)
                        and t_config.enableCombinedTeacherTrainedModel
                        and self._combined_motion_direction_classifier
                        is not None
                        and entity_type
                        in self._combined_motion_direction_classifier
                    ):
                        dc = self._combined_motion_direction_classifier[
                            entity_type
                        ]
                        log.info(
                            "Combined teacher trained model direction classifier is being used."
                        )
                    else:
                        dc = self._motion_direction_classifiers[entity_type]
                        log.info("Motion direction classifier is being used.")

                entities, intermediates = dc.track_video(
                    video_path,
                    alarm.scene_info,
                    interfaces_prefix=interfaces_prefix,
                    processing_fps=config.HAIE.MOTION_PROCESSING_FPS,
                    moving_only=True,
                    tenant_id=alarm.tenant_id,
                    camera_id=alarm.source_entity_id,
                    tenant_config=t_config
                    if t_config and isinstance(t_config, TenantConfig)
                    else None,
                )

                if (
                    t_config
                    and isinstance(t_config, TenantConfig)
                    and t_config.useFaceRecognition
                    and self.face_recognizer
                ):
                    matched_faces_flag = self.run_face_recognition(
                        alarm, entities, video_path, t_config, tmp_dir
                    )

                if (
                    t_config
                    and isinstance(t_config, TenantConfig)
                    and t_config.checkVectorDb
                ):
                    add_entities_to_pinecone(
                        alarm,
                        entities,
                        self._controller,
                        t_config.checkVectorDb,
                    )
                dc.dump_debug(
                    video_path,
                    entities,
                    alarm.scene_info,
                    alarm.alarm_time,
                    alarm.tenant_id,
                    intermediates,
                )
                return MLServiceResponse(
                    entities=entities,
                    motion=motion,
                    processed_frames=intermediates.get("frame_nums"),
                    deadzone_detections=intermediates.get(
                        "deadzone_detections", 0
                    ),
                    matched_faces=matched_faces_flag,
                )
            entities, intermediates = self._direction_classifiers[
                entity_type
            ].track_video(
                video_path,
                alarm.scene_info,
                interfaces_prefix=interfaces_prefix,
                tenant_config=t_config
                if t_config and isinstance(t_config, TenantConfig)
                else None,
                tenant_id=alarm.tenant_id,
                camera_id=alarm.source_entity_id,
            )
            entities = (
                self._movement_pattern_analyzer.assign_movement_patterns(
                    entities,
                    video_path,
                    alarm,
                )
            )

            if (
                t_config
                and isinstance(t_config, TenantConfig)
                and t_config.useFaceRecognition
                and self.face_recognizer
            ):
                matched_faces_flag = self.run_face_recognition(
                    alarm, entities, video_path, t_config, tmp_dir
                )

            self._direction_classifiers[entity_type].dump_debug(
                video_path,
                entities,
                alarm.scene_info,
                alarm.alarm_time,
                alarm.tenant_id,
                intermediates,
            )
            # if len(entities) == 0:
            #     # if there are no entities found, check for motion, unless
            #     # it's a motion alert (in which case there's no point doing
            #     # this again)
            #     log.debug("No entities found, running motion detector")
            #     self._segmentor.reset_state()
            #     (  # pylint: disable=unbalanced-tuple-unpacking
            #         _,
            #         frame_segments,
            #     ) = extract_highlights(
            #         vid_cap_or_path=video_path,
            #         segmentor=self._segmentor,
            #     )
            #     # motion exists if there is atleast one highlight
            #     motion = len(frame_segments) > 0
            return MLServiceResponse(
                entities=entities,
                motion=motion,
                processed_frames=intermediates.get("frame_nums"),
                deadzone_detections=intermediates.get(
                    "deadzone_detections", 0
                ),
                matched_faces=matched_faces_flag,
            )


def get_ml_service() -> MLService:
    db = RDSClient()
    cm = ctrl.ControllerMap(db.db_adapter)
    detector = get_default_detector()
    ensemble_detector = get_ensemble_motion_detector()
    people_motion_detector = get_people_motion_detector()
    people_thermal_motion_detector = get_people_thermal_motion_detector()
    vehicle_motion_detector = get_vehicle_motion_detector()
    person_tracker = vehicle_tracker = get_default_tracker("TRACKER")
    motion_person_tracker = get_default_tracker("MOTION_PEOPLE_TRACKER")
    motion_vehicle_tracker = get_default_tracker("MOTION_VEHICLE_TRACKER")
    detection_classifier = get_default_classifer(cm)
    llm_detection_classifier = get_llm_classifier()
    people_vehicle_motion_detector = get_people_and_vehicle_motion_detector()
    person_dc = DirectionClassifier(
        detector,
        {EntityType.PERSON: person_tracker},
        get_default_tracklet_merger(),
        None,
        target_entity=EntityType.PERSON,
        debug_data_dumper=DebugDataDumper(),
        min_entity_duration=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_ENTITY_DURATION.people.default,
        min_variation=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_VARIATION.people.default,
        max_min_iou=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MIN_IOU.people.default,
        max_movement=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MOVEMENT.people.default,
        max_conf_short_track=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_CONF_SHORT_TRACK.people.default,
    )
    vehicle_dc = DirectionClassifier(
        detector,
        {EntityType.VEHICLE: vehicle_tracker},
        get_default_vehicle_tracklet_merger(),
        None,
        target_entity=EntityType.VEHICLE,
        debug_data_dumper=DebugDataDumper(),
        min_entity_duration=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_ENTITY_DURATION.vehicles.default,
        min_variation=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_VARIATION.vehicles.default,
        max_min_iou=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MIN_IOU.vehicles.default,
        max_movement=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MOVEMENT.vehicles.default,
        max_conf_short_track=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_CONF_SHORT_TRACK.vehicles.default,
    )
    motion_person_dc = DirectionClassifier(
        people_motion_detector,
        {EntityType.PERSON: motion_person_tracker},
        None,
        detection_classifier=detection_classifier,
        target_entity=EntityType.PERSON,
        debug_data_dumper=DebugDataDumper(),
        min_entity_duration=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_ENTITY_DURATION.people.motion,
        min_variation=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_VARIATION.people.motion,
        max_min_iou=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MIN_IOU.people.motion,
        max_movement=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MOVEMENT.people.motion,
        max_conf_short_track=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_CONF_SHORT_TRACK.people.motion,
        llm_detection_classifier=llm_detection_classifier,
    )
    motion_thermal_person_dc = DirectionClassifier(
        people_thermal_motion_detector,
        {EntityType.PERSON: motion_person_tracker},
        None,
        detection_classifier=None,
        target_entity=EntityType.PERSON,
        debug_data_dumper=DebugDataDumper(),
        min_entity_duration=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_ENTITY_DURATION.people.motion,
        min_variation=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_VARIATION.people.motion,
        max_min_iou=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MIN_IOU.people.motion,
        max_movement=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MOVEMENT.people.motion,
        max_conf_short_track=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_CONF_SHORT_TRACK.people.thermal_motion,
    )
    motion_vehicle_dc = DirectionClassifier(
        vehicle_motion_detector,
        {EntityType.VEHICLE: motion_vehicle_tracker},
        None,
        None,
        target_entity=EntityType.VEHICLE,
        debug_data_dumper=DebugDataDumper(),
        min_entity_duration=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_ENTITY_DURATION.vehicles.motion,
        min_variation=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_VARIATION.vehicles.motion,
        max_min_iou=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MIN_IOU.vehicles.motion,
        max_movement=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MOVEMENT.vehicles.motion,
        max_conf_short_track=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_CONF_SHORT_TRACK.vehicles.motion,
    )
    motion_ensemble_dc = DirectionClassifier(
        ensemble_detector,
        {
            EntityType.PERSON: motion_person_tracker,
            EntityType.VEHICLE: motion_vehicle_tracker,
        },
        None,
        detection_classifier=detection_classifier,
        target_entity=(EntityType.PERSON, EntityType.VEHICLE),
        debug_data_dumper=DebugDataDumper(),
        min_entity_duration=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_ENTITY_DURATION.people.motion,
        min_variation=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_VARIATION.people.motion,
        max_min_iou=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MIN_IOU.people.motion,
        max_movement=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MOVEMENT.people.motion,
        max_conf_short_track=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_CONF_SHORT_TRACK.people.motion,
        llm_detection_classifier=llm_detection_classifier,
    )
    motion_person_vehicle_dc = DirectionClassifier(
        people_vehicle_motion_detector,
        {
            EntityType.PERSON: motion_person_tracker,
            EntityType.VEHICLE: motion_vehicle_tracker,
        },
        None,
        detection_classifier=detection_classifier,
        target_entity=(EntityType.PERSON, EntityType.VEHICLE),
        debug_data_dumper=DebugDataDumper(),
        min_entity_duration=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_ENTITY_DURATION.people.motion,
        min_variation=ml_config.MLSERVICE.FILTERING_CRITERIA.MIN_VARIATION.people.motion,
        max_min_iou=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MIN_IOU.people.motion,
        max_movement=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_MOVEMENT.people.motion,
        max_conf_short_track=ml_config.MLSERVICE.FILTERING_CRITERIA.MAX_CONF_SHORT_TRACK.people.motion,
        llm_detection_classifier=llm_detection_classifier,
    )
    direction_classifiers = [person_dc, vehicle_dc]
    motion_direction_classifiers = [
        motion_person_dc,
        motion_vehicle_dc,
        motion_ensemble_dc,
    ]
    combined_motion_direction_classifier = [motion_person_vehicle_dc]
    ml_service = MLService(
        db,
        cm,
        direction_classifiers,
        motion_direction_classifiers=motion_direction_classifiers,
        thermal_motion_direction_classifiers=[motion_thermal_person_dc],
        combined_motion_direction_classifier=combined_motion_direction_classifier,
        face_recognizer=get_default_face_recognizer(),
    )
    return ml_service
