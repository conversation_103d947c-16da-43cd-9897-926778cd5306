import datetime
import os
import typing

import numpy as np
import structlog

from common_utils.ffmpeg_helpers import (
    combine_image_and_video,
    draw_box_on_video,
)
from common_utils.s3_utils import upload_video_to_s3_get_link
from config import backend_config as config
from controller import ControllerMap
from interfaces.alarm import Alarm
from interfaces.entities.entity import Entity
from interfaces.entities.entity_types import PersonSubClassType
from interfaces.entities.person import Person

log = structlog.get_logger("hakimo.ml_module", module="ML Service Utils")


def upload_facial_recognition_alarm_video(
    video_path: str,
    image_path: str,
    tenant_id: str,
    profile_id: str,
    matched_box: typing.Optional[typing.List[float]],
    matched_frame: int = 0,
    image_scale: float = 0.5,
) -> str:
    # Draw bounding box on original video if matched_box is provided
    if matched_box:
        _, boxed_video_path = draw_box_on_video(
            video_path,
            matched_box,
            matched_frame=matched_frame,
        )
        if not boxed_video_path:
            boxed_video_path = video_path
    else:
        boxed_video_path = video_path
    # Combine overlay image with the boxed video
    _, combined_video_path = combine_image_and_video(
        boxed_video_path,
        image_path,
        image_scale=image_scale,
    )
    if not combined_video_path:
        combined_video_path = boxed_video_path

    s3_bucket = config.gateway()["s3Bucket"]
    s3_path = os.path.join(
        "facial_recognition",
        tenant_id,
        datetime.datetime.now().strftime("%Y-%m-%d"),
        f"{profile_id}_{os.path.basename(combined_video_path)}",
    )
    return upload_video_to_s3_get_link(
        combined_video_path, s3_bucket, s3_path, config.HAIE.AWS_S3_REGION
    )


def add_entities_to_pinecone(
    alarm: Alarm,
    entities: typing.Sequence[Entity],
    controller: ControllerMap,
    checkVectorDb: bool = False,
):
    if checkVectorDb and alarm.source_entity_id is not None:
        embeddings = []
        boxes = []
        frames = []
        track_ids = []
        for entity in entities:
            if (
                isinstance(entity, Person)
                and entity.track is not None
                and entity.track.boxes is not None
                and entity.person_type
                not in [
                    PersonSubClassType.REID_FALSE_POSITIVE,
                    PersonSubClassType.OPTICAL_FLOW_FALSE_POSITIVE,
                    PersonSubClassType.NON_MOTION,
                    PersonSubClassType.FALSE_POSITIVE,
                ]
                and all(box.feature is not None for box in entity.track.boxes)
            ):
                embeddings.extend(
                    [
                        np.array(box.feature).tolist()
                        for box in entity.track.boxes
                    ]
                )
                boxes.extend(entity.track.boxes)
                frames.extend(entity.track.frames)
                track_ids.extend(
                    [str(entity.track._track_id)] * len(entity.track.frames)
                )
        log.debug("adding entities", n_entities=len(entities))
        labels = [PersonSubClassType.BASE.value] * len(embeddings)
        success = controller.pinecone.add_alarm_embeddings(
            alarm_uuid=alarm.alarm_uuid,
            tenant_id=alarm.tenant_id,
            camera_id=alarm.source_entity_id,
            embeddings=embeddings,
            labels=labels,
            boxes=boxes,
            frames=frames,
            track_ids=track_ids,
        )
        log.info(
            "Add entities to pinecone",
            n_entities=len(entities),
            labels=labels,
            boxes=boxes,
            success=success,
        )
