from typing import Optional, Union

from pinecone import Index
from pinecone.grpc import PineconeGRPC

from common_utils.io_helpers import read_file
from config import backend_config as config

from .aws_rekognition import AWSRekognitionFaceRecognizer
from .base_dlib import DlibFaceRecognizer


def get_default_pinecone_face_index() -> Optional[Index]:
    if (
        config.HAIE.PINECONE_FACE_RECOG_ENABLED
        and config.HAIE.PINECONE_FACE_RECOG_API_KEY is not None
    ):
        client = PineconeGRPC(api_key=config.HAIE.PINECONE_FACE_RECOG_API_KEY)
        index = client.Index(config.HAIE.PINECONE_FACE_RECOG_INDEX)
        return index
    return None


def get_default_face_recognizer() -> (
    Optional[Union[AWSRekognitionFaceRecognizer, DlibFaceRecognizer]]
):
    if config.HAIE.FACE_RECOGNITION.get("use_aws_rekognition", False):
        access_key = read_file(config.HAIE.AWS_ACCESS_KEY_ID, missing="")
        secret_key = read_file(config.HAIE.AWS_SECRET_KEY, missing="")
        collection_id = config.HAIE.FACE_RECOGNITION.get(
            "rekognition_collection", ""
        )
        if access_key and secret_key and collection_id:
            return AWSRekognitionFaceRecognizer(
                access_key, secret_key, collection_id
            )

    pinecone_index = get_default_pinecone_face_index()
    if pinecone_index:
        return DlibFaceRecognizer(pinecone_index)

    return None
