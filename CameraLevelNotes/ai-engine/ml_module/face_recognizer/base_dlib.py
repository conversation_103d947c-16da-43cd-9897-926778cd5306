import time
import typing

import face_recognition
import structlog
from pinecone import Index

from interfaces.entities.entity import Entity
from interfaces.entities.person import Person

log = structlog.get_logger("hakimo", module="Dlib Face Recognizer")


class DlibFaceRecognizer:
    def __init__(self, pinecone_index: Index):
        self.pinecone_index = pinecone_index

    def recognize_faces(
        self,
        entities: typing.Sequence[Entity],
        distance_score: float = 0.4,
        biggest_n_boxes: int = 5,
    ) -> typing.List[
        typing.Tuple[str, str, int, typing.Optional[typing.List[float]], float]
    ]:
        start_time = time.time()
        matched_entities = []
        for person in entities:
            if not isinstance(person, Person):
                continue
            if not person.track:
                log.debug(
                    "Skipping person with no tracks",
                    person_id=person.person_id,
                )
                continue

            areas = []

            if person.track.patches:
                areas = [
                    (patch, frame, patch.shape[0] * patch.shape[1])
                    for patch, frame in zip(
                        person.track.patches, person.track.patch_frames
                    )
                    if patch is not None
                ]
            else:
                areas = [
                    (box.patch, frame, box.patch.shape[0] * box.patch.shape[1])
                    for box, frame in zip(
                        person.track.boxes, person.track.frames
                    )
                    if box.patch is not None
                ]

            if areas:
                biggest_n_patches = list(
                    sorted(areas, key=lambda x: x[2], reverse=True)
                )[:biggest_n_boxes]
                for patch, frame, _ in biggest_n_patches:
                    face_location = face_recognition.face_locations(
                        patch, model="cnn"
                    )
                    if not face_location:
                        continue
                    face_embeddings = face_recognition.face_encodings(
                        patch, face_location
                    )
                    if not face_embeddings:
                        continue
                    face_embedding = face_embeddings[0].tolist()

                    pinecone_result = self.pinecone_index.query(
                        vector=face_embedding,
                        top_k=10,
                        namespace="test_namespace",
                        include_metadata=True,
                    )
                    if (
                        pinecone_result
                        and hasattr(pinecone_result, "matches")
                        and pinecone_result.matches
                    ):
                        best_match = (pinecone_result.matches)[0]

                        matched_profile_id = str(
                            best_match.metadata["profile_id"]
                        )
                        matched_filename = str(best_match.metadata["filename"])
                        distance = best_match.score
                        log.debug(
                            "Matched_filename with similarity:",
                            matched_filename=matched_filename,
                            distance=distance,
                        )

                        if distance <= distance_score:
                            box = None
                            try:
                                det = person.track.boxes[
                                    person.track.frames.index(frame)
                                ]
                                box = [float(i) for i in det._box]
                            except ValueError:
                                log.debug()
                                box = person.get_box_around_track()
                            log.debug(
                                f"Match found: {matched_filename}: {matched_profile_id} with distance: {distance} at frame {frame} and box {box}"
                            )
                            matched_entities.append(
                                (
                                    person.person_id,
                                    matched_profile_id,
                                    frame,
                                    box,
                                    distance,
                                )
                            )
                            break
            else:
                log.debug(
                    "No usable patches found", person_id=person.person_id
                )
                continue
        log.debug(
            "Finished Recognizing Faces", time_taken=time.time() - start_time
        )
        return matched_entities
