"""Implementation for VideoTimeAnalyzer class and related functions"""

import datetime
import enum
import re
import typing

import pytz
import structlog

import controller as ctrl
from config import backend_config as config
from interfaces.alarm import ALARM_TYPE_MOTION, Alarm
from interfaces.alarm_mapper import AlarmMapper
from ml_module.motion_detector.constants import (
    MOTION_VIDEO_SEGMENT_DURATION_SECONDS,
    MOTION_VIDEO_SEGMENT_DURATION_SECONDS_v2,
)
from ml_module.utils import get_dho_threshold_incl_heuristics

log = structlog.get_logger("hakimo", module="Video Analyzer")


class VideoAnalyzerResult(enum.Enum):
    """Class that describes different reasons behind
    enough video by video analyzer.

    ENOUGH : enough video has been received to resolve.
    REQUEST_TIME_LIMIT : more video has been requested than the
        maximum allowed, the limit does not provide enough
        video.
    NOT_ENOUGH : not enough video received but can request more.
    VIDEO_UNAVAILABLE : Video not available for this alarm
    MORE_VIDEO_UNAVAILABLE : Excess video not available for this alarm
    VIDEO_NOT_REQUESTED : Video not requested for this alarm
    """

    ENOUGH = 0
    REQUEST_TIME_LIMIT = 1
    NOT_ENOUGH = 2
    VIDEO_UNAVAILABLE = 3
    MORE_VIDEO_UNAVAILABLE = 4
    VIDEO_NOT_REQUESTED = 5


class VideoTimeAnalyzer:
    """Class that has various utilities to return the context window
    (time around the alarm needed to resolve the alarm)
    Optionally will analyze the video to return context window based
    on the scene
    """

    def __init__(
        self, alarm_mapper: AlarmMapper, controller: ctrl.ControllerMap
    ):
        self._alarm_mapper = alarm_mapper
        self._controller = controller
        self._max_request_time_mins = 5

    def check_request_video_config(self, alarm: Alarm) -> bool:
        """
        Checks and returns if the alarm_processing_config for the alarm has the request_video flag set or not.
        DOES NOT ask for the video if request_video flag is set to False in alarm_processing_config.
        Otherwise continues the default flow
        """
        alarm_type = self._alarm_mapper.get_internal_alarm_type(alarm)
        alarm_processing_config = self._controller.alarm.get_processing_config(
            alarm
        )
        # Default to True if no alarm processing config is defined
        if alarm_processing_config is None:
            return True
        alarm_type_list = alarm_processing_config.alarm_types
        # Apply or disable for all based on whatever is configured
        if alarm_type_list is None:
            return alarm_processing_config.request_video
        # Return the configured flag if alarm_type is there in alarm_types list. Otherwise, return True
        if alarm_type_list is not None and alarm_type in alarm_type_list:
            return alarm_processing_config.request_video
        return True

    def require_alarm_video(self, alarm: Alarm):
        """Decides whether video is required to process the given alarm.

        Args:
            alarm ([Alarm]): Alarm object

        Returns:
            [bool]: True if video is required, else False
        """
        # This method is only for doors, not camera alarms
        if alarm.door_uuid is None:
            return False

        # Check if alarm processing config has request_video flag enabled or not
        if not self.check_request_video_config(alarm):
            log.debug(
                "Not asking for video because of alarm processing config",
                alarm_id=alarm.alarm_uuid,
            )
            return False

        if self._alarm_mapper.get_internal_alarm_type(alarm) in [
            "Access Granted",
            "Door Forced Open",
        ]:
            return True
        alarm_type = self._alarm_mapper.get_internal_alarm_type(alarm).lower()
        # Ask for video for these events only if the previous event at that
        # door was more than 5 minutes ago
        if any(
            [
                "canceled" in alarm_type,
                "restored" in alarm_type,
                alarm_type == "door close",
                alarm_type == "secured",
                alarm_type == "door closed again",
            ]
        ):
            last_alarms = self._controller.alarm.get_last_n_alarms_before(
                alarm.door_uuid, alarm.alarm_time, tenant_id=alarm.tenant_id
            )
            if not last_alarms:
                log.error("No previous alarm for canceled or restored event")
                return True
            last_alarm = last_alarms[0]

            assert alarm.alarm_time.tzinfo in [
                pytz.utc,
                None,
            ], f"Unexpected alarm timezone {alarm.alarm_time.tzinfo}"
            assert (
                last_alarm.alarm_timestamp_utc.tzinfo
                in [
                    pytz.utc,
                    None,
                ]
            ), f"Unexpected alarm timezone {last_alarm.alarm_timestamp_utc.tzinfo}"
            if alarm.alarm_time.tzinfo is None:
                alarm.alarm_time = alarm.alarm_time.replace(tzinfo=pytz.utc)
            last_alarm.alarm_timestamp_utc = (
                last_alarm.alarm_timestamp_utc.replace(
                    tzinfo=alarm.alarm_time.tzinfo
                )
            )

            if (
                alarm.alarm_time - last_alarm.alarm_timestamp_utc
                > datetime.timedelta(minutes=5)
            ):
                return True
            return False
        if any(
            [
                "alarm triggered" in alarm_type,
                "custom event" in alarm_type,
                # Temporarily not ask for video
                # to alleviate processing trouble
                alarm_type == "door open",
                "request to exit" in alarm_type,
                # Ignore video for software events like dfo masked
                # and unmasked
                re.match(r".* masked", alarm_type),
                re.match(r".* unmasked", alarm_type),
                # Other events we don't care about
                re.match(r"relay contact (de)?activated", alarm_type),
                re.match(
                    r"reader mode (card only|unlocked|card and pin|pin or card|locked)",
                    alarm_type,
                ),
                alarm_type == "line error active",
            ]
        ):
            return False
        # Temporary video for all alarms
        return True

    def get_door_opening_closing_times(self, alarm: Alarm):
        """
        Obtain the time around the alarm required to resolve an alarm
        (Context time window)
        """
        dho_threshold = None
        if alarm.door_uuid is not None:
            dho_threshold = get_dho_threshold_incl_heuristics(
                self._controller, alarm.door_uuid, alarm.tenant_id
            )

        if (
            self._alarm_mapper.get_internal_alarm_type(alarm)
            == "Door Held Open"
        ):
            # For DHO we require alarm - dho_threshold until DHO Cancelled
            # Start by asking for some amount of time, pipeline will rerequest
            # if we need more video
            door_opening_time = alarm.alarm_time - datetime.timedelta(
                seconds=dho_threshold + 10
                if dho_threshold
                else 3 * config.HAIE.TIME_BEFORE_ALARM_TIMESTAMP
            )
            door_closing_time = alarm.alarm_time + datetime.timedelta(
                seconds=3 * config.HAIE.TIME_AFTER_ALARM_TIMESTAMP
            )
        else:
            # For all other events request a blanket period of video
            # and possibly request more later
            door_opening_time = alarm.alarm_time - datetime.timedelta(
                seconds=config.HAIE.TIME_BEFORE_ALARM_TIMESTAMP
            )
            door_closing_time = alarm.alarm_time + datetime.timedelta(
                seconds=config.HAIE.TIME_AFTER_ALARM_TIMESTAMP
            )

        return door_opening_time, door_closing_time

    def verify_sufficient_video(
        self,
        alarm: Alarm,
        video_details: dict,
    ) -> typing.Tuple[
        VideoAnalyzerResult,
        typing.Tuple[
            typing.Optional[datetime.datetime],
            typing.Optional[datetime.datetime],
        ],
    ]:
        """Given the tracking output for a given alarm, and the alarm,
        verify if the amount of video is enough to resolve the alarm.
        For example, if a DFO cancelled has not come in yet, then we
        need to request for more video.

        Args:
            alarm (Alarm): alarm object
            video_details (dict):

        Returns:
            [Tuple]: A boolean (indicating whether enough video is present),
            and a tuple of the needed video start and end time to resolve the alarm
        """
        # Check video status
        if not video_details["video_available"]:
            if video_details["local_video_path"] is None:
                video_details["video_check_result"] = (
                    VideoAnalyzerResult.VIDEO_UNAVAILABLE
                )
                return VideoAnalyzerResult.VIDEO_UNAVAILABLE, (
                    alarm.processing_start_time_utc,
                    alarm.processing_end_time_utc,
                )
            video_details["video_check_result"] = (
                VideoAnalyzerResult.MORE_VIDEO_UNAVAILABLE
            )
            return VideoAnalyzerResult.MORE_VIDEO_UNAVAILABLE, (
                alarm.processing_start_time_utc,
                alarm.processing_end_time_utc,
            )
        if video_details["local_video_path"] is None:
            video_details["video_check_result"] = (
                VideoAnalyzerResult.VIDEO_NOT_REQUESTED
            )
            return VideoAnalyzerResult.VIDEO_NOT_REQUESTED, (
                alarm.processing_start_time_utc,
                alarm.processing_end_time_utc,
            )
        if (
            alarm.processing_start_time_utc is None
            or alarm.processing_end_time_utc is None
        ):
            raise ValueError("Processing time not set for alarm")
        if alarm.alarm_type == ALARM_TYPE_MOTION:
            start_time, end_time = self._get_motion_context_window(alarm)
        else:
            start_time, end_time = self.get_alarm_context_window(alarm)

        if (
            start_time >= alarm.processing_start_time_utc
            and end_time <= alarm.processing_end_time_utc
        ):
            # Assumption here is that if you request a given video period,
            # You have received the entire video, except for parts without motion.
            # So it is enough to check if the previous request contains the
            # alarm context. If not, we should rerequest
            log.debug("Enough video received")
            video_details["video_check_result"] = VideoAnalyzerResult.ENOUGH
            return VideoAnalyzerResult.ENOUGH, (
                alarm.processing_start_time_utc,
                alarm.processing_end_time_utc,
            )

        enough_video = VideoAnalyzerResult.NOT_ENOUGH
        log.info(
            "Excess video details",
            video_end_time_utc=end_time,
            excess_video_length=end_time - alarm.video_end_time_utc,
        )
        # Do not ask for more than 5 minutes of video
        if end_time - alarm.alarm_time >= datetime.timedelta(
            minutes=self._max_request_time_mins
        ):
            log.info(
                "Cannot request more video",
                alarm_time=alarm.alarm_time,
                video_end_time=end_time,
                time_limit_mins=self._max_request_time_mins,
            )
            enough_video = VideoAnalyzerResult.REQUEST_TIME_LIMIT
        video_details["video_check_result"] = enough_video
        return enough_video, (
            start_time,
            end_time,
        )

    def _get_motion_context_window(
        self, alarm: Alarm
    ) -> typing.Tuple[datetime.datetime, datetime.datetime]:
        assert alarm.processing_start_time_utc is not None
        assert alarm.processing_end_time_utc is not None
        new_end_time = alarm.processing_end_time_utc
        inc_video_length = None
        if (
            alarm.last_motion_event_timestamp_utc
            and alarm.last_motion_event_timestamp_utc != alarm.alarm_time
        ):
            inc_video_length = MOTION_VIDEO_SEGMENT_DURATION_SECONDS
            tenant_conf = self._controller.tenant.get_config(alarm.tenant_id)
            if (
                tenant_conf
                and tenant_conf.motionConfig
                and "incrementalVideoDuration" in tenant_conf.motionConfig
            ):
                inc_video_length = tenant_conf.motionConfig[
                    "incrementalVideoDuration"
                ]
            assert inc_video_length is not None
            new_end_time = (
                alarm.last_motion_event_timestamp_utc
                + datetime.timedelta(seconds=inc_video_length)
            )
        elif alarm.alarm_time:
            inc_video_length = MOTION_VIDEO_SEGMENT_DURATION_SECONDS_v2
            tenant_conf = self._controller.tenant.get_config(alarm.tenant_id)
            if (
                tenant_conf
                and tenant_conf.motionConfig
                and "incrementalVideoDuration" in tenant_conf.motionConfig
            ):
                inc_video_length = tenant_conf.motionConfig[
                    "incrementalVideoDuration"
                ]
            assert inc_video_length is not None
            new_end_time = alarm.alarm_time + datetime.timedelta(
                seconds=inc_video_length
            )
        return alarm.processing_start_time_utc, new_end_time

    def get_alarm_context_window(
        self,
        alarm: Alarm,
    ) -> typing.Tuple[datetime.datetime, datetime.datetime]:
        """Generates the minimum start and end time of video
        required to analyzer a given alarm. If the alarm has an
        'end_event' (an event that signals the end of the alarm period),
        then we return the current video time, if the end event occurs
        within the period of video. If not, we request extra video.
        If the alarm does not have an end event, then we simply
        request video based on the alarm type.

        Returns:
            typing.Tuple[datetime.datetime, datetime.date]: start and end time
            of required video.
        """
        # Get the start and end time of context, purely based on alarm type
        blind_start, blind_end = self.get_door_opening_closing_times(alarm)
        # Determine alarm type to check for, and excess time required
        end_event_type = self._alarm_mapper.get_end_event_type(alarm)
        if end_event_type is None or alarm.door_uuid is None:
            log.info(
                "Canceled events not applicable",
                alarm_type=alarm.alarm_type,
                start_time=alarm.alarm_time,
                end_time=alarm.video_end_time_utc,
                door_id=alarm.door_uuid,
            )
            return blind_start, blind_end
        tenant_config = self._controller.tenant.get_config(alarm.tenant_id)
        if tenant_config is not None and tenant_config.dontUseEndEvents:
            log.info(
                "End events disabled",
                tenant_id=alarm.tenant_id,
            )
            return blind_start, blind_end

        first_cancel_time = self._alarm_mapper.get_end_event_time(
            alarm, end_event_type
        )
        if first_cancel_time:
            end_time = max(
                blind_end,
                first_cancel_time
                + datetime.timedelta(
                    seconds=config.HAIE.videoAnalyzer["endEventBuffer"]
                ),
            )
            return blind_start, end_time

        log.debug(
            "Did not find canceled/restored event",
        )
        internal_alarm_type = self._alarm_mapper.get_internal_alarm_type(alarm)
        if internal_alarm_type.startswith("Access Granted"):
            # For AG make sure that we request DHO threshold in any case, unless
            # we've already seen a door close.
            # If we've seen a door close, we won't reach this part of the code
            # If we do not have the dho threshold for the door
            # Only request for 20 seconds after the alarm
            dho_threshold = (
                get_dho_threshold_incl_heuristics(
                    self._controller, alarm.door_uuid, alarm.tenant_id
                )
                or 20
            )
            end_time = alarm.alarm_time + datetime.timedelta(
                seconds=dho_threshold
            )
            return blind_start, end_time
        # Re-request for excess video in increments
        excess_time_sec = 20
        if internal_alarm_type == "Door Forced Open":
            excess_time_sec = 10
        elif internal_alarm_type == "Door Held Open":
            excess_time_sec = 30
        if alarm.processing_end_time_utc is None:
            end_time = alarm.video_end_time_utc + datetime.timedelta(
                seconds=excess_time_sec
            )
            log.error("No alarm processing time")
        else:
            end_time = alarm.processing_end_time_utc + datetime.timedelta(
                seconds=excess_time_sec
            )
        return blind_start, end_time
