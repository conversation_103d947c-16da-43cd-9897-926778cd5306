import abc
import datetime
import time

import structlog
from pymysql import OperationalError
from structlog.contextvars import bind_contextvars, unbind_contextvars

from common_utils.typing_helpers import NUMBER
from controller import ControllerMap
from interfaces.alarm_state import AlarmState
from models_rds.rds_client import RDSClient

log = structlog.get_logger("hakimo", module="Base Sync Manager")


class BaseStateManager:
    SHUTDOWN = False

    def __init__(
        self,
        init_state: AlarmState,
        controller: ControllerMap,
        rds_client: RDSClient,
    ):
        assert isinstance(init_state, AlarmState), (
            "State manager must have a init"
            f" state of type AlarmState (not {type(init_state)})"
        )
        self._init_state = init_state
        self._controller = controller
        self._rds_client = rds_client
        # Amount of time an alarm must be in the state before
        # the state manager will pick it up
        self._state_timeout: NUMBER = 0

    @abc.abstractmethod
    def process_alarm(self, alarm_id: str, tenant_id: str):
        """Given an alarm in the init_state (self.init_state),
        process it appropriately (including transitions, messages) etc.

        Args:
            alarm_id ([str]): Alarm id to be processed
        """
        raise NotImplementedError

    def run(self):
        while not self.SHUTDOWN:
            log.info("Getting alarm details")
            alarm_details = self._controller.alarm.get_alarm_by_state(
                self._init_state,
                updated_before=datetime.datetime.utcnow()
                - datetime.timedelta(seconds=self.state_timeout),
            )
            if alarm_details is None:
                log.info("Got None alarm details, sleeping for 2 seconds")
                time.sleep(2)
                continue
            alarm_id, tenant_id = alarm_details
            bind_contextvars(alarm_id=alarm_id, tenant_id=tenant_id)
            try:
                self.process_alarm(alarm_id, tenant_id)
            except OperationalError as op_err:
                log.exception(
                    "db error: failed to update raw alarm in sync manager",
                    exc_info=op_err,
                    alarm_id=alarm_id,
                    tenant_id=tenant_id,
                )
            except Exception as e:  # pylint: disable=broad-except
                log.exception("Exception in base sync manager", exc_info=e)
            finally:
                unbind_contextvars("alarm_id", "tenant_id")

    @property
    def state_timeout(self) -> NUMBER:
        return self._state_timeout

    @state_timeout.setter
    def state_timeout(self, val: NUMBER):
        assert isinstance(
            val, (float, int)
        ), f"State timeout must be in seconds (int/float) not {type(val)}"
        self._state_timeout = val
