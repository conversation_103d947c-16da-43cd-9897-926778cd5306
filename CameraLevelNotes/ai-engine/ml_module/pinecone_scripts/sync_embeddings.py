import tempfile
import typing
from time import sleep, time
from typing import TYPE_CHECKING

import boto3
import face_recognition
import structlog
from dataclass_wizard import asdict
from pinecone.core.client.exceptions import PineconeException
from pinecone.core.grpc.protos.vector_service_pb2 import UpsertResponse
from pinecone.grpc import PineconeGRPC
from pinecone.grpc.future import PineconeGrpcFuture

if TYPE_CHECKING:
    from dataclasses import dataclass
else:
    from pydantic.dataclasses import dataclass

from common_utils.redis.redis_consumer_utils import RedisConsumerUtils
from config import backend_config as config
from ml_module.utils import download_alarm_video

log = structlog.get_logger("hakimo", module="Face Recognition DB Sync")


@dataclass  # pylint:disable=used-before-assignment
class PineconeMetadata:
    firstName: str
    lastName: str
    age: int
    gender: str
    employeeId: str
    added_timestamp: int


EMBEDDING = typing.List[float]


@dataclass
class PineconeInputData:
    id: str
    values: EMBEDDING
    metadata: PineconeMetadata


class Pinecone:
    def __init__(self):
        # TO-DO: change this to match other PRs
        client = PineconeGRPC(api_key=config.HAIE.PINECONE_API_KEY)
        self.index = client.Index(
            config.HAIE.FACE_RECOGNITION["pinecone_index"]
        )
        self._embedding_dim = config.HAIE.FACE_RECOGNITION["embedding_dim"]
        self._timeout = config.HAIE.FACE_RECOGNITION["timeout"]
        self._namespace = config.HAIE.FACE_RECOGNITION["namespace"]

    def upsert(
        self,
        inp_data: typing.List[typing.Dict],
    ) -> bool:
        assert self.index is not None
        try:
            upsert_response = self.index.upsert(
                vectors=inp_data,
                namespace=self._namespace,
                timeout=self._timeout,
            )
            if isinstance(upsert_response, UpsertResponse):
                return upsert_response.upserted_count == len(inp_data)
            elif isinstance(upsert_response, PineconeGrpcFuture):
                return True
            elif isinstance(upsert_response, dict):
                return upsert_response["upserted_count"] == len(inp_data)
            else:
                assert False
        except PineconeException as pe:
            log.warning("Hit Timeout in upsert", exc_info=pe)
            return False


class Syncer:
    def __init__(self, sync_wait: int = 300):
        self.sync_wait = sync_wait
        self.redis_consumer = RedisConsumerUtils()
        self.pinecone = Pinecone()
        self.processing_queue_name = config.HAIE.FACE_RECOGNITION[
            "processing_queue_name"
        ]
        self.main_queue_name = config.HAIE.FACE_RECOGNITION["main_queue_name"]
        self.use_aws = config.HAIE.FACE_RECOGNITION["use_aws_rekognition"]
        self.rekognition = boto3.client("rekognition")
        self.collection_id = config.HAIE.FACE_RECOGNITION[
            "rekognition_collection"
        ]

    def start(self):
        while True:
            self.run_sync()
            sleep(self.sync_wait)

    def download_and_get_embedding(
        self, image_url: str
    ) -> typing.Tuple[bool, EMBEDDING]:
        with tempfile.TemporaryDirectory() as tmp_dir:
            image_path = download_alarm_video(image_url, tmp_dir)
            img = face_recognition.load_image_file(image_path)
            if img is None:
                return False, [
                    0.0,
                ]
            encoding = face_recognition.face_encodings(img)[0]
            return True, encoding.tolist()

    def add_face_to_aws_rekognition(
        self, image_url: str, profile_id: str
    ) -> bool:
        # Ensure collection exists
        try:
            self.rekognition.describe_collection(
                CollectionId=self.collection_id
            )
        except self.rekognition.exceptions.ResourceNotFoundException:
            try:
                self.rekognition.create_collection(
                    CollectionId=self.collection_id
                )
                log.info(
                    "Created new Rekognition collection",
                    collection_id=self.collection_id,
                )
            except Exception as e:
                log.error(
                    "Failed to create Rekognition collection", exc_info=e
                )
                return False

        # Download image from URL to temp file
        try:
            with tempfile.TemporaryDirectory() as tmp_dir:
                image_path = download_alarm_video(image_url, tmp_dir)
                with open(image_path, "rb") as image_file:
                    image_bytes = image_file.read()
        except Exception as e:
            log.error(
                "Failed to download image from URL",
                image_url=image_url,
                exc_info=e,
            )
            return False

        # Index face
        try:
            response = self.rekognition.index_faces(
                CollectionId=self.collection_id,
                Image={"Bytes": image_bytes},
                ExternalImageId=profile_id,
                DetectionAttributes=["DEFAULT"],
            )
            if response["FaceRecords"]:
                log.info("Face added to Rekognition", profile_id=profile_id)
                return True
            else:
                log.warning("No face indexed", profile_id=profile_id)
                return False
        except Exception as e:
            log.error("Error indexing face to Rekognition", exc_info=e)
            return False

    def sync_data(
        self, profile_id: str, image_url: str, message: typing.Dict
    ) -> bool:
        if self.use_aws:
            rekognition_success = self.add_face_to_aws_rekognition(
                image_url, profile_id
            )
            if not rekognition_success:
                return False
            return True

        success, embedding = self.download_and_get_embedding(image_url)
        if not success:
            log.error(
                "Failed to sync", profile_id=profile_id, image_url=image_url
            )
            return False

        added_timestamp = int(message.get("timestamp", time()))
        firstName = message.get("firstName", "Catty")
        lastName = message.get("lastName", "Purry")
        age = message.get("age", 100)
        gender = message.get("gender", "cat")
        employeeId = message.get("employeeId", "cat123")
        input_data = [
            asdict(
                PineconeInputData(
                    f"{profile_id}_{added_timestamp}",
                    embedding,
                    PineconeMetadata(
                        firstName,
                        lastName,
                        age,
                        gender,
                        employeeId,
                        added_timestamp,
                    ),
                )
            ),
        ]

        return self.pinecone.upsert(
            inp_data=input_data,
        )

    def run_sync(self):
        message = self.redis_consumer.get_message_from_queue(
            self.main_queue_name
        )
        if message is None:
            log.info(
                "No message in queue to process fetching from set",
            )
            message = self.redis_consumer.get_old_unprocessed_message(
                self.processing_queue_name
            )
        if message:
            profile_id = message.pop("profile_id")
            image_url = message.pop("image_url")
            success = self.sync_data(profile_id, image_url, message)
            if success:
                self.redis_consumer.ack(message, self.processing_queue_name)


if __name__ == "__main__":
    syncr = Syncer(300)
    syncr.run_sync()
