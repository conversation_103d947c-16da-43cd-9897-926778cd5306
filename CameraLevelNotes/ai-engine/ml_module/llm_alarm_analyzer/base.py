import abc
import random
from dataclasses import dataclass, field
from typing import Dict, List, Optional


@dataclass
class AIResultRA:
    success: bool = False
    reason: Optional[str] = None
    analysis: Optional[str] = None
    recommendation: Optional[str] = None
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None


@dataclass
class AIResultLA:
    success: bool = False
    reason: Optional[str] = None
    explanation: Optional[str] = None
    summary: Optional[str] = None
    recommendation: Optional[str] = None
    score: Optional[int] = None
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None


@dataclass
class LLMRequestResponse:
    success: bool = False
    data: Dict[str, str] = field(default_factory=dict)
    full_output: str = ""
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    error_reason: Optional[str] = None


def calculate_retry_sleep_time(
    retry_count: int,
    base_delay: float,
    max_delay: float,
    jitter_factor: float,
) -> float:
    delay = min(max_delay, base_delay * (2 ** (retry_count - 1)))
    jitter = random.uniform(0, delay * jitter_factor)
    return delay + jitter


class LLMClient:
    def __init__(
        self,
        api_key: str,
        base_url: str,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 3.0,
        jitter_factor: float = 1.0,
        timeout: int = 15,
    ):
        self.api_key = api_key
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.jitter_factor = jitter_factor

    @abc.abstractmethod
    def send_request(
        self,
        tenant_id: Optional[str],
        alarm_phase: Optional[str],
        request_text: Optional[str],
        video_uri: Optional[str] = None,
        video_bytes: Optional[str] = None,
        images: Optional[List[str]] = None,
        image_mime_type: str = "image/webp",
        temperature: float = 0.1,
    ) -> LLMRequestResponse:
        raise NotImplementedError

    @abc.abstractmethod
    def send_request_with_video(
        self,
        tenant_id: Optional[str],
        text: str,
        video_path: str,
        use_video_uri: bool = False,
        temperature: float = 0.1,
    ) -> LLMRequestResponse:
        raise NotImplementedError
