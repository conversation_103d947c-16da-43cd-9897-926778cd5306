import datetime
import typing
from bisect import bisect_right
from operator import itemgetter

import structlog

import controller as ctrl
from common_utils.typing_helpers import NUMBER
from config import backend_config as config
from controller.alarm.alarm_filters import AlarmFilters
from interfaces.alarm import Alarm
from interfaces.alarm_mapper import Alarm<PERSON>apper
from interfaces.entities.entity import Entity, EntryType
from interfaces.tags import Tags
from ml_module.tap_generator._unauthorized_entries.utils import (
    deduplicate_list_with_employee_ids,
    find_smallest_time_diff,
    get_entry_times_from_entities,
    get_match_params,
)
from ml_module.utils import get_dho_threshold_incl_heuristics
from models_rds.rds_client import RDSClient

from .constants import accessEntryWindowAfter, accessEntryWindowBefore
from .matching import match_ag_and_entries

log = structlog.get_logger(
    "hakimo", module="TAP Generator/Unauthorized Entry Checker v2"
)


class UnauthorizedAccessCheckerv2:
    def __init__(
        self,
        rds_client: R<PERSON><PERSON><PERSON>,
        controller: ctrl.Controller<PERSON>ap,
        alarm_mapper: AlarmMapper,
    ):
        self._rds_client = rds_client
        self._controller = controller
        self._alarm_mapper = alarm_mapper

    def check_unauthorized_access(
        self, tags: Tags, alarm: Alarm, check_escorts: bool = False
    ) -> typing.Tuple[
        typing.List[Entity],
        typing.List[Entity],
        typing.Sequence[datetime.datetime],
    ]:
        """
        Returns a tuple of ununauthorized entries, entries which are escorted,
        and all the raw access granted times in the context of this alarm
        """
        acc_granted_type = self._rds_client.get_alarm_type_id_from_alarm_type(
            AlarmMapper.get_ag_regex() + r"|(Open Door Command Issued.*)",
            regex=True,
        )
        if alarm.door_uuid is None or not tags.entities:
            # If door is unknown, we can't get AG times
            # If no entities present, no unauthorized entries
            return [], [], []

        tag_entities = [x for x in tags.entities if isinstance(x, Entity)]
        entry_times = get_entry_times_from_entities(
            tag_entities, alarm.video_start_time_utc
        )
        if not entry_times:
            # If no entries present, no unauthorized entries
            return [], [], []
        # Get DHO threshold for this door, if it exists
        dho_threshold = get_dho_threshold_incl_heuristics(
            self._controller, alarm.door_uuid, alarm.tenant_id
        )

        match_params = get_match_params(entry_times[0][1])
        videoAdjustmentTime = config.HAIE.heuristics.get(
            "videoAdjustmentTime", 0
        )
        ag_window = self._get_access_granted_window(
            entry_times, dho_threshold, videoAdjustmentTime, match_params
        )
        db_rows = self._get_alarms_in_window(
            ag_window, alarm.door_uuid, alarm.tenant_id, acc_granted_type
        )
        acc_granted_times = [row[0].alarm_timestamp_utc for row in db_rows]
        authorized_entry_idx = match_ag_and_entries(
            acc_granted_times, [i[0] for i in entry_times], match_params
        )
        for entry_idx, ag_idx in authorized_entry_idx.items():
            entry_times[entry_idx][1].entry_type = EntryType.AUTHORIZED
            entry_times[entry_idx][1].associated_alarm = db_rows[ag_idx][
                0
            ].uuid
        log.debug(
            "Assigned Authorized Entities",
            acc_granted_times=acc_granted_times,
            entry_times=entry_times,
            authorized_entry_idx=authorized_entry_idx.items(),
        )
        if check_escorts:
            escort_ags = [row[0] for row in db_rows if row[4].is_escort]
            escort_ag_times = [
                alarm.alarm_timestamp_utc for alarm in escort_ags
            ]
            if len(escort_ags) > 0:
                unauthorized_entries = [
                    entry
                    for entry in entry_times
                    if entry[1].entry_type == EntryType.UNKNOWN
                ]
                escorted_entry_idx = self._find_escorted_entities(
                    entries=[entry[0] for entry in unauthorized_entries],
                    escort_acc_granted_times=escort_ag_times,
                    match_params=match_params,
                )
                for entry_idx, ag_idx in escorted_entry_idx.items():
                    unauthorized_entries[entry_idx][
                        1
                    ].entry_type = EntryType.ESCORTED
                    unauthorized_entries[entry_idx][
                        1
                    ].associated_alarm = escort_ags[ag_idx].uuid
                log.debug(
                    "Assigned Escorted Entities",
                    acc_granted_times=acc_granted_times,
                    entry_times=entry_times,
                    escorted_entry_idx=escorted_entry_idx.items(),
                )
        unauthorized_entries = [
            entry
            for entry in entry_times
            if entry[1].entry_type == EntryType.UNKNOWN
        ]
        if len(acc_granted_times) > 0:
            unauthorized_entry_idx = self._find_unauthorized_entities(
                entries=[entry[0] for entry in unauthorized_entries],
                acc_granted_times=acc_granted_times,
            )
            for entry_idx, ag_idx in unauthorized_entry_idx.items():
                unauthorized_entries[entry_idx][
                    1
                ].entry_type = EntryType.UNAUTHORIZED
                unauthorized_entries[entry_idx][1].associated_alarm = db_rows[
                    ag_idx
                ][0].uuid
        else:
            unauthorized_entry_idx = dict()
            for entry in unauthorized_entries:
                entry[1].entry_type = EntryType.UNAUTHORIZED
                entry[1].associated_alarm = alarm.alarm_uuid
        log.debug(
            "Assigned Unauthorized Entities",
            acc_granted_times=acc_granted_times,
            entry_times=entry_times,
            unauthorized_entry_idx=unauthorized_entry_idx.items(),
        )
        relevant_entries = list(
            filter(
                lambda x: x[1].associated_alarm == alarm.alarm_uuid,
                entry_times,
            )
        )
        relevant_unautorized_entities = list(
            map(
                itemgetter(1),
                filter(
                    lambda x: x[1].entry_type == EntryType.UNAUTHORIZED,
                    relevant_entries,
                ),
            )
        )
        relevant_escorted_entities = list(
            map(
                itemgetter(1),
                filter(
                    lambda x: x[1].entry_type == EntryType.ESCORTED,
                    relevant_entries,
                ),
            )
        )
        return (
            relevant_unautorized_entities,
            relevant_escorted_entities,
            acc_granted_times,
        )

    @staticmethod
    def _get_access_granted_window(
        entry_times: typing.List[typing.Tuple[datetime.datetime, Entity]],
        dho_threshold: typing.Optional[NUMBER],
        videoAdjustmentTime: NUMBER,
        match_params: typing.Dict[str, NUMBER],
    ) -> typing.Tuple[datetime.datetime, datetime.datetime]:
        entry_times = sorted(entry_times, key=lambda x: x[0])
        first_entry = entry_times[0][0]
        last_entry = entry_times[-1][0]
        video_adjustment = datetime.timedelta(seconds=videoAdjustmentTime)
        prev_ag_adjustment = datetime.timedelta(
            seconds=(
                max(dho_threshold, match_params[accessEntryWindowAfter])
                if dho_threshold
                else match_params[accessEntryWindowAfter]
            )
        )
        ag_window_start = first_entry - prev_ag_adjustment - video_adjustment
        ag_window_end = (
            last_entry
            + datetime.timedelta(seconds=match_params[accessEntryWindowBefore])
            + video_adjustment
        )
        return ag_window_start, ag_window_end

    def _get_alarms_in_window(
        self,
        ag_window: typing.Tuple[datetime.datetime, datetime.datetime],
        door_uuid: str,
        tenant_id: str,
        acc_granted_type: typing.List[str],
    ):
        filters = AlarmFilters(
            utc_time_interval=(ag_window[0], ag_window[1]),
            display_only=False,
            door_uuid=([door_uuid], True),
            alarm_type_uuid=(acc_granted_type, True),
            order_desc=False,
        )
        db_rows = self._controller.alarm.fetch_alarms(
            tenant_ids=[tenant_id], filters=filters, limit=None
        )
        db_rows = deduplicate_list_with_employee_ids(
            db_rows, employee_ids=[row[0].employee_id for row in db_rows]
        )
        return db_rows

    @staticmethod
    def _find_unauthorized_entities(
        entries: typing.List[datetime.datetime],
        acc_granted_times: typing.Sequence[datetime.datetime],
    ) -> typing.Dict[int, int]:
        """
        It takes a list of times
        when a person entered a room, and a list of times when the person was granted
        access to the room, and returns a dictionary that maps the index of each entry
        time to the index of the access time that is closest to it

        Args:
          entries (typing.List[datetime.datetime]): a list of datetime objects, representing the time of
        each entry
          acc_granted_times (typing.Sequence[datetime.datetime]): a list of datetime objects, sorted in
        ascending order

        Returns:
          A dictionary of the index of the entry and the index of the access granted time.
        """
        unauthorized_idx: typing.Dict[int, int] = {}
        if not entries:
            # empty input, return empty list
            return unauthorized_idx
        for entry_idx, entry_time in enumerate(entries):
            idx = bisect_right(acc_granted_times, entry_time)
            if idx == len(acc_granted_times):
                idx -= 1
            unauthorized_idx[entry_idx] = idx
        return unauthorized_idx

    @staticmethod
    def _find_escorted_entities(
        entries: typing.List[datetime.datetime],
        escort_acc_granted_times: typing.Sequence[datetime.datetime],
        match_params: typing.Dict[str, NUMBER],
    ) -> typing.Dict[int, int]:
        """
        > Given a list of unauthorised entry times, and a list of authorised entry times, find the
        indices of the unauthorised entries that are within a certain escort time window of the authorised
        entries

        Args:
          entries (typing.List[datetime.datetime]): a list of datetime objects, representing the times
        of unauthorized entries
          escort_acc_granted_times (typing.Sequence[datetime.datetime]): a list of datetime objects
          match_params (typing.Dict[str, NUMBER]): a dictionary of parameters that are used to determine
        if a match is valid.

        Returns:
          A dictionary of the index of the entry and the index of the access granted time.
        """
        escorted_idx: typing.Dict[int, int] = {}
        if not entries:
            # empty input, return empty list
            return escorted_idx
        escort_threshold = match_params["escortEntryWindow"]
        for entry_idx, unauth_entry_time in enumerate(entries):
            time_diff, ag_idx = find_smallest_time_diff(
                escort_acc_granted_times, unauth_entry_time
            )
            if time_diff < escort_threshold:
                escorted_idx[entry_idx] = ag_idx
        return escorted_idx
