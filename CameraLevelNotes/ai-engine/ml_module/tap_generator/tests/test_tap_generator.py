import datetime
import typing
from unittest.mock import patch

import pytest

from controller import ControllerMap
from interfaces.alarm import Alarm
from interfaces.alarm_mapper import AlarmMapper
from interfaces.camera_details import CameraPosition
from interfaces.entities.entity import MovementTypes
from interfaces.entities.person import Person
from interfaces.group_priority import GroupPriority
from interfaces.scene_info import SceneInfo
from interfaces.tags import Tags, TagTypes
from interfaces.tenant_config import AlarmProcessingConfig
from ml_module.detection import BaseDetection
from ml_module.tap_generator import (
    <PERSON><PERSON><PERSON><PERSON>,
    DF<PERSON><PERSON><PERSON>,
    D<PERSON><PERSON>andler,
    GenericAlarmHandler,
    InvalidAccess<PERSON>evel<PERSON>andler,
    InvalidBadgeHandler,
    <PERSON><PERSON>sance<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    UnknownAlarmHandler,
)
from ml_module.tap_generator.tests.test_data import TestTAPData
from ml_module.track import BaseTrack
from ml_module.video_time_analyzer.analyzer import VideoAnalyzerResult


class TestTapGenerator:
    @pytest.fixture
    def ml_mock_rds_client(self):  # pylint: disable=no-self-use
        return TestTAPData().get_rds_client()

    @pytest.fixture
    def controller(self, ml_mock_rds_client):
        yield ControllerMap(ml_mock_rds_client.db_adapter)

    @pytest.fixture
    def alarm_mapper(self, controller):  # pylint: disable=no-self-use
        with patch("prometheus_client.Summary"):
            yield AlarmMapper(controller)

    @pytest.fixture
    def tap_gen(self, ml_mock_rds_client, alarm_mapper):
        return TAPGenerator(alarm_mapper, ml_mock_rds_client)

    @pytest.fixture
    def default_config(self, controller):
        return controller.alarm.get_processing_config(
            Alarm(
                alarm_time="2022-10-10 06:30:27",
                alarm_type="test alarm type",
                tenant_id="test_tenant",
            )
        )

    @pytest.fixture
    def default_alarm_proc_config(self):
        return AlarmProcessingConfig()

    @pytest.fixture
    def custom_alarm_proc_config(self):
        alarmProcessingConfig = AlarmProcessingConfig()
        alarmProcessingConfig.priority = GroupPriority.ignore
        alarmProcessingConfig.enableForAllAlarmType = False
        alarmProcessingConfig.alarm_types = [
            "Door Forced Open",
            "Door Held Open",
        ]
        return alarmProcessingConfig

    @pytest.mark.parametrize(
        "alarm,method",
        [
            (
                Alarm(
                    alarm_type="Access Granted - No Entry",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant_wo_tailgating",
                ),
                AGHandler.get_access_granted_tap,
            ),
            (
                Alarm(
                    alarm_type="Access denied",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant_wo_tailgating",
                ),
                InvalidAccessLevelHandler().handle_event,
            ),
            (
                Alarm(
                    alarm_type="Access Granted - No Entry",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                AGHandler.get_access_granted_tap,
            ),
            (
                Alarm(
                    alarm_type="Door Forced Open",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                DFOHandler().handle_event,
            ),
            (
                Alarm(
                    alarm_type="Secret Unknown Test alarm",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                UnknownAlarmHandler.handle_unknown_alarm,
            ),
            (
                Alarm(
                    alarm_type="Custom Name for DFO Door Force",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                DFOHandler().handle_event,
            ),
            (
                Alarm(
                    alarm_type="Momentary unlock",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                AGHandler.get_access_granted_tap_wo_tailgating,
            ),
            (
                Alarm(
                    alarm_type="Alarm Mask Group Disarmed",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                NuisanceHandler.autoresolve_nuisance,
            ),
        ],
    )
    def test_tap_generation_routing(
        self,
        alarm,
        method,
        tap_gen: TAPGenerator,
        default_config: AlarmProcessingConfig,
    ):
        resol_func = tap_gen.get_resolution_function(
            tap_gen._alarm_mapper.get_internal_alarm_type(alarm),
            default_config,
        )
        assert (
            resol_func.__name__ == method.__name__
            and resol_func.__module__ == method.__module__
            and resol_func.__doc__ == method.__doc__
        )

    @pytest.mark.parametrize(
        "alarm,method",
        [
            (
                Alarm(
                    alarm_type="Access Granted - No Entry",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant_wo_tailgating",
                ),
                AGHandler.get_access_granted_tap,
            ),
            (
                Alarm(
                    alarm_type="Access denied",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant_wo_tailgating",
                ),
                InvalidAccessLevelHandler().handle_event,
            ),
            (
                Alarm(
                    alarm_type="Access Granted - No Entry",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                AGHandler.get_access_granted_tap,
            ),
            (
                Alarm(
                    alarm_type="Door Forced Open",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                DFOHandler().handle_event,
            ),
            (
                Alarm(
                    alarm_type="Custom Name for DFO Door Force",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                DFOHandler().handle_event,
            ),
            (
                Alarm(
                    alarm_type="Momentary unlock",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                AGHandler.get_access_granted_tap_wo_tailgating,
            ),
            (
                Alarm(
                    alarm_type="Alarm Mask Group Disarmed",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                NuisanceHandler.autoresolve_nuisance,
            ),
            (
                Alarm(
                    alarm_type="Generic Test alarm",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                GenericAlarmHandler().handle_event,
            ),
        ],
    )
    def test_tap_custom_generation_routing(
        self,
        alarm,
        method,
        tap_gen: TAPGenerator,
        custom_alarm_proc_config: AlarmProcessingConfig,
    ):
        resol_func = tap_gen.get_custom_resolution_function(
            tap_gen._alarm_mapper.get_internal_alarm_type(alarm),
            custom_alarm_proc_config,
        )
        assert (
            resol_func.__name__ == method.__name__
            and resol_func.__module__ == method.__module__
            and resol_func.__doc__ == method.__doc__
        )

    @pytest.mark.parametrize(
        "alarm,entry_times,unauth_entries",
        [
            (  # 0
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 25
                        )
                        - datetime.timedelta(seconds=10),
                        "video_end_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 25
                        )
                        + datetime.timedelta(seconds=13),
                        "alarm_uuid": "ra1",
                        "alarm_time": datetime.datetime(2020, 1, 1, 1, 10, 25),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [10, 12],
                True,  # Deduplicated AG
            ),
            (  # 1
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 25
                        )
                        - datetime.timedelta(seconds=10),
                        "video_end_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 25
                        )
                        + datetime.timedelta(seconds=13),
                        "alarm_uuid": "ra1",
                        "alarm_time": datetime.datetime(2020, 1, 1, 1, 10, 25),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [10, 12, 12],
                True,
            ),
            # Last door close at 10:00:00
            # Next door close at 10:00:10
            (  # 2
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            1990, 9, 27, 9, 59, 55
                        ),
                        "video_end_time_utc": datetime.datetime(
                            1990,
                            9,
                            27,
                            10,
                            5,
                        ),
                        "alarm_uuid": "ra1",
                        "alarm_time": datetime.datetime(1990, 9, 27, 10, 0, 5),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [2, 11, 16],
                False,
            ),
            # AG 1 sec before a door close at 10:10
            # Door close should not be used to clip
            (  # 3
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            1990, 9, 27, 9, 59, 55
                        ),
                        "video_end_time_utc": datetime.datetime(
                            1990,
                            9,
                            27,
                            10,
                            5,
                        ),
                        "alarm_uuid": "ra21",
                        "alarm_time": datetime.datetime(1990, 9, 27, 10, 0, 9),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [4, 16, 18],
                True,
            ),
            # AG 1 sec before a door close at 10:00
            # Door close should not be used to clip
            (  # 4
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            1990, 9, 27, 9, 59, 50
                        ),
                        "video_end_time_utc": datetime.datetime(
                            1990,
                            9,
                            27,
                            10,
                            5,
                        ),
                        "alarm_uuid": "ra1",
                        "alarm_time": datetime.datetime(
                            1990, 9, 27, 9, 59, 59
                        ),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [4, 16, 21],
                False,
            ),
            (  # 5
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            1990, 9, 27, 9, 59, 50
                        ),
                        "video_end_time_utc": datetime.datetime(
                            1990,
                            9,
                            27,
                            10,
                            5,
                        ),
                        "alarm_uuid": "ra21",
                        "alarm_time": datetime.datetime(
                            1990, 9, 27, 9, 59, 59
                        ),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [4, 16, 18],
                True,
            ),
            (  # 6
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            1990, 9, 27, 9, 59, 55
                        ),
                        "video_end_time_utc": datetime.datetime(
                            1990,
                            9,
                            27,
                            10,
                            5,
                        ),
                        "alarm_uuid": "ra21",
                        "alarm_time": datetime.datetime(1990, 9, 27, 10, 0, 5),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [2, 11],
                False,
            ),
            (  # 7
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            1990, 9, 27, 9, 59, 55
                        ),
                        "video_end_time_utc": datetime.datetime(
                            1990,
                            9,
                            27,
                            10,
                            5,
                        ),
                        "alarm_uuid": "ra21",
                        "alarm_time": datetime.datetime(1990, 9, 27, 10, 0, 5),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [2, 11, 12],
                True,
            ),
            (  # 8
                Alarm(
                    **{
                        "alarm_uuid": "ra1",
                        "door_uuid": "d1",
                        "alarm_time": datetime.datetime(1989, 9, 27, 10, 0, 0),
                        "video_start_time_utc": (
                            datetime.datetime(1989, 9, 27, 10, 0, 0)
                            - datetime.timedelta(seconds=20)
                        ),
                        "video_end_time_utc": (
                            datetime.datetime(1989, 9, 27, 10, 0, 0)
                            + datetime.timedelta(seconds=20)
                        ),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [21, 32],
                True,
            ),
            (  # 9
                Alarm(
                    **{
                        "alarm_uuid": "ra1",
                        "door_uuid": "d1",
                        "alarm_time": datetime.datetime(1989, 9, 27, 10, 0, 0),
                        "video_start_time_utc": (
                            datetime.datetime(1989, 9, 27, 10, 0, 0)
                            - datetime.timedelta(seconds=20)
                        ),
                        "video_end_time_utc": (
                            datetime.datetime(1989, 9, 27, 10, 0, 0)
                            + datetime.timedelta(seconds=20)
                        ),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [19, 36],
                False,
            ),
            (  # 10
                Alarm(
                    **{
                        "alarm_uuid": "ra1",
                        "door_uuid": "d1",
                        "alarm_time": datetime.datetime(1989, 9, 27, 10, 0, 0),
                        "video_start_time_utc": (
                            datetime.datetime(1989, 9, 27, 10, 0, 0)
                            - datetime.timedelta(seconds=20)
                        ),
                        "video_end_time_utc": (
                            datetime.datetime(1989, 9, 27, 10, 0, 0)
                            + datetime.timedelta(seconds=20)
                        ),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [26, 35],
                False,
            ),
            (  # 11
                Alarm(
                    **{
                        "alarm_uuid": "ra1",
                        "door_uuid": "d1",
                        "alarm_time": datetime.datetime(
                            1989, 9, 27, 10, 0, 20
                        ),
                        "video_start_time_utc": (
                            datetime.datetime(1989, 9, 27, 10, 0, 20)
                            - datetime.timedelta(seconds=10)
                        ),
                        "video_end_time_utc": (
                            datetime.datetime(1989, 9, 27, 10, 0, 20)
                            + datetime.timedelta(seconds=10)
                        ),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [8, 19],
                True,
            ),
            (  # 12
                Alarm(
                    **{
                        "alarm_uuid": "ra1",
                        "door_uuid": "d1",
                        "alarm_time": datetime.datetime(
                            1988,
                            9,
                            27,
                            10,
                            0,
                            20,
                        ),
                        "video_start_time_utc": (
                            datetime.datetime(1988, 9, 27, 10, 0, 20)
                            - datetime.timedelta(seconds=10)
                        ),
                        "video_end_time_utc": (
                            datetime.datetime(1988, 9, 27, 10, 0, 20)
                            + datetime.timedelta(seconds=10)
                        ),
                        "alarm_type": "Access Granted No Entry Made",
                        "tenant_id": "test_tenant",
                    }
                ),
                [6, 7, 10],
                False,
            ),
        ],
    )
    def test_unauthorized_access_check(
        self, tap_gen: TAPGenerator, alarm, entry_times, unauth_entries
    ):  # pylint: disable=no-self-use
        people = []
        for time in entry_times:
            person = Person()
            person.movement_pattern = MovementTypes.ENTRY
            person.entry_time = time
            people.append(person)
        video_tags = Tags(people, {"tags": ["entry"]})
        # TODO: Fix tests by adding query alarm also to the DB (as in the real system)
        if unauth_entries:
            assert tap_gen._handlers["unauth_access"](video_tags, alarm)[0]
        else:
            assert not tap_gen._handlers["unauth_access"](video_tags, alarm)[0]

    @pytest.mark.parametrize(
        "alarm,entry_times,unauth_entries,escorted_entries",
        [
            (
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            1994, 9, 27, 4, 20, 0
                        )
                        - datetime.timedelta(seconds=60),
                        "video_end_time_utc": datetime.datetime(
                            1994, 9, 27, 4, 20, 0
                        )
                        + datetime.timedelta(seconds=60),
                        "alarm_uuid": "ra18",
                        "alarm_time": datetime.datetime(1994, 9, 27, 4, 20, 0),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [60, 65],
                False,
                True,
            ),
            (
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            1994, 9, 27, 4, 20, 0
                        )
                        - datetime.timedelta(seconds=60),
                        "video_end_time_utc": datetime.datetime(
                            1994, 9, 27, 4, 20, 0
                        )
                        + datetime.timedelta(seconds=60),
                        "alarm_uuid": "ra18",
                        "alarm_time": datetime.datetime(1994, 9, 27, 4, 20, 0),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [60, 65, 110],
                True,
                True,
            ),
            (
                Alarm(
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            1994, 9, 27, 4, 20, 0
                        )
                        - datetime.timedelta(seconds=60),
                        "video_end_time_utc": datetime.datetime(
                            1994, 9, 27, 4, 20, 0
                        )
                        + datetime.timedelta(seconds=60),
                        "alarm_uuid": "ra18",
                        "alarm_time": datetime.datetime(1994, 9, 27, 4, 20, 0),
                        "alarm_type": "Access Granted",
                        "tenant_id": "test_tenant",
                    }
                ),
                [],
                False,
                False,
            ),
        ],
    )
    def test_escorted_access_check(
        self,
        tap_gen: TAPGenerator,
        alarm,
        entry_times,
        unauth_entries,
        escorted_entries,
    ):  # pylint: disable=no-self-use
        people = []
        for time in entry_times:
            person = Person()
            person.movement_pattern = MovementTypes.ENTRY
            person.entry_time = time
            people.append(person)
        video_tags = Tags(people, {"tags": ["entry"]})
        unauth_entities, escorted_entities, _ = tap_gen._handlers[
            "unauth_access"
        ](video_tags, alarm, check_escorts=True)
        assert (len(unauth_entities) != 0) == unauth_entries
        assert (len(escorted_entities) != 0) == escorted_entries

    @pytest.mark.parametrize(
        "video_tags,expected_tap,escort_tap,tailgating_tap",
        [
            (
                Tags(
                    [],
                    {"tags": ["entry", "unauthorized_entry"]},
                ),
                35,
                70,
                35,
            ),
            (
                Tags(
                    [],
                    {"tags": ["entry", "escorted_entry"]},
                ),
                70,
                70,
                70,
            ),
            (
                Tags(
                    [],
                    {"tags": ["entry", "escorted_entry"]},
                ),
                55,
                55,
                70,
            ),
            (
                Tags(
                    [],
                    {"tags": ["loitering_secure_side"]},
                ),
                7,
                55,
                70,
            ),
            (
                Tags(
                    [],
                    {"tags": ["loitering_unsecure_side"]},
                ),
                9,
                55,
                70,
            ),
        ],
    )
    def test_ag_tap(
        self,
        default_config,
        video_tags,
        expected_tap,
        escort_tap,
        tailgating_tap,
    ):  # pylint: disable=no-self-use,unused-argument
        default_config.escortTailgatingTAP = escort_tap
        default_config.tailgatingTAP = tailgating_tap
        tap = AGHandler.get_access_granted_tap(video_tags, default_config)[0]
        assert tap == expected_tap

    @pytest.mark.parametrize(
        "video_tags,expected_tap",
        [
            (
                Tags(
                    [],
                    {"tags": ["entry", "unauthorized_entry"]},
                ),
                9,
            ),
            (
                Tags(
                    [],
                    {"tags": ["entry", "escorted_entry"]},
                ),
                8,
            ),
            (
                Tags(
                    [],
                    {"tags": []},
                ),
                5,
            ),
            (
                Tags(
                    [],
                    {"tags": ["loitering_secure_side"]},
                ),
                7,
            ),
            (
                Tags(
                    [],
                    {"tags": ["loitering_unsecure_side"]},
                ),
                8,
            ),
        ],
    )
    def test_ag_no_tailgating_tap(
        self, default_config, video_tags, expected_tap
    ):  # pylint: disable=no-self-use,unused-argument
        tap = AGHandler.get_access_granted_tap_wo_tailgating(
            video_tags, default_config
        )[0]
        assert tap == expected_tap

    def test_dfo(self, tap_gen: TAPGenerator, default_config):  # pylint: disable=no-self-use
        alarm = Alarm(
            alarm_type="Door Forced Open",
            raw_info={},
            alarm_time=datetime.datetime(2020, 1, 1, 4, 10, 25),
            **{
                "door_uuid": "d1",
                "video_start_time_utc": datetime.datetime(
                    2020, 1, 1, 4, 10, 25
                )
                - datetime.timedelta(seconds=10),
                "video_end_time_utc": datetime.datetime(2020, 1, 1, 4, 10, 25)
                + datetime.timedelta(seconds=10),
                "alarm_uuid": "ra3",
                "tenant_id": "test_tenant",
            },
        )

        person1 = Person()
        person1.movement_pattern = MovementTypes.ENTRY
        person1.entry_time = 12
        video_tags = Tags([person1], {"tags": ["entry"]})
        assert tap_gen._handlers["unauth_access"](video_tags, alarm)[0]
        video_tags = tap_gen.get_video_tags(
            [person1],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
        )
        assert TagTypes.UNAUTHORIZED_ENTRY in video_tags
        assert TagTypes.ENTRY not in video_tags
        video_tags = tap_gen.get_video_tags(
            [],
            alarm,
            {"video_check_result": VideoAnalyzerResult.VIDEO_UNAVAILABLE},
            default_config,
            None,
        )
        assert DFOHandler().handle_event(video_tags)[0] > 50
        video_tags = tap_gen.get_video_tags(
            [person1],
            alarm,
            {"video_check_result": VideoAnalyzerResult.MORE_VIDEO_UNAVAILABLE},
            default_config,
            None,
        )
        assert TagTypes.MORE_VIDEO_UNAVAILABLE in video_tags
        assert DFOHandler().handle_event(video_tags)[0] > 50

    @pytest.mark.parametrize("config_type", ["default", "P2"])
    def test_dfo_with_entry_exit(
        self, tap_gen: TAPGenerator, default_config, config_type
    ):  # pylint: disable=no-self-use
        default_config.priority = getattr(GroupPriority, config_type)
        alarm = Alarm(
            alarm_type="Door Forced Open",
            raw_info={},
            **{
                "door_uuid": "d1",
                "video_start_time_utc": datetime.datetime(
                    2020, 1, 1, 4, 10, 25
                )
                - datetime.timedelta(seconds=10),
                "video_end_time_utc": datetime.datetime(2020, 1, 1, 4, 10, 25)
                + datetime.timedelta(seconds=10),
                "alarm_uuid": "ra3",
                "alarm_time": datetime.datetime(2020, 1, 1, 4, 10, 25),
                "tenant_id": "test_tenant",
            },
        )

        person1 = Person()
        person1.movement_pattern = MovementTypes.EXIT
        person1.exit_time = 8
        person2 = Person()
        person2.movement_pattern = MovementTypes.ENTRY
        person2.entry_time = 10
        video_tags = Tags([person1, person2], {"tags": ["exit", "entry"]})
        assert tap_gen._handlers["unauth_access"](video_tags, alarm)[0]
        video_tags = tap_gen.get_video_tags(
            [person1, person2],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
        )
        assert DFOHandler().handle_event(video_tags, default_config)[0] > 50  # pylint: disable=protected-access
        assert TagTypes.UNAUTHORIZED_ENTRY in video_tags
        assert TagTypes.ENTRY not in video_tags

    @pytest.mark.parametrize(
        "alarm_time",
        [
            (datetime.datetime(2020, 1, 3, 4, 10, 23)),
            (datetime.datetime(1999, 2, 1, 1, 12, 31)),
        ],
    )
    def test_dfo_only_entry_with_ag(
        self, tap_gen: TAPGenerator, alarm_time, default_config
    ):  # pylint: disable=no-self-use
        alarm = Alarm(
            alarm_type="Door Forced Open",
            raw_info={},
            **{
                "door_uuid": "d1",
                "video_start_time_utc": alarm_time
                - datetime.timedelta(seconds=10),
                "video_end_time_utc": alarm_time
                + datetime.timedelta(seconds=10),
                "alarm_uuid": "ra5",
                "alarm_time": alarm_time,
                "tenant_id": "test_tenant",
            },
        )

        person1 = Person()
        person1.movement_pattern = MovementTypes.ENTRY
        person1.entry_time = 4
        video_tags = Tags([person1], {"tags": ["entry"]})
        assert not tap_gen._handlers["unauth_access"](video_tags, alarm)[0]
        video_tags = tap_gen.get_video_tags(
            [person1],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
        )
        assert DFOHandler().handle_event(video_tags, default_config)[0] < 50  # pylint: disable=protected-access
        assert TagTypes.UNAUTHORIZED_ENTRY not in video_tags
        assert TagTypes.ENTRY in video_tags
        # TODO: Add AG raw alarm to test ACCESS_GRANTED tag

    def test_dfo_only_exit_without_ag(
        self, tap_gen: TAPGenerator, default_config
    ):
        alarm = Alarm(
            alarm_type="Door Forced Open",
            raw_info={},
            **{
                "door_uuid": "d1",
                "video_start_time_utc": datetime.datetime(
                    2020, 1, 3, 5, 10, 23
                )
                - datetime.timedelta(seconds=10),
                "video_end_time_utc": datetime.datetime(2020, 1, 3, 5, 10, 23)
                + datetime.timedelta(seconds=10),
                "alarm_uuid": "ra6",
                "alarm_time": datetime.datetime(2020, 1, 3, 5, 10, 23),
                "tenant_id": "test_tenant",
            },
        )

        person1 = Person()
        person1.movement_pattern = MovementTypes.EXIT
        person1.exit_time = 8
        video_tags = Tags([person1], {"tags": ["exit"]})
        assert not tap_gen._handlers["unauth_access"](video_tags, alarm)[0]
        video_tags = tap_gen.get_video_tags(
            [person1],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
        )
        assert DFOHandler().handle_event(video_tags, default_config)[0] < 50  # pylint: disable=protected-access
        assert TagTypes.UNAUTHORIZED_ENTRY not in video_tags
        assert TagTypes.ENTRY not in video_tags
        assert TagTypes.EXIT in video_tags

    def test_dfo_motion_no_people(self, tap_gen: TAPGenerator, default_config):  # pylint: disable=no-self-use
        alarm = Alarm(
            alarm_type="Door Forced Open",
            raw_info={},
            **{
                "door_uuid": "d1",
                "video_start_time_utc": datetime.datetime(
                    2020, 1, 3, 5, 10, 23
                )
                - datetime.timedelta(seconds=10),
                "video_end_time_utc": datetime.datetime(2020, 1, 3, 5, 10, 23)
                + datetime.timedelta(seconds=10),
                "alarm_uuid": "ra6",
                "alarm_time": datetime.datetime(2020, 1, 3, 5, 10, 23),
                "tenant_id": "test_tenant",
            },
        )

        video_tags = Tags([], {"tags": ["motion_detected"]})
        assert not tap_gen._handlers["unauth_access"](video_tags, alarm)[0]
        video_tags = tap_gen.get_video_tags(
            [],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
            motion=True,
        )
        assert DFOHandler().handle_event(video_tags)[0] > 50
        assert TagTypes.MOTION_DETECTED in video_tags
        assert TagTypes.NO_MOTION_DETECTED not in video_tags

    def test_dfo_no_motion_no_people(
        self, tap_gen: TAPGenerator, default_config
    ):  # pylint: disable=no-self-use
        alarm = Alarm(
            alarm_type="Door Forced Open",
            raw_info={},
            **{
                "door_uuid": "d1",
                "video_start_time_utc": datetime.datetime(
                    2020, 1, 3, 5, 10, 23
                )
                - datetime.timedelta(seconds=10),
                "video_end_time_utc": datetime.datetime(2020, 1, 3, 5, 10, 23)
                + datetime.timedelta(seconds=10),
                "alarm_uuid": "ra6",
                "alarm_time": datetime.datetime(2020, 1, 3, 5, 10, 23),
                "tenant_id": "test_tenant",
                "scene_info": SceneInfo(
                    camera_position=CameraPosition.UNSECURE
                ),
            },
        )

        video_tags = Tags([], {"tags": ["no_motion_detected"]})
        assert not tap_gen._handlers["unauth_access"](video_tags, alarm)[0]
        video_tags = tap_gen.get_video_tags(
            [],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
            motion=False,
        )
        assert DFOHandler().handle_event(video_tags, default_config)[0] < 50  # pylint: disable=protected-access
        assert TagTypes.MOTION_DETECTED not in video_tags
        assert TagTypes.NO_MOTION_DETECTED in video_tags
        alarm.scene_info = SceneInfo(camera_position=CameraPosition.SECURE)
        video_tags = tap_gen.get_video_tags(
            [],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
            motion=False,
        )
        assert DFOHandler().handle_event(video_tags, default_config)[0] > 50  # pylint: disable=protected-access
        assert TagTypes.MOTION_DETECTED not in video_tags
        assert TagTypes.NO_MOTION_DETECTED not in video_tags

    @pytest.mark.parametrize(
        "people, exist_unauthorized, tagged, not_tagged, tap_greater_50,config_type",
        [
            (
                [
                    Person(**{"movement_pattern": "ENTRY", "entry_time": 8}),
                ],
                True,
                [TagTypes.UNAUTHORIZED_ENTRY],
                [TagTypes.ENTRY],
                True,
                "default",
            ),
            (
                [Person(**{"movement_pattern": "EXIT", "exit_time": 10})],
                False,
                [TagTypes.EXIT],
                [TagTypes.UNAUTHORIZED_ENTRY],
                False,
                "default",
            ),
            ([], False, [], [], True, "default"),
            (
                [
                    Person(**{"movement_pattern": "EXIT", "exit_time": 10}),
                    Person(**{"movement_pattern": "ENTRY", "entry_time": 8}),
                ],
                True,
                [TagTypes.EXIT, TagTypes.UNAUTHORIZED_ENTRY],
                [],
                True,
                "default",
            ),
            (
                [
                    Person(**{"movement_pattern": "EXIT", "exit_time": 10}),
                    Person(**{"movement_pattern": "ENTRY", "entry_time": 8}),
                ],
                True,
                [TagTypes.EXIT, TagTypes.UNAUTHORIZED_ENTRY],
                [],
                True,
                "P3",
            ),
        ],
    )
    def test_dho(
        self,
        people,
        exist_unauthorized,
        tagged,
        not_tagged,
        tap_greater_50,
        config_type,
        tap_gen: TAPGenerator,
        default_config,
    ):
        default_config.priority = getattr(GroupPriority, config_type)
        # the parameters represent the number of people and their movement,
        # whether or not unauthorized entrants exist for the test params,
        # the tags that should be present,
        # the tags that should not be present,
        # whether or not the tap value should be greater than 50.
        alarm = Alarm(
            alarm_type="Door Held Open",
            raw_info={},
            alarm_time=datetime.datetime(1995, 1, 3, 4, 8, 23),
            **{
                "door_uuid": "d1",
                "video_start_time_utc": datetime.datetime(1995, 1, 3, 4, 8, 23)
                - datetime.timedelta(seconds=10),
                "video_end_time_utc": datetime.datetime(1995, 1, 3, 4, 8, 23)
                + datetime.timedelta(seconds=10),
                "alarm_uuid": "ra9",
                "tenant_id": "test_tenant",
            },
        )
        video_tags = Tags(people, {"tags": []})
        unauthorized_people = tap_gen._handlers["unauth_access"](
            video_tags, alarm
        )[0]
        if exist_unauthorized:
            assert len(unauthorized_people)
        else:
            assert not len(unauthorized_people)
        video_tags = tap_gen.get_video_tags(
            people,
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
        )
        for tag in tagged:
            assert tag in video_tags
        for tag in not_tagged:
            assert tag not in video_tags
        if tap_greater_50:
            assert (
                DHOHandler().handle_event(video_tags, default_config)[0] > 50
            )
        else:
            assert (
                DHOHandler().handle_event(video_tags, default_config)[0] <= 50
            )

    def test_invalid_access_level_entry(
        self, tap_gen: TAPGenerator, default_config
    ):  # pylint: disable=no-self-use
        alarm = Alarm(
            alarm_type="Invalid Access Level",
            raw_info={},
            **{
                "door_uuid": "d1",
                "video_start_time_utc": datetime.datetime(
                    2021, 1, 3, 5, 10, 23
                )
                - datetime.timedelta(seconds=10),
                "video_end_time_utc": datetime.datetime(2021, 1, 3, 5, 10, 23)
                + datetime.timedelta(seconds=10),
                "alarm_uuid": "ra6",
                "alarm_time": datetime.datetime(2021, 1, 3, 5, 10, 23),
                "tenant_id": "test_tenant",
            },
        )

        person1 = Person()
        person1.movement_pattern = MovementTypes.ENTRY
        person1.entry_time = 12
        person2 = Person()
        person2.movement_pattern = MovementTypes.UNKNOWN

        video_tags = tap_gen.get_video_tags(
            [person1, person2],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
        )
        assert (
            InvalidAccessLevelHandler().handle_event(
                video_tags, default_config
            )[0]
            > 50
        )  # pylint: disable=protected-access
        assert TagTypes.ENTRY not in video_tags
        assert TagTypes.UNAUTHORIZED_ENTRY in video_tags

    @pytest.mark.parametrize(
        "alarm,ag_tag",
        [
            (
                Alarm(
                    alarm_type="Invalid Badge",
                    raw_info={},
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 23
                        )
                        - datetime.timedelta(seconds=10),
                        "video_end_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 23
                        )
                        + datetime.timedelta(seconds=10),
                        "alarm_uuid": "ra7",
                        "alarm_time": datetime.datetime(2020, 1, 1, 1, 10, 23),
                        "tenant_id": "test_tenant",
                    },
                ),
                False,
            ),
            (
                Alarm(
                    alarm_type="Invalid Badge",
                    raw_info={},
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 23
                        )
                        - datetime.timedelta(seconds=10),
                        "video_end_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 23
                        )
                        + datetime.timedelta(seconds=10),
                        "alarm_uuid": "ra7",
                        "alarm_time": datetime.datetime(2020, 1, 1, 1, 10, 23),
                        "tenant_id": "test_tenant",
                        "employee_id": "1",
                    },
                ),
                True,
            ),
            (
                Alarm(
                    alarm_type="Invalid Badge",
                    raw_info={},
                    **{
                        "door_uuid": "d1",
                        "video_start_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 23
                        )
                        - datetime.timedelta(seconds=10),
                        "video_end_time_utc": datetime.datetime(
                            2020, 1, 1, 1, 10, 23
                        )
                        + datetime.timedelta(seconds=10),
                        "alarm_uuid": "ra7",
                        "alarm_time": datetime.datetime(2020, 1, 1, 1, 10, 23),
                        "tenant_id": "test_tenant",
                        "employee_id": "4",
                    },
                ),
                False,
            ),
        ],
    )
    def test_invalid_badge_ag(
        self, tap_gen: TAPGenerator, default_config, alarm, ag_tag
    ):  # pylint: disable=no-self-use
        video_tags = tap_gen.get_video_tags(
            [],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_config,
        )
        tap, tags = InvalidBadgeHandler().handle_event(
            video_tags, default_config
        )
        if ag_tag:
            assert tap < 50
            assert TagTypes.ACCESS_GRANTED in video_tags
        else:
            assert tap > 50
            assert TagTypes.ACCESS_GRANTED not in video_tags

    def test_corrupt_video(
        self, tap_gen: TAPGenerator, default_alarm_proc_config
    ):
        alarm = Alarm(
            alarm_type="Access Granted",
            raw_info={},
            **{
                "alarm_uuid": "ra688",
                "alarm_time": datetime.datetime(2021, 2, 14, 9, 58, 0),
                "tenant_id": "test_tenant",
                "door_uuid": "d1",
            },
        )
        assert (
            tap_gen.get_tap(
                [],
                alarm,
                {
                    "video_corrupt": True,
                    "video_check_result": VideoAnalyzerResult.VIDEO_NOT_REQUESTED,
                },
                default_alarm_proc_config,
            )[0]
            > 50
        )

    def test_comms_lost(
        self, tap_gen: TAPGenerator, default_alarm_proc_config
    ):  # pylint: disable=no-self-use
        alarm = Alarm(
            alarm_type="Communications Lost",
            raw_info={},
            **{
                "alarm_uuid": "ra6",
                "alarm_time": datetime.datetime(2021, 2, 14, 9, 58, 0),
                "tenant_id": "test_tenant",
                "door_uuid": "d1",
            },
        )
        assert (
            tap_gen.get_tap(
                [],
                alarm,
                {
                    "video_check_result": VideoAnalyzerResult.VIDEO_NOT_REQUESTED
                },
                default_alarm_proc_config,
            )[0]
            < 50
        )

        alarm = Alarm(
            alarm_type="Communications Lost",
            raw_info={},
            **{
                "alarm_uuid": "ra6",
                "alarm_time": datetime.datetime(2021, 2, 14, 9, 52, 0),
                "tenant_id": "test_tenant",
                "door_uuid": "d1",
            },
        )

        assert (
            tap_gen.get_tap(
                [],
                alarm,
                {
                    "video_check_result": VideoAnalyzerResult.VIDEO_NOT_REQUESTED
                },
                default_alarm_proc_config,
            )[0]
            > 50
        )

    @pytest.mark.parametrize(
        "alarm,is_restored",
        [
            (
                Alarm(
                    alarm_type="Granted Access - Pending Entry",
                    raw_info={},
                    **{
                        "alarm_uuid": "ra6",
                        "alarm_time": datetime.datetime(2021, 2, 14, 9, 58, 0),
                        "tenant_id": "test_tenant",
                        "door_uuid": "d1",
                    },
                ),
                True,
            ),
            (
                Alarm(
                    alarm_type="Granted Access - Pending Entry",
                    raw_info={},
                    **{
                        "alarm_uuid": "ra6",
                        "alarm_time": datetime.datetime(2021, 2, 13, 9, 58, 0),
                        "tenant_id": "test_tenant",
                        "door_uuid": "d1",
                    },
                ),
                True,
            ),
            (
                Alarm(
                    alarm_type="Granted Access - Pending Entry",
                    raw_info={},
                    **{
                        "alarm_uuid": "ra6",
                        "alarm_time": datetime.datetime(2021, 2, 14, 9, 52, 0),
                        "tenant_id": "test_tenant",
                        "door_uuid": "d1",
                    },
                ),
                False,
            ),
        ],
    )
    def test_granted_pending(
        self,
        tap_gen: TAPGenerator,
        alarm,
        is_restored,
        default_alarm_proc_config,
    ):  # pylint: disable=no-self-use
        tags = tap_gen.get_tap(
            [],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_alarm_proc_config,
        )[1]
        if is_restored:
            assert TagTypes.RESTORED_IMMEDIATELY in tags
        else:
            assert TagTypes.RESTORED_IMMEDIATELY not in tags

    def test_unknown_alarm(
        self, tap_gen: TAPGenerator, default_alarm_proc_config
    ):  # pylint: disable=no-self-use
        alarm = Alarm(
            alarm_type="Secret Unknown alarm",
            raw_info={},
            **{
                "alarm_uuid": "ra6",
                "alarm_time": datetime.datetime(2021, 1, 3, 5, 10, 23),
                "tenant_id": "test_tenant",
            },
        )

        assert (
            tap_gen.get_tap(
                [],
                alarm,
                {
                    "video_check_result": VideoAnalyzerResult.VIDEO_NOT_REQUESTED
                },
                default_alarm_proc_config,
            )[0]
            == 70
        )

    @pytest.mark.parametrize(
        "alarm",
        [
            Alarm(
                alarm_type="Door Held Open Masked",
                raw_info={},
                **{
                    "alarm_uuid": "ra_test_alarm",
                    "alarm_time": datetime.datetime(1996, 2, 14, 9, 58, 0),
                    "tenant_id": "test_tenant",
                    "door_uuid": "d1",
                },
            ),
            Alarm(
                alarm_type="Door Held Open Unmasked",
                raw_info={},
                **{
                    "alarm_uuid": "ra_test_alarm",
                    "alarm_time": datetime.datetime(1996, 2, 14, 9, 48, 0),
                    "tenant_id": "test_tenant",
                    "door_uuid": "d1",
                },
            ),
            Alarm(
                alarm_type="Door Forced Open Masked",
                raw_info={},
                **{
                    "alarm_uuid": "ra_test_alarm",
                    "alarm_time": datetime.datetime(1996, 2, 15, 9, 58, 0),
                    "tenant_id": "test_tenant",
                    "door_uuid": "d1",
                },
            ),
            Alarm(
                alarm_type="Door Forced Open Unmasked",
                raw_info={},
                **{
                    "alarm_uuid": "ra_test_alarm",
                    "alarm_time": datetime.datetime(1996, 2, 15, 9, 48, 0),
                    "tenant_id": "test_tenant",
                    "door_uuid": "d1",
                },
            ),
            Alarm(
                alarm_type="Door Close",
                raw_info={},
                **{
                    "alarm_uuid": "ra_test_alarm",
                    "alarm_time": datetime.datetime(1996, 2, 15, 9, 48, 0),
                    "tenant_id": "test_tenant",
                    "door_uuid": "d1",
                },
            ),
            Alarm(
                alarm_type="Reader Mode Unlocked",
                raw_info={},
                **{
                    "alarm_uuid": "ra_test_alarm_1",
                    "alarm_time": datetime.datetime(1996, 2, 15, 9, 48, 30),
                    "tenant_id": "test_tenant",
                    "door_uuid": "d1",
                },
            ),
            Alarm(
                alarm_type="Request To Exit - Door Used",
                raw_info={},
                **{
                    "alarm_uuid": "ra_test_alarm_1",
                    "alarm_time": datetime.datetime(1996, 2, 15, 9, 48, 30),
                    "tenant_id": "test_tenant",
                    "door_uuid": "d1",
                },
            ),
            Alarm(
                alarm_type="Request To Exit - Door Not Used",
                raw_info={},
                **{
                    "alarm_uuid": "ra_test_alarm_1",
                    "alarm_time": datetime.datetime(1996, 2, 15, 9, 48, 30),
                    "tenant_id": "test_tenant",
                    "door_uuid": "d1",
                },
            ),
        ],
    )
    def test_nuisance_resolve(self, tap_gen, alarm, default_alarm_proc_config):
        tap = tap_gen.get_tap(
            [],
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_alarm_proc_config,
        )[0]
        assert tap < 50

    @pytest.mark.parametrize(
        "alarm,tags,expected_tap, priority",
        [
            (
                Alarm(
                    alarm_type="Invalid Badge",
                    raw_info={},
                    **{
                        "alarm_uuid": "ra_test_alarm",
                        "alarm_time": datetime.datetime(1996, 2, 15, 9, 48, 0),
                        "tenant_id": "test_tenant",
                        "door_uuid": "d1",
                    },
                ),
                Tags([], {"tags": ["ENTRY"]}),
                20,
                GroupPriority.default,
            ),
            (
                Alarm(
                    alarm_type="Invalid Badge",
                    raw_info={},
                    **{
                        "alarm_uuid": "ra_test_alarm",
                        "alarm_time": datetime.datetime(1996, 2, 15, 9, 48, 0),
                        "tenant_id": "test_tenant",
                        "door_uuid": "d1",
                    },
                ),
                Tags([], {"tags": ["ACCESS_GRANTED"]}),
                45,
                GroupPriority.default,
            ),
            (
                Alarm(
                    alarm_type="Invalid Badge",
                    raw_info={},
                    **{
                        "alarm_uuid": "ra_test_alarm",
                        "alarm_time": datetime.datetime(1996, 2, 15, 9, 48, 0),
                        "tenant_id": "test_tenant",
                        "door_uuid": "d1",
                    },
                ),
                Tags([], {"tags": []}),
                70,
                GroupPriority.default,
            ),
            (
                Alarm(
                    alarm_type="Invalid Badge",
                    raw_info={},
                    **{
                        "alarm_uuid": "ra_test_alarm",
                        "alarm_time": datetime.datetime(1996, 2, 15, 9, 48, 0),
                        "tenant_id": "test_tenant",
                        "door_uuid": "d1",
                    },
                ),
                Tags([], {"tags": []}),
                80,
                GroupPriority.P0,
            ),
        ],
    )
    def test_invalid_family(
        self, tap_gen, alarm, tags, priority, expected_tap, default_config
    ):
        default_config.priority = priority
        assert (
            InvalidBadgeHandler().handle_event(tags, default_config)[0]
            == expected_tap
        )

    def test_unprocessed_alarm(self, tap_gen, default_alarm_proc_config):
        default_alarm_proc_config.processAlarmVideo = False
        alarm = Alarm(
            alarm_type="DOOR_HELD_OPEN",
            raw_info={},
            alarm_time=datetime.datetime(1995, 1, 3, 4, 8, 23),
            **{
                "door_uuid": "d1",
                "video_start_time_utc": datetime.datetime(1995, 1, 3, 4, 8, 23)
                - datetime.timedelta(seconds=10),
                "video_end_time_utc": datetime.datetime(1995, 1, 3, 4, 8, 23)
                + datetime.timedelta(seconds=10),
                "alarm_uuid": "ra9",
                "tenant_id": "test_tenant",
            },
            scene_info=SceneInfo(),
        )
        tap, tags = tap_gen.get_tap(
            [],
            alarm,
            {
                "video_check_result": VideoAnalyzerResult.ENOUGH,
                "local_video_path": "test_video.mp4",
            },
            default_alarm_proc_config,
        )
        assert tap == 80
        assert "DOOR_NOT_ONBOARDED" in tags
        default_alarm_proc_config.processAlarmVideo = True
        tap, tags = tap_gen.get_tap(
            [],
            alarm,
            {
                "video_check_result": VideoAnalyzerResult.ENOUGH,
                "local_video_path": "test_video.mp4",
            },
            default_alarm_proc_config,
        )
        assert tap == 80

    @patch("config.backend_config.HAIE.MOTION_TIME", 1)
    @patch("config.backend_config.HAIE.PROCESSING_FPS", 1)
    def test_get_video_tags(self, tap_gen, default_alarm_proc_config):
        alarm = Alarm(
            **{
                "alarm_uuid": "ra1",
                "door_uuid": "d1",
                "alarm_time": datetime.datetime(1979, 9, 27, 10, 0, 0),
                "video_start_time_utc": (
                    datetime.datetime(1979, 9, 27, 10, 0, 0)
                    - datetime.timedelta(seconds=20)
                ),
                "video_end_time_utc": (
                    datetime.datetime(1979, 9, 27, 10, 0, 0)
                    + datetime.timedelta(seconds=20)
                ),
                "alarm_type": "Access Granted",
                "tenant_id": "test_tenant",
            }
        )
        people = []
        person = Person(
            track=BaseTrack(
                list(range(4)),
                [BaseDetection.create("person", [0, 0, 0, 0])] * 4,
            )
        )
        person.movement_pattern = MovementTypes.UNKNOWN
        people.append(person)
        tags = tap_gen.get_video_tags(
            people,
            alarm,
            {"video_check_result": VideoAnalyzerResult.ENOUGH},
            default_alarm_proc_config,
        )
        assert set(tags.video_tag_names) == {
            "MOTION_DETECTED",
            "DOOR_NOT_ONBOARDED",
        }

    @pytest.mark.parametrize(
        "input_tag_types,kept,removed",
        [
            (
                [TagTypes.VIDEO_UNAVAILABLE, TagTypes.DOOR_NOT_ONBOARDED],
                [TagTypes.DOOR_NOT_ONBOARDED],
                [TagTypes.VIDEO_UNAVAILABLE],
            ),
        ],
    )
    def test_remove_conflicting_tags(
        self,
        tap_gen,
        input_tag_types: typing.List[TagTypes],
        kept: typing.List[TagTypes],
        removed: typing.List[TagTypes],
    ):
        tags = Tags([], {"tags": input_tag_types})
        processed_tags = TAPGenerator._remove_conflicting_tags(tags)
        for kept_tag_type in kept:
            assert kept_tag_type in processed_tags
        for removed_tag_type in removed:
            assert removed_tag_type not in processed_tags
        assert len(processed_tags) == len(kept)

    def test_custom_tap_is_used(
        self, tap_gen: TAPGenerator, custom_alarm_proc_config
    ):  # pylint: disable=no-self-use
        alarm = Alarm(
            alarm_type="Random alarm",
            raw_info={},
            **{
                "alarm_uuid": "ra6",
                "alarm_time": datetime.datetime(2021, 1, 3, 5, 10, 23),
                "tenant_id": "test_tenant",
            },
        )
        assert (
            tap_gen.get_tap(
                [],
                alarm,
                {
                    "video_check_result": VideoAnalyzerResult.VIDEO_NOT_REQUESTED
                },
                custom_alarm_proc_config,
            )[0]
            == 70
        )

    @pytest.mark.parametrize(
        "alarm,tag",
        [
            (
                Alarm(
                    alarm_type="Door Forced Open",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                30,
            ),
            (
                Alarm(
                    alarm_type="Door Held Open",
                    alarm_time=datetime.datetime(2020, 1, 1, 1, 10, 25),
                    tenant_id="test_tenant",
                ),
                25,
            ),
        ],
    )
    def test_if_set_of_alarm_is_used(
        self, alarm, tag, tap_gen: TAPGenerator, custom_alarm_proc_config
    ):  # pylint: disable=no-self-use
        assert (
            tap_gen.get_tap(
                [],
                alarm,
                {
                    "video_check_result": VideoAnalyzerResult.VIDEO_NOT_REQUESTED
                },
                custom_alarm_proc_config,
            )[0]
            == tag
        )
