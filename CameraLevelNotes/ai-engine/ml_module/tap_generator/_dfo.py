import typing

import structlog

from interfaces.group_priority import GroupPriority
from interfaces.tags import Tags, TagTypes

from ._utils import BaseTAPHandler

log = structlog.get_logger("hakimo", module="TAP Generator/DFO")


class DFOHandler(BaseTAPHandler):
    def __init__(self):
        super().__init__()
        self._mapper = {
            GroupPriority.default: self._handle_default,
            GroupPriority.P0: self._handle_p0,
            GroupPriority.ignore: self._handle_ignore,
        }

    @staticmethod
    def _handle_p0(tags: Tags) -> typing.Tuple[float, Tags]:
        log.info("using p0 handle for tap")
        return 90, tags

    @staticmethod
    def _handle_ignore(tags: Tags) -> typing.Tuple[float, Tags]:
        log.info("using ignore handle for tap")
        return 30, tags

    @staticmethod
    def _handle_default(tags: Tags) -> typing.Tuple[float, Tags]:
        true_alarm_probability = 70
        if TagTypes.UNAUTHORIZED_ENTRY in tags:
            true_alarm_probability = 95
        elif (
            TagTypes.LOITERING_UNSECURE_SIDE in tags
            and TagTypes.ACCESS_GRANTED not in tags
        ):
            true_alarm_probability = 90
        elif TagTypes.LOITERING_SECURE_SIDE in tags:
            true_alarm_probability = 40
        elif TagTypes.ENTRY in tags:
            true_alarm_probability = 25
        elif TagTypes.EXIT in tags:
            true_alarm_probability = 15
        elif TagTypes.ACCESS_GRANTED in tags:
            true_alarm_probability = 45
        elif TagTypes.MOTION_DETECTED in tags:
            true_alarm_probability = 70
        elif TagTypes.NO_MOTION_DETECTED in tags:
            true_alarm_probability = 20
        elif TagTypes.RESTORED_IMMEDIATELY in tags:
            true_alarm_probability = 15
        true_alarm_probability = max(15, true_alarm_probability)

        if (
            TagTypes.ALARM_ACTIVE_TOO_LONG in tags
            or TagTypes.MORE_VIDEO_UNAVAILABLE in tags
        ):
            # return default TAP on timeout
            return max(70, true_alarm_probability), tags
        return true_alarm_probability, tags
