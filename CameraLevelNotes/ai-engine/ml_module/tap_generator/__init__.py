# pylint: disable=import-outside-toplevel
"""Implementation for TAP Generator class and related methods.
Given the video tags and alarm type, will generate the TAP
"""

import datetime
import re
import typing

import structlog
from prometheus_client import Gauge

import controller as ctrl
from interfaces.alarm import ALARM_TYPE_MOTION, Alarm
from interfaces.alarm_mapper import AlarmMapper
from interfaces.camera_details import CameraPosition
from interfaces.entities.entity import Entity, EntryType, MovementTypes
from interfaces.group_priority import GroupPriority
from interfaces.scene_info import SceneInfo
from interfaces.tags import Tags, TagTypes
from interfaces.tenant_config import (
    AlarmProcessingConfig,
    TenantAlarmProcessingConfig,
)
from ml_module.tap_generator._unauthorized_entries.handler import (
    check_unauthorized_access_handler,
)
from ml_module.utils import is_door_associated_alarm, should_be_processed_by_ai
from ml_module.video_time_analyzer import VideoAnalyzerResult
from models_rds.rds_client import RDS<PERSON>lient

from ._ag import <PERSON><PERSON>and<PERSON>
from ._dfo import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ._dho import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ._event_pairs import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ._generic_alarm import <PERSON>ric<PERSON>larm<PERSON><PERSON><PERSON>
from ._invalid_badge_events import (
    InvalidAccessLevelHandler,
    InvalidBadgeHandler,
)
from ._motion_alarms import MotionHandler, get_motion_video_tags
from ._nuisance_alarms import NuisanceHandler
from ._unknown_alarms import UnknownAlarmHandler
from ._utils import (
    add_video_related_tags,
    check_ag_in_entry_window,
    check_invalid_badge_ag_clear,
    check_movement_in_videos,
)

log = structlog.get_logger("hakimo", module="TAP Generator")


class TAPGenerator:
    """Catch all class, that takes in a list of video tags and the alarm type
    and returns the TAP for this alarm
    """

    def __init__(self, alarm_mapper: AlarmMapper, rds_client: RDSClient):
        badge_handler = InvalidBadgeHandler()

        self._tagger_directory: typing.Dict[str, typing.Callable] = {
            "Access Granted": AGHandler.get_access_granted_tap,
            # No tailgating when open door command is issued
            "Open Door Command Issued": AGHandler.get_access_granted_tap_wo_tailgating,
            # S2 alias for open door command
            "Momentary unlock": AGHandler.get_access_granted_tap_wo_tailgating,
            "Door Forced Open": DFOHandler().handle_event,
            "Door Held Open": DHOHandler().handle_event,
            "Invalid Access Level": InvalidAccessLevelHandler().handle_event,
            "Invalid Facility Code": badge_handler.handle_event,
            "Invalid Badge": badge_handler.handle_event,
            "Invalid Card Format": badge_handler.handle_event,
            ALARM_TYPE_MOTION: MotionHandler.handle_motion,
        }
        self._alarm_mapper = alarm_mapper
        self.rds_client = rds_client
        self._controller = ctrl.ControllerMap(self.rds_client.db_adapter)
        self._handlers: typing.Dict[str, typing.Any] = {
            # Handlers that require state such as DB access
            "event_pairs": EventPairHandler(self.rds_client, self._controller),
            "unauth_access": check_unauthorized_access_handler(
                self.rds_client, self._controller, self._alarm_mapper
            ),
        }

    def get_resolution_function(
        self, alarm_type: str, processing_config: AlarmProcessingConfig
    ) -> typing.Callable:
        if alarm_type in self._tagger_directory:
            # If in the tagger directory, return the corresponding method
            return self._tagger_directory[alarm_type]
        event_pair_handler: EventPairHandler = self._handlers["event_pairs"]
        # Check tenant config for resolving alarm
        for pattern in processing_config.alarmSOP:
            if re.fullmatch(pattern, alarm_type, flags=re.IGNORECASE):
                if "resolvingAlarm" in processing_config.alarmSOP[pattern]:
                    return event_pair_handler.resolve_event_pair
        if alarm_type in event_pair_handler.EVENT_PAIRS:
            # If alarm is a pair alarm, (e.g. Comms Lost)
            # Resolve by checking if pair exists
            return event_pair_handler.resolve_event_pair
        for pattern in event_pair_handler.EVENT_PAIRS.values():
            if re.fullmatch(pattern, alarm_type, flags=re.IGNORECASE):
                # If alarm is a pair alarm (e.g. Comms lost restored),
                # resolve as a nuisance
                return NuisanceHandler.autoresolve_nuisance
        for re_pattern in NuisanceHandler.NUISANCE_ALARMS:
            # If alarm is a nuisance
            if re_pattern.match(alarm_type):
                return NuisanceHandler.autoresolve_nuisance
        # Get TAP for unknown cases
        return UnknownAlarmHandler.handle_unknown_alarm

    def get_tap(
        self,
        entities: typing.Sequence[Entity],
        alarm: Alarm,
        video_details: dict,
        alarm_processing_config: AlarmProcessingConfig,
        motion: typing.Optional[bool] = None,
    ) -> typing.Tuple[float, Tags]:
        """Simple router to obtain TAP based on alarm type, and video tags
        Args:
            entities ([list]): List of Person objects for this alarm
            alarm ([Alarm]): Alarm object for which TAP is being generated
                The alarm type can influence what the TAP should be
        Raises:
            ValueError: Raised if invalid alarm type is encountered
        Returns:
            [float]: TAP value for this alarm
        """
        if alarm.alarm_type == ALARM_TYPE_MOTION:
            video_tags = get_motion_video_tags(
                entities,
                video_details,
                alarm_processing_config,
            )
        else:
            video_tags = self.get_video_tags(
                entities,
                alarm,
                video_details,
                alarm_processing_config,
                motion,
            )
        if alarm_processing_config.enableForAllAlarmType:
            tap, video_tags = self.get_resolution_function(
                self._alarm_mapper.get_internal_alarm_type(alarm),
                alarm_processing_config,
            )(video_tags, alarm_processing_config)
            if TagTypes.VIDEO_CORRUPT in video_tags:
                # If alarm was restored immediately, don't change the tap
                if TagTypes.RESTORED_IMMEDIATELY not in video_tags:
                    tap = max(55, tap)
            log.info("Found TAP", tap=tap, tags=video_tags)
            return tap, video_tags
        # If processing is not for the all alarm type use the new implementation
        return self.get_tap_custom(
            alarm,
            alarm_processing_config,
            video_tags,
        )

    def get_video_tags(
        self,
        entities: typing.Sequence[Entity],
        alarm: Alarm,
        video_details: dict,
        processing_config: AlarmProcessingConfig,
        motion: typing.Optional[bool] = None,
    ) -> Tags:
        """Simple router to obtain tags based on alarm,
            and entities in the scene
        Args:
            entities ([list]): list of Entity objects, with movement patterns
                already populated.
            alarm ([Alarm]): Alarm object for which tags are being generated
                The alarm type can influence what the tag should be in certain cases

        Raises:
            ValueError: Raised if invalid alarm type is encountered

        Returns:
            [Tags]: Tags object that includes all tags for this video,
                along with the entities objects
        """
        tags = Tags(entities, {"tags": []})
        (
            _,
            _,
            ag_events,
        ) = self._handlers["unauth_access"](
            tags, alarm, processing_config.useEscortStatus
        )
        # Tags for this alarm, based on this alarm (entity movement, video quality etc)
        tags = self._get_processing_tags(
            tags, alarm, entities, video_details, processing_config, motion
        )
        # Tags that include alarm context (other alarms)
        tags = self._get_context_tags(
            alarm, tags, ag_events, processing_config
        )
        TAPGenerator._remove_conflicting_tags(tags)
        log.debug("Added tags", tags=tags)
        return tags

    def _get_context_tags(
        self,
        alarm: Alarm,
        tags: Tags,
        ag_events: typing.List[datetime.datetime],
        processing_config: AlarmProcessingConfig,
    ) -> Tags:
        internal_type = self._alarm_mapper.get_internal_alarm_type(alarm)
        if (
            ag_events
            and check_ag_in_entry_window(
                alarm.alarm_time,
                ag_events,
                alarm.process_people,
                alarm.process_vehicles,
            )
            and internal_type not in ["Access Granted", "Invalid Badge"]
        ):
            tags.add_video_tag("ACCESS_GRANTED", 100)
        if internal_type == "Invalid Badge" and check_invalid_badge_ag_clear(
            alarm, self._alarm_mapper, self._controller
        ):
            tags.add_video_tag("ACCESS_GRANTED", 100)

        restored_event_times = self._handlers[
            "event_pairs"
        ].check_restored_event(alarm, processing_config)
        if restored_event_times:
            tags.add_video_tag("RESTORED_IMMEDIATELY", 100)
        return tags

    def _get_processing_tags(
        self,
        tags: Tags,
        alarm: Alarm,
        entities: typing.Sequence[Entity],
        video_details: dict,
        processing_config: AlarmProcessingConfig,
        motion: typing.Optional[bool] = None,
    ):
        if (
            alarm.scene_info is None  # dcp does not exit
            or alarm.scene_info.door_bbox is None  # door isnt labelled
            or alarm.scene_info.camera_position == CameraPosition.UNKNOWN
        ) and is_door_associated_alarm(alarm):
            tags.add_video_tag("DOOR_NOT_ONBOARDED", 100)
        for entity in entities:
            if entity.entry_type == EntryType.UNAUTHORIZED:
                tags.add_video_tag("UNAUTHORIZED_ENTRY", 100)
            elif entity.entry_type == EntryType.ESCORTED:
                tags.add_video_tag("ESCORTED_ENTRY", 100)
            elif entity.movement_pattern != MovementTypes.UNKNOWN:
                tags.add_video_tag(entity.movement_pattern.name, 100)

        if check_movement_in_videos(entities):
            tags.add_video_tag("MOTION_DETECTED", 100)

        if len(entities) == 0 and motion is not None:
            if motion:
                tags.add_video_tag("MOTION_DETECTED", 100)
            elif (
                alarm.scene_info is not None
                and alarm.scene_info.camera_position == CameraPosition.UNSECURE
            ):
                # only add no motion tag if there are
                # no people AND no motion was detected
                # AND camera is on unsecure side
                tags.add_video_tag("NO_MOTION_DETECTED", 100)

        # Add video related tags to only alarm that are associated with a door
        # Alarms like Server disconnected/Camera Defective need not have video related tags
        if is_door_associated_alarm(alarm):
            tags = add_video_related_tags(tags, video_details)

        if not should_be_processed_by_ai(
            processing_config,
            video_details,
            alarm,
            self._alarm_mapper.get_internal_alarm_type(alarm),
        ):
            # ugly code due to Optional[SceneInfo] for mypy
            if alarm.scene_info is not None:
                scene_info: SceneInfo = alarm.scene_info
                if not (  # for these two conditions DOOR_NOT_ONBOARDED tag is better
                    scene_info.door_bbox is None
                    or scene_info.camera_position == CameraPosition.UNKNOWN
                ):
                    tags.add_video_tag("NOT_PROCESSED_BY_AI", 100)
        return tags

    @staticmethod
    def _remove_conflicting_tags(tags: Tags) -> Tags:
        if (
            TagTypes.VIDEO_UNAVAILABLE in tags
            and TagTypes.DOOR_NOT_ONBOARDED in tags
        ):
            tags.remove_video_tag(TagTypes.VIDEO_UNAVAILABLE)
        return tags

    def get_tap_custom(
        self,
        alarm: Alarm,
        alarm_processing_config: AlarmProcessingConfig,
        video_tags: Tags,
    ) -> typing.Tuple[float, Tags]:
        """
        Get the alarms for which we need to use this door filtering;
        Apply the filter as per the rule on those alarms;
        Alarms which are not there then for them groupconfig will change to default;
        """
        log.info("inside tap custom function")
        alarm_type = self._alarm_mapper.get_internal_alarm_type(alarm)
        alarm_type_list = alarm_processing_config.alarm_types
        if alarm_type_list is not None and alarm_type in alarm_type_list:
            log.info(
                "finding out the tap using custom resolution function",
                alarm_type=alarm_type,
                alarm_type_list=alarm_type_list,
            )
            tap, video_tags = self.get_custom_resolution_function(
                alarm_type,
                alarm_processing_config,
            )(video_tags, alarm_processing_config)
            if TagTypes.VIDEO_CORRUPT in video_tags:
                tap = max(55, tap)
            log.info(
                "Found TAP for alarm type",
                alarm_type=alarm_type,
                tap=tap,
                tags=video_tags,
            )
            return tap, video_tags
        stored_priority = alarm_processing_config.priority
        # setting priority to default
        alarm_processing_config.priority = GroupPriority.default
        log.info(
            "Finding out TAP for alarm type not in list",
            alarm_type=alarm_type,
            alarm_type_list=alarm_type_list,
        )
        tap, video_tags = self.get_resolution_function(
            self._alarm_mapper.get_internal_alarm_type(alarm),
            alarm_processing_config,
        )(video_tags, alarm_processing_config)
        # resetting back to actual priority
        alarm_processing_config.priority = stored_priority
        if TagTypes.VIDEO_CORRUPT in video_tags:
            tap = max(55, tap)
        log.info("Found TAP using default handler", tap=tap, tags=video_tags)
        return tap, video_tags

    def get_custom_resolution_function(
        self, alarm_type: str, processing_config: AlarmProcessingConfig
    ) -> typing.Callable:
        if alarm_type in self._tagger_directory:
            # If in the tagger directory, return the corresponding method
            return self._tagger_directory[alarm_type]
        event_pair_handler: EventPairHandler = self._handlers["event_pairs"]
        # Check tenant config for resolving alarm
        config_priority = processing_config.priority
        for pattern in processing_config.alarmSOP:
            if config_priority == GroupPriority.ignore:
                return event_pair_handler.handle_ignore
            if config_priority == GroupPriority.P0:
                return event_pair_handler.handle_p0
            if re.fullmatch(pattern, alarm_type, flags=re.IGNORECASE):
                if "resolvingAlarm" in processing_config.alarmSOP[pattern]:
                    return event_pair_handler.resolve_event_pair
        if alarm_type in event_pair_handler.EVENT_PAIRS:
            # If alarm is a pair alarm, (e.g. Comms Lost)
            # Resolve by checking if pair exists
            if config_priority == GroupPriority.ignore:
                return event_pair_handler.handle_ignore
            if config_priority == GroupPriority.P0:
                return event_pair_handler.handle_p0
            return event_pair_handler.resolve_event_pair
        for pattern in event_pair_handler.EVENT_PAIRS.values():
            if re.fullmatch(pattern, alarm_type, flags=re.IGNORECASE):
                # If alarm is a pair alarm (e.g. Comms lost restored),
                # resolve as a nuisance
                return NuisanceHandler.autoresolve_nuisance
        for re_pattern in NuisanceHandler.NUISANCE_ALARMS:
            # If alarm is a nuisance
            if re_pattern.match(alarm_type):
                return NuisanceHandler.autoresolve_nuisance
        # Get TAP for other alarms if they are not from the above
        generic_handler = GenericAlarmHandler().handle_event
        return generic_handler
