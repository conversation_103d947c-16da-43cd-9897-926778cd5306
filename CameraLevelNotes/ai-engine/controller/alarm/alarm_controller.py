import dataclasses
import datetime
import time
import typing
import uuid
from datetime import datetime as dt
from typing import Iterable, Optional

import opentracing
import structlog
from opentracing.propagation import Format
from sqlalchemy import func, select, update

import errors
import interfaces.alarm as alarm_module
from common_utils.bridge.msg_sender import Msg<PERSON><PERSON>
from common_utils.metrics_definitions import (
    ALARM_STATE_PROM,
    ALARM_STATUS_CHANGE_COUNTER,
    MYSQL_READ_SUMMARY,
    MYSQL_WRITE_SUMMARY,
    TALKDOWN_EVENT_LATENCY,
)
from common_utils.redis.redis_consumer_utils import RedisConsumerUtils
from config import backend_config as config
from controller.alarm_types.alarm_types_controller import AlarmTypesController
from controller.interface import ControllerBase
from database.db_adapter import DBAdapter
from interfaces.alarm import ALARM_TYPE_MOTION
from interfaces.alarm_state import AlarmState
from interfaces.source_systems import ACSSource
from interfaces.tags import Tags
from interfaces.tenant_config import AlarmProcessingConfig
from interfaces.video_request import VideoRequest
from models_rds.alarm_updates import AlarmUpdates
from models_rds.ml_outputs import MLOutputs
from models_rds.raw_alarms import (
    RawAlarms,
    compute_partition_key,
    get_default_partition_key_filter,
)
from models_rds.video_tags import VideoTags

from .alarm_fetcher import FETCHED_ALARMS, FETCHED_SINGLE_ALARM, AlarmFetcher
from .alarm_filters import AlarmFilters
from .alarm_processing_config import AlarmConfigHandler

log = structlog.get_logger("hakimo", module="Alarm Controller")


@dataclasses.dataclass
class AlarmProcessingCriteria:
    """Criteria to get an alarm to process"""

    # if with_video = True, only alarms with video are processed
    # if with_video = False, only alarms without video are processed
    # if with_video is None, any alarm can be processed
    with_video: typing.Optional[bool] = None
    per_tenant_processor: bool = False
    processor_tenants: typing.List[str] = dataclasses.field(
        default_factory=list
    )
    enterprise_processor: typing.Optional[bool] = None
    queue_processor: typing.Optional[bool] = None


class AlarmController(ControllerBase):
    def __init__(
        self, db: DBAdapter, read_db: typing.Optional[DBAdapter] = None
    ):
        super().__init__(db, read_db=read_db)
        self.resolver_user_id = config.HAIE.HAKIMO_USER_ID
        self._fetcher = AlarmFetcher(db, read_db=read_db)
        self._config_handler = AlarmConfigHandler(db, read_db=read_db)
        _m_sender = MsgSender(db)
        self._msg_sender = _m_sender
        self._redis_consumer_utils = RedisConsumerUtils()

    def update_alarm(
        self, u: AlarmUpdates, now=None, field_updates=None, partition_key=None
    ):
        """Updates the alarm records in the database based on the data provided

        Args:
            u (AlarmUpdates): [Object containing all alarm updates to be applied]
            now ([type], optional): [timestamp of latest update]. Defaults to None.
        """
        if not now:
            now = dt.utcnow()

        u.uuid = str(uuid.uuid4())
        u.updated_at_utc = now
        u.created_at_utc = now

        if field_updates is not None:
            updates = field_updates
        else:
            updates = {}
        with self.db.get_session() as sess:
            start = time.time()
            sess.add(u)
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_WRITE_SUMMARY.labels(
                tenant=u.tenant_id,
                query="update_alarm",
                table_name="alarm_updates",
            ).observe(time_taken_ms)
            start = time.time()
            alarm = (
                sess.execute(
                    select(RawAlarms)
                    .filter(RawAlarms.uuid == u.alarm_id)
                    .filter(
                        RawAlarms.partition_key == partition_key
                        if partition_key
                        else get_default_partition_key_filter()
                    )
                )
                .scalars()
                .first()
            )
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_READ_SUMMARY.labels(
                tenant=u.tenant_id,
                query="get_raw_alarms_by_alarm_id",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)
            if u.current_status:
                updates["current_status"] = u.current_status
                updates["alarm_update_id"] = u.uuid

                if (
                    # Only update resolution timestamp if it has not already been set
                    not alarm.resolution_timestamp_utc
                    and u.update_timestamp_utc
                    and (
                        u.current_status in alarm_module.RESOLVED_STATUSES
                        or u.current_status
                        == alarm_module.ACKNOWLEDGED_VIA_ACS
                    )
                ):
                    updates["resolution_timestamp_utc"] = (
                        u.update_timestamp_utc
                    )

            # Only if a user is resolving the alarm, assign user to the alarm

            old_status = alarm.current_status
            old_user_id = alarm.user_id
            new_status = u.current_status
            new_user_id = u.user_id
            updates["user_id"] = self._resolve_user_id_update(
                old_status, old_user_id, new_status, new_user_id
            )
            updates["partition_key"] = partition_key
            start = time.time()
            self._update_raw_alarm(sess, u.alarm_id, **updates)
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_WRITE_SUMMARY.labels(
                tenant=u.tenant_id,
                query="update_raw_alarms_v2",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)
            if u.event == alarm_module.ALARM_UPDATES_SPEAKER_TALKDOWN_EVENT:
                event_timestamp = alarm.alarm_timestamp_utc
                if alarm.last_motion_event_timestamp_utc:
                    event_timestamp = alarm.last_motion_event_timestamp_utc

                talkdown_type = (
                    "automated"
                    if u.user_id == config.HAIE.HAKIMO_USER_ID
                    else "manual"
                )
                TALKDOWN_EVENT_LATENCY.labels(
                    tenant=u.tenant_id,
                    talkdown_type=talkdown_type,
                ).observe(
                    (u.update_timestamp_utc - event_timestamp).total_seconds()
                )

            if new_status and old_status != new_status:
                ALARM_STATUS_CHANGE_COUNTER.labels(
                    tenant=u.tenant_id,
                    old_status=old_status,
                    new_status=new_status,
                ).inc()
            log.debug(
                "Alarm updated",
                comment=u.plain_text_comment,
                current_status=u.current_status,
            )

            return u.uuid

    def resolve_alarm(
        self,
        alarm_id: str,
        tenant_id: str,
        forbidden_statuses: typing.Optional[typing.List[str]] = None,
        partition_key: int = None,
    ) -> None:
        forbidden_statuses = forbidden_statuses or []
        with self.db.get_session() as sess:
            alarm: RawAlarms = (
                sess.execute(
                    select(RawAlarms)
                    .filter(RawAlarms.uuid == alarm_id)
                    .filter(
                        RawAlarms.partition_key == partition_key
                        if partition_key
                        else get_default_partition_key_filter()
                    )
                )
                .scalars()
                .first()
            )
            if alarm.current_status in forbidden_statuses:
                return
        now = dt.utcnow()
        u = AlarmUpdates(
            alarm_id=alarm_id,
            event=alarm_module.ALARM_UPDATES_RESOLVED_HAKIMO_EVENT,
            current_status=alarm_module.RESOLVED_BY_HAKIMO_STATUS,
            user_id=self.resolver_user_id,
            tenant_id=tenant_id,
            update_timestamp_utc=now,
        )
        self.update_alarm(
            u,
            now=now,
            field_updates={"sync_version": 1},
            partition_key=partition_key,
        )

    def mark_alarm_pending(
        self,
        new_alarm: alarm_module.Alarm,
        forbidden_statuses: typing.Optional[typing.List[str]] = None,
        partition_key: int = None,
    ) -> None:
        forbidden_statuses = forbidden_statuses or []
        # If alarm is already in a forbidden status,
        # do not mark as pending
        with self.db.get_session() as sess:
            query = select(RawAlarms).filter(
                RawAlarms.uuid == new_alarm.alarm_uuid
            )

            query = query.filter(
                RawAlarms.partition_key == partition_key
                if partition_key
                else get_default_partition_key_filter()
            )
            alarm: RawAlarms = sess.execute(query).scalars().first()
            if alarm.current_status in forbidden_statuses:
                return
        now = dt.utcnow()
        u = AlarmUpdates(
            alarm_id=new_alarm.alarm_uuid,
            event=alarm_module.ALARM_UPDATES_PENDING_EVENT,
            current_status=alarm_module.PENDING_STATUS,
            user_id=self.resolver_user_id,
            tenant_id=new_alarm.tenant_id,
            update_timestamp_utc=now,
        )
        self.update_alarm(u, now=now, partition_key=partition_key)

    def unset_alarm_sync_version(
        self,
        alarm_id: str,
    ):
        with self.db.get_session() as sess:
            self._update_raw_alarm(
                sess,
                alarm_id,
                sync_version=0,
            )

    def update_tap(
        self,
        alarm_id: str,
        tenant_id: str,
        tap: float,
        new_ml_outputs: bool = True,
        partition_key: int = None,
    ) -> typing.Optional[str]:
        if tap is None:
            # Require tap to generate meaningful ML output
            return None
        if new_ml_outputs:
            ml_uuid = str(uuid.uuid4())
            now = dt.utcnow()
            ml_outputs = MLOutputs(
                uuid=ml_uuid,
                alarm_id=alarm_id,
                ml_output_timestamp_utc=now,
                true_alarm_probability=tap,
                haie_ml_version=alarm_module.ML_OUTPUTS_HAIE_ML_VERSION,
                tenant_id=tenant_id,
            )
        with self.db.get_session() as sess:
            if new_ml_outputs:
                sess.add(ml_outputs)
                self._update_raw_alarm(
                    sess,
                    alarm_id,
                    true_alarm_probability=tap,
                    ml_output_id=ml_uuid,
                    sync_version=1,
                    partition_key=partition_key,
                )
                return ml_uuid
            else:
                self._update_raw_alarm(
                    sess,
                    alarm_id,
                    true_alarm_probability=tap,
                    sync_version=1,
                    partition_key=partition_key,
                )
                return None

    def add_comment(self, alarm_id: str, tenant_id: str, comment_str: str):
        """Given the alarm ID, tenant ID and a comment string, add a comment
        onto that alarm
        """
        now = dt.utcnow()
        u = AlarmUpdates(
            alarm_id=alarm_id,
            event=alarm_module.ALARM_UPDATES_ADDED_COMMENT_EVENT,
            user_id=self.resolver_user_id,
            tenant_id=tenant_id,
            plain_text_comment=comment_str,
            update_timestamp_utc=now,
        )
        self.update_alarm(u, now=now)

    @staticmethod
    def _update_raw_alarm(sess, alarm_id, **kv):
        partition_key = None
        if "partition_key" in kv:
            partition_key = kv.pop("partition_key")
        alarm = (
            sess.execute(
                select(RawAlarms)
                .filter(RawAlarms.uuid == alarm_id)
                .filter(
                    RawAlarms.partition_key == partition_key
                    if partition_key
                    else get_default_partition_key_filter()
                )
            )
            .scalars()
            .first()
        )

        # Update the attributes
        for key, value in kv.items():
            setattr(alarm, key, value)

    def by_id(
        self,
        alarm_uuid: str,
        tenant_id: typing.Optional[str] = None,
        partition_key: int = None,
    ) -> RawAlarms:
        # TODO: Remove this once UI bug is fixed
        log.info("Called with partition key", partition_key=partition_key)
        if partition_key is not None:
            partition_key = int(partition_key)
            if partition_key > compute_partition_key():
                partition_key = compute_partition_key()
        log.info("Using partition key", partition_key=partition_key)
        with self.db.get_session() as sess:
            start = time.time()
            query = select(RawAlarms).filter(RawAlarms.uuid == alarm_uuid)
            if partition_key is not None:
                query = query.filter(RawAlarms.partition_key == partition_key)
            else:
                query = query.filter(get_default_partition_key_filter())
            if isinstance(tenant_id, str):
                query = query.filter(RawAlarms.tenant_id == tenant_id)
            a = sess.execute(query).scalars().first()
            sess.expunge_all()
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_READ_SUMMARY.labels(
                tenant=tenant_id,
                query="get_raw_alarms_by_id",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)
            return a

    def by_ids(
        self,
        alarm_uuids: typing.List[str],
        tenant_id: typing.Optional[str] = None,
        timestamp: typing.Optional[datetime.datetime] = None,
    ) -> typing.List[RawAlarms]:
        all_alarms = []
        BATCH_SIZE = 1000
        for i in range(0, len(alarm_uuids), BATCH_SIZE):
            with self.db.get_session() as sess:
                query = select(RawAlarms).filter(
                    RawAlarms.uuid.in_(alarm_uuids[i : i + BATCH_SIZE])
                )
                query = query.filter(
                    get_default_partition_key_filter(created_at_utc=timestamp)
                )
                if isinstance(tenant_id, str):
                    query = query.filter(RawAlarms.tenant_id == tenant_id)
                alarms = sess.execute(query).scalars().all()
                sess.expunge_all()
                all_alarms.extend(alarms)
        return all_alarms

    def fetch_alarm(
        self, alarm_id: str, partition_key: int = None
    ) -> FETCHED_SINGLE_ALARM:
        return self._fetcher.fetch_alarm(alarm_id, partition_key=partition_key)

    # pylint: disable=no-member
    def fetch_alarms(
        self,
        tenant_ids: typing.Optional[typing.Sequence[str]],
        filters: AlarmFilters,
        offset: typing.Optional[int] = 0,
        limit: typing.Optional[int] = 100,
        msp_locations: typing.Optional[typing.Sequence[int]] = None,
    ) -> typing.Sequence[FETCHED_ALARMS]:
        return self._fetcher.fetch_alarms(
            tenant_ids, filters, offset, limit, msp_locations
        )

    def fetch_latest_video_tags(
        self, alarms: typing.Sequence[RawAlarms]
    ) -> typing.Sequence[typing.Tuple[str, str]]:
        """Returns a list of tuples, each containing the video tag name
        and the alarm ID it's associated to, for the ml_output_id pointed
        to by the alarm
        """
        _db = self.read_db if self.read_db is not None else self.db
        with _db.get_session() as sess:
            tags = sess.execute(
                select(VideoTags.video_tag, MLOutputs.alarm_id)
                .join(MLOutputs)
                .filter(MLOutputs.alarm_id.in_([i.uuid for i in alarms]))
                .filter(MLOutputs.uuid.in_([i.ml_output_id for i in alarms]))
            ).all()
            return tags

    def transition_alarm(
        self,
        alarm_id: str,
        tenant_id: str,
        new_state: AlarmState,
        alarm: typing.Optional[RawAlarms] = None,
        old_state_check: typing.Optional[AlarmState] = None,
        partition_key: int = None,
    ) -> bool:
        """Transitions an alarm from its current state to its new state. Does not enforce
        any rules for transitions - caller is responsible to make sure transition is valid.
        If old_state is provided, the transition will only occur if alarm is in the old_state.
        Returns a boolean indicating if alarm was transitioned
        Args:
            alarm_id (str): alarm uuid
            tenant_id (str): tenant uuid
            new_state (str): new state to transition the alarm into
        """
        new_state_name = new_state.name
        cur_time = datetime.datetime.now()
        if alarm is None:
            with self.db.get_session() as session:
                alarm = (
                    session.execute(
                        select(RawAlarms)
                        .filter(RawAlarms.uuid == alarm_id)
                        .filter(RawAlarms.tenant_id == tenant_id)
                        .filter(
                            RawAlarms.partition_key == partition_key
                            if partition_key
                            else get_default_partition_key_filter()
                        )
                        .with_for_update()
                    )
                    .scalars()
                    .first()
                )
                old_state = alarm.alarm_state
                if old_state_check and old_state != old_state_check.name:
                    log.warning(
                        "Error transitioning alarm",
                        old_state=old_state,
                        expected_old_state=old_state_check.name,
                        new_state=new_state_name,
                        alarm_id=alarm.uuid,
                    )
                    return False
                old_state_change_time = alarm.last_state_change_time
                self._update_raw_alarm(
                    session,
                    alarm_id,
                    alarm_state=new_state_name,
                    last_state_change_time=cur_time,
                    partition_key=partition_key,
                )
        else:
            old_state = alarm.alarm_state
            if old_state_check and old_state != old_state_check.name:
                log.warning(
                    "Error transitioning alarm",
                    old_state=old_state,
                    expected_old_state=old_state_check.name,
                    new_state=new_state_name,
                    alarm_id=alarm.uuid,
                )
                return False
            old_state_change_time = alarm.last_state_change_time
            alarm.alarm_state = new_state_name
            alarm.last_state_change_time = cur_time
        transition_time = (cur_time - old_state_change_time).total_seconds()
        ALARM_STATE_PROM.labels(
            tenant_id=tenant_id, alarm_state=old_state
        ).observe(transition_time)
        log.info(
            "Updated alarm state",
            old_state=old_state,
            new_state=new_state_name,
            transition_time=transition_time,
        )
        return True

    def get_alarm_counts(
        self, state: AlarmState, partition_key: int = None
    ) -> dict:
        """Returns a dictionary of counts, with key=alarmstate, and value=count

        Returns:
            dict: dictionary with counts
        """
        with self.db.get_session() as sess:
            start = time.time()
            count = sess.execute(
                select(func.count(RawAlarms.uuid))
                .filter(RawAlarms.alarm_state == state.name)
                .filter(
                    RawAlarms.partition_key == partition_key
                    if partition_key
                    else get_default_partition_key_filter()
                )
            ).scalar_one()
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_READ_SUMMARY.labels(
                tenant="",
                query="get_alarm_counts",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)
            log.debug(
                "Time taken to get count of alarm", time_taken=time_taken_ms
            )
        return count

    def get_alarm_counts_group_by_tenant(
        self, state: AlarmState, partition_key: int = None
    ) -> typing.Dict[str, typing.Dict[str, int]]:
        """Returns a dictionary of alarm counts grouped by tenant for a given alarm state.

        Args:
            state (AlarmState): The alarm state to filter by.

        Returns:
            dict: A dictionary where the keys are tenant id and the values are the counts of alarms
            in the given state for that tenant.
        """
        with self.read_db.get_session() as sess:
            start = time.time()
            tenant_count = sess.execute(
                select(RawAlarms.tenant_id, func.count(RawAlarms.uuid))
                .filter(RawAlarms.alarm_state == state.name)
                .filter(
                    RawAlarms.partition_key == partition_key
                    if partition_key
                    else get_default_partition_key_filter()
                )
                .group_by(RawAlarms.tenant_id)
            ).fetchall()
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_READ_SUMMARY.labels(
                tenant="",
                query="get_alarm_counts_group_by_tenant",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)
            log.debug(
                "Time taken to get count of alarm", time_taken=time_taken_ms
            )
        return dict(tenant_count)

    def get_alarm_to_process(
        self,
        alarm_criteria: AlarmProcessingCriteria,
        is_queue_based_polling_active=False,
        queue_based_polling_tenants=[],
        partition_key: int = None,
    ) -> typing.Tuple[typing.Optional[str], typing.Optional[str], bool]:
        """Get an alarm to be processed, and atomically transition it to in_progress

        Returns:
            [str]: alarm ID to be processed
        """
        log.debug("Getting alarm to process")
        with self.db.get_session() as sess:
            query = select(RawAlarms).filter(
                RawAlarms.alarm_state == AlarmState.UNPROCESSED.name
            )
            query = query.filter(
                RawAlarms.partition_key == partition_key
                if partition_key
                else get_default_partition_key_filter()
            )
            """
            1. We apply the tenant_processor and enterprise_processor filter to only with_video processors
            2. no_video processors process all tenants 
            """
            p_tenants = alarm_criteria.processor_tenants
            add_no_vid_filter = True
            motion_processor = False
            if alarm_criteria.with_video is not None:
                if alarm_criteria.with_video:
                    query = query.filter(RawAlarms.video_path.isnot(None))
                    if alarm_criteria.per_tenant_processor:
                        assert len(p_tenants) > 0
                        query = query.filter(
                            RawAlarms.tenant_id.in_(p_tenants)
                        )
                        add_no_vid_filter = False
                    elif alarm_criteria.enterprise_processor:
                        query = query.filter(
                            RawAlarms.tenant_id.notin_(p_tenants)
                        ).filter(
                            RawAlarms.source_system != ACSSource.MOTION.name
                        )
                        add_no_vid_filter = False
                    else:
                        if (
                            is_queue_based_polling_active
                            and len(queue_based_polling_tenants) > 0
                        ):
                            p_tenants += queue_based_polling_tenants
                        query = query.filter(
                            RawAlarms.tenant_id.notin_(p_tenants)
                        ).filter(
                            RawAlarms.source_system == ACSSource.MOTION.name
                        )
                        add_no_vid_filter = False
                        motion_processor = True
            if add_no_vid_filter:
                query = query.filter(RawAlarms.video_path.is_(None))
            query = query.order_by(RawAlarms.alarm_timestamp_utc.desc()).limit(
                1
            )
            query = query.with_for_update(skip_locked=True)
            start = time.time()
            alarm: typing.Optional[RawAlarms] = (
                sess.execute(query).scalars().first()
            )
            time_taken_ms = round((time.time() - start) * 1000)
            if alarm is None:
                return None, None, False
            log.debug(
                "Time taken to get alarm",
                method="get_alarm_to_process",
                time_taken=time_taken_ms,
            )
            MYSQL_READ_SUMMARY.labels(
                tenant="",
                query="get_alarm_to_process",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)

            # Since the order by query has been removed,
            # we compare the times and move the alarm to processed if the difference is more than a threshold
            if motion_processor:
                alarm_time_diff = int(time.time()) - int(
                    alarm.alarm_timestamp_utc.timestamp()
                )
                if alarm_time_diff > config.HAIE.OLD_MOTION_ALARM_SKIP:
                    log.info(
                        "Motion Alarm is more than an hour old, skipping processing...",
                        alarm_id=alarm.uuid,
                        tenant_id=alarm.tenant_id,
                    )
                    self.transition_alarm(
                        alarm.uuid,
                        alarm.tenant_id,
                        AlarmState.PROCESSED,
                        alarm,
                        partition_key=partition_key,
                    )
                    return None, None, True

            self.transition_alarm(
                alarm.uuid,
                alarm.tenant_id,
                AlarmState.IN_PROGRESS,
                alarm,
                partition_key=partition_key,
            )
            alarm_id = alarm.uuid
            tenant_id = alarm.tenant_id
        log.info(
            "Updated alarm state",
            old_state=AlarmState.UNPROCESSED.name,
            new_state=AlarmState.IN_PROGRESS.name,
            alarm_id=alarm_id,
            tenant_id=tenant_id,
        )
        return alarm_id, tenant_id, False

    def alarm_id_by_source_id(
        self,
        source_id: str,
        tenant_id: str,
        reference_timestamp: typing.Optional[datetime.datetime] = None,
        time_delta: int = 0,
        source_system: typing.Optional[str] = None,
        partition_key: typing.Optional[int] = None,
    ) -> typing.Optional[str]:
        """
        This function returns the alarm uuid from the db, given the source_id
        (which is supposed to be unique for all alarms, generated from raw alarm info)
        """

        with self.db.get_session() as sess:
            query = (
                select(
                    RawAlarms.uuid,
                )
                .filter(RawAlarms.tenant_id == tenant_id)
                .filter(RawAlarms.source_id == source_id)
            )
            if reference_timestamp:
                delta_ts = datetime.timedelta(seconds=time_delta)
                start_time = reference_timestamp - delta_ts
                end_time = reference_timestamp + delta_ts
                query = query.filter(
                    RawAlarms.alarm_timestamp_utc.between(start_time, end_time)
                )
            if partition_key:
                query = query.filter(RawAlarms.partition_key == partition_key)
            elif reference_timestamp:
                query = query.filter(
                    get_default_partition_key_filter(
                        start_at_utc=start_time, end_at_utc=end_time
                    )
                )
            else:
                query = query.filter(get_default_partition_key_filter())
            if source_system:
                query = query.filter(RawAlarms.source_system == source_system)
            alarm_id = sess.execute(query).first()
        return alarm_id[0] if alarm_id is not None else None

    def alarm_by_source_id(
        self,
        source_id: str,
        tenant_id: str,
    ) -> typing.Optional[RawAlarms]:
        """
        This function returns the alarm record from the db, given the source_id
        (which is supposed to be unique for all alarms, generated from raw alarm info)
        """
        with self.db.get_session() as sess:
            a = (
                sess.execute(
                    select(
                        RawAlarms,
                    )
                    .filter(RawAlarms.tenant_id == tenant_id)
                    .filter(RawAlarms.source_id == source_id)
                    .filter(get_default_partition_key_filter())
                )
                .scalars()
                .first()
            )
            sess.expunge_all()
            return a if a is not None else None

    def write_update_alarm(self, alarm, alarm_type_id=None):
        try:
            raw_alarm_rds = RawAlarms(
                uuid=str(uuid.uuid4()),
                alarm_timestamp_utc=alarm.alarm_time,
                source_id=alarm.source_id,
                source_system=alarm.source_system,
                tenant_id=alarm.tenant_id,
                raw_source_data=alarm.raw_info,
                alarm_type_id=alarm_type_id,
                door_id=alarm.door_uuid,
            )
            raw_alarm_uuid = self.db.save_object(
                raw_alarm_rds, "raw_alarms_v2"
            )
            log.debug(
                "Inserted record in raw_alarms table",
                alarm_id=raw_alarm_uuid,
            )
            return raw_alarm_uuid

        except errors.EntityExistsError:
            existing_alarm_id = self.alarm_id_by_source_id(
                alarm.source_id, alarm.tenant_id
            )
            if existing_alarm_id is not None:
                with self.db.get_session() as sess:
                    AlarmController._update_raw_alarm(
                        sess,
                        existing_alarm_id,
                        alarm_timestamp_utc=alarm.alarm_time,
                        source_id=alarm.source_id,
                        source_system=alarm.source_system,
                        tenant_id=alarm.tenant_id,
                        raw_source_data=alarm.raw_info,
                        door_id=alarm.door_uuid,
                    )
            log.debug(
                "Updated record in raw_alarms table",
                alarm_id=raw_alarm_rds.uuid,
                source_id=raw_alarm_rds.source_id,
            )
            return existing_alarm_id

    def send_video_request(self, alarm, start_time, end_time):
        """
        Post a message to send video for given alarm for the specified start and end time.
        @param alarm: Alarm object for which video is needed.
        @param start_time: Video start time
        @param end_time: Video end time
        """
        vid_req = VideoRequest(
            return_video=True,
            alarm_time_utc=alarm.alarm_time,
            alarm_uuid=alarm.alarm_uuid,
            camera_info=alarm.camera_info,
            video_start_time=start_time,
            video_end_time=end_time,
            video_file="",
            video_available=True,
            tenant_id=alarm.tenant_id,
        )
        payload = vid_req.to_json()
        if alarm.trace_data is not None:
            parent_span = opentracing.tracer.extract(
                Format.TEXT_MAP, alarm.trace_data
            )
            if parent_span is not None:
                opentracing.tracer.inject(
                    parent_span, Format.TEXT_MAP, payload
                )

        self._msg_sender.send(
            alarm.tenant_id,
            f"hip/{alarm.source_system}/send_video",
            payload,
        )

    def update_video_tags(
        self, tenant_id, ml_output_uuid, tags
    ) -> typing.List[str]:
        """Update the video tags (except the loitering tags) based on given ML
        output UUID and corresponding tags. Posts a message to update
        the associated alarm in ACS with the given tags."""
        blocked_tags = ["ALARM_ACTIVE_TOO_LONG"]
        filtered_tags = [
            VideoTags(
                uuid=str(uuid.uuid4()),
                ml_output_id=ml_output_uuid,
                video_tag=t,
                tenant_id=tenant_id,
            )
            for t in tags.video_tag_names
            if t not in blocked_tags
        ]

        video_tags = []
        if filtered_tags:
            log.info(
                "Write video tags",
                tags=filtered_tags,
                count=len(filtered_tags),
            )
            video_tags = [v.video_tag for v in filtered_tags]
            self.db.insert_many(filtered_tags)
        return video_tags
        # update the video tags based on the computation and send the msg to HIP

    def get_alarm_by_state(
        self,
        alarm_state: AlarmState,
        updated_before: typing.Optional[datetime.datetime] = None,
        partition_key: int = None,
    ):
        """Given the alarm state, return the most recently updated
        alarm_id in that state OR if updated_before is provided, return
        the most recently updated alarm_id that was
        updated before updated_before

        Args:
            alarm_state (AlarmState): [description]
            updated_before (typing.Optional[datetime.datetime], optional):
            Time before which returned alarm_id must have been updated.
            Defaults to None.

        Returns:
            tuple: alarm_id, tenant_id
        """
        with self.db.get_session() as sess:
            query = select(RawAlarms.uuid, RawAlarms.tenant_id).filter(
                RawAlarms.alarm_state == alarm_state.name
            )
            query = query.filter(
                RawAlarms.partition_key == partition_key
                if partition_key
                else get_default_partition_key_filter()
            )
            if updated_before is not None:
                query = query.filter(
                    RawAlarms.updated_at_utc <= updated_before
                )
            query = query.order_by(RawAlarms.updated_at_utc.desc())
            alarm_details = sess.execute(query).first()
        return alarm_details

    def mark_alarm_no_video(self, alarm_uuid: str, tenant_id: str) -> None:
        """Marks a given alarm as having video unavailable state, where
        video cannot be requested, or more video will not come in

        Args:
            alarm_uuid (str)
            tenant_id (str)
        """
        with self.db.get_session() as sess:
            alarm = (
                sess.execute(
                    select(RawAlarms)
                    .filter(RawAlarms.uuid == alarm_uuid)
                    .filter(RawAlarms.tenant_id == tenant_id)
                    .filter(get_default_partition_key_filter())
                )
                .scalars()
                .first()
            )
            assert (
                alarm is not None
            ), f"Invalid alarm to mark without video: {alarm_uuid}"
            alarm.video_available = False

    def inc_alarm_video_corrupt(
        self, alarm_uuid: str, tenant_id: str, partition_key: int = None
    ) -> None:
        """Marks a given alarm as having video corrupt (used to request video again)"""
        with self.db.get_session() as sess:
            alarm = (
                sess.execute(
                    select(RawAlarms)
                    .filter(RawAlarms.uuid == alarm_uuid)
                    .filter(RawAlarms.tenant_id == tenant_id)
                    .filter(
                        RawAlarms.partition_key == partition_key
                        if partition_key
                        else get_default_partition_key_filter()
                    )
                )
                .scalars()
                .first()
            )
            assert (
                alarm is not None
            ), f"Invalid alarm to mark without video: {alarm_uuid}"
            alarm.video_corrupt_retries += 1

    def get_tap_tags(
        self, alarm_uuid: str, tenant_id: str, partition_key: int = None
    ) -> typing.Tuple[typing.Optional[float], typing.Optional[Tags]]:
        """Return the TAP and tags corresponding to a raw alarm. If the alarm
        does not exist, or if the TAP and tags do not exist, return (None, None)
        """
        with self.db.get_session() as sess:
            tap = sess.execute(
                select(
                    RawAlarms.true_alarm_probability,
                )
                .filter(RawAlarms.uuid == alarm_uuid)
                .filter(RawAlarms.tenant_id == tenant_id)
                .filter(
                    RawAlarms.partition_key == partition_key
                    if partition_key
                    else get_default_partition_key_filter()
                )
            ).first()
            if tap is None or tap[0] is None:
                return None, None
            tap = tap[0]
            tags = sess.execute(
                select(VideoTags.video_tag)
                .filter(MLOutputs.uuid == RawAlarms.ml_output_id)
                .filter(MLOutputs.uuid == VideoTags.ml_output_id)
                .filter(RawAlarms.uuid == alarm_uuid)
                .filter(RawAlarms.tenant_id == tenant_id)
            ).all()
            return tap, Tags([], {"tags": [tag[0] for tag in tags]})

    def get_matching_alarm_ids(
        self,
        tenant_id: str,
        alarm_type_id: str,
        door_id: str,
        timestamp: datetime.datetime,
        time_delta: int,
        current_status: Optional[Iterable[str]] = None,
    ) -> typing.List[str]:
        """Get list of matching alarm uuids that occurred around
        (+/-time_delta) of given timestamp. The alarms are ordered in
        descending order of the timestamp at which alarm was triggered.

        Args:
            tenant_id (str):
            alarm_type_id (str):
            door_id (str):
            timestamp (datetime):
            time_delta (int): Number of seconds +/- of given timestamp during which the alarm has been triggered.
            current_status (List[str]): If specified return ids whose status is one of the values specified in the list.

        Returns:
            List of matching alarm uuids that match the given criteria.

        """
        delta_ts = datetime.timedelta(seconds=time_delta)
        start_time = timestamp - delta_ts
        end_time = timestamp + delta_ts

        with self.db.get_session() as sess:
            query = (
                select(RawAlarms.uuid, RawAlarms.current_status)
                .filter(
                    RawAlarms.tenant_id == tenant_id,
                    RawAlarms.alarm_type_id == alarm_type_id,
                    RawAlarms.door_id == door_id,
                )
                .filter(
                    RawAlarms.alarm_timestamp_utc.between(start_time, end_time)
                )
                .filter(
                    get_default_partition_key_filter(
                        start_at_utc=start_time, end_at_utc=end_time
                    )
                )
            )
            if current_status is not None:
                query = query.filter(
                    RawAlarms.current_status.in_(current_status)
                )

            alarms = sess.execute(
                query.order_by(RawAlarms.alarm_timestamp_utc.desc())
            ).all()
            return [a[0] for a in alarms]

    def update_alarm_processed_times(
        self,
        alarm: alarm_module.Alarm,
        process_start_time: typing.Optional[datetime.datetime],
        process_end_time: typing.Optional[datetime.datetime],
        partition_key: int = None,
    ):
        """Updates the actual processed start and end time within
        the video  for an alarm
        """
        with self.db.get_session() as sess:
            start = time.time()
            sess.execute(
                update(RawAlarms)
                .filter(RawAlarms.uuid == alarm.alarm_uuid)
                .filter(RawAlarms.tenant_id == alarm.tenant_id)
                .filter(
                    RawAlarms.partition_key == partition_key
                    if partition_key
                    else get_default_partition_key_filter()
                )
                .values(
                    processing_start_timestamp_utc=process_start_time,
                    processing_end_timestamp_utc=process_end_time,
                )
            )
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_WRITE_SUMMARY.labels(
                tenant=alarm.tenant_id,
                query="update_alarm_processed_times",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)

    def _resolve_user_id_update(
        self, old_status, old_user_id, new_status, new_user_id
    ):
        # No status changed, so no new user assigned
        # Covers adding comments or feedback
        if old_status == new_status or new_status in [None, ""]:
            return old_user_id

        # If transitioning to Pending, unassign any user
        if new_status == alarm_module.PENDING_STATUS:
            return None

        # Transitioning from Pending status
        if old_status == alarm_module.PENDING_STATUS:
            if old_user_id in [None, ""]:
                if new_status in [
                    alarm_module.RESOLVED_BY_HAKIMO_STATUS,
                    alarm_module.RESOLVED_MANUALLY,
                    alarm_module.IN_PROGRESS_STATUS,
                ]:
                    return new_user_id
                else:
                    return old_user_id
            else:
                return old_user_id

        if (
            old_status == alarm_module.IN_PROGRESS_STATUS
            and new_status in alarm_module.RESOLVED_STATUSES
        ):
            return old_user_id

        if (
            old_status in alarm_module.RESOLVED_STATUSES
            and new_status == alarm_module.IN_PROGRESS_STATUS
        ):
            return new_user_id

        # by default, change user id for all other status changes only if non hakimo id
        if new_user_id != self.resolver_user_id:
            return new_user_id
        else:
            return old_user_id

    def get_last_n_alarms_before(
        self,
        door_id: str,
        end_time: datetime.datetime,
        num_alarms: int = 1,
        tenant_id: typing.Optional[str] = None,
        self_alarm_uuid: typing.Optional[str] = None,
        partition_key: int = None,
    ) -> typing.List[RawAlarms]:
        with self.db.get_session() as sess:
            query = select(RawAlarms).filter(RawAlarms.door_id == door_id)
            if self_alarm_uuid is None:
                query = query.filter(RawAlarms.alarm_timestamp_utc < end_time)
            else:
                query = query.filter(
                    RawAlarms.alarm_timestamp_utc <= end_time
                ).filter(RawAlarms.uuid != self_alarm_uuid)
            query = (
                query.filter(
                    RawAlarms.partition_key == partition_key
                    if partition_key
                    else get_default_partition_key_filter()
                )
                .order_by(RawAlarms.alarm_timestamp_utc.desc())
                .limit(num_alarms)
            )
            start = time.time()
            alarms = sess.execute(query).scalars().all()
            sess.expunge_all()
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_READ_SUMMARY.labels(
                tenant=tenant_id,
                query="get_last_n_alarms_before",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)
            return alarms

    def get_alarm_times_at_door(
        self,
        door_uuid: str,
        start_time: datetime.datetime,
        end_time: typing.Optional[datetime.datetime] = None,
        alarm_type_id: typing.Union[typing.Sequence[str], str] = None,
    ) -> typing.List[datetime.datetime]:
        """
        Given a doorid, get raw alarm times at the door between
        start and end time
        If alarm_type_id is not None, only return alarms of that type
        """
        if end_time is None:
            end_time = datetime.datetime.max
        with self.db.get_session() as sess:
            query = (
                select(RawAlarms.alarm_timestamp_utc)
                .filter(RawAlarms.door_id == door_uuid)
                .filter(
                    (RawAlarms.alarm_timestamp_utc).between(
                        start_time, end_time
                    )
                )
            )
            query = query.filter(get_default_partition_key_filter())
            if isinstance(alarm_type_id, (list, tuple)):
                query = query.filter(
                    RawAlarms.alarm_type_id.in_(alarm_type_id)
                )
            elif alarm_type_id is not None:
                query = query.filter(RawAlarms.alarm_type_id == alarm_type_id)

            # unpack the tuple of ids from sqlalchemy row
            alarm_times = sess.execute(query).scalars().all()
        return alarm_times

    def get_matching_alarms_order_by_kwargs(
        self,
        tenant_id: str,
        door_id: str,
        timestamp: datetime.datetime,
        time_delta: int,
        **kwargs,
    ) -> typing.List[RawAlarms]:
        """Get list of matching alarms that occurred around
        (+/-time_delta) of given timestamp. The alarms are ordered
        according to the kwargs passed.

        Args:
            tenant_id (str):
            door_id (str):
            timestamp (datetime):
            time_delta (int): Number of seconds +/- of given timestamp during which the alarm has been triggered.

        Returns:
            List of matching alarm that match the given criteria.

        """
        delta_ts = datetime.timedelta(seconds=time_delta)
        start_time = timestamp - delta_ts
        end_time = timestamp + delta_ts

        with self.db.get_session() as sess:
            query = (
                select(RawAlarms)
                .filter(
                    RawAlarms.tenant_id == tenant_id,
                    RawAlarms.door_id == door_id,
                )
                .filter(
                    RawAlarms.alarm_timestamp_utc.between(start_time, end_time)
                )
                .filter(
                    get_default_partition_key_filter(
                        start_at_utc=start_time, end_at_utc=end_time
                    )
                )
            )
            if kwargs.get("order_by_source_id"):
                query = query.order_by(RawAlarms.source_id)
            elif kwargs.get("order_by_alarm_time"):
                query = query.order_by(RawAlarms.alarm_timestamp_utc)
            else:
                # Default
                query = query.order_by(RawAlarms.alarm_timestamp_utc.desc())

            alarms = sess.execute(query).scalars().all()
            sess.expunge_all()
            return alarms

    def get_preceding_motion_alarm_ids(
        self,
        tenant_id: str,
        timestamp: datetime.datetime,
        time_delta: int,
        source_entity_id: str,
        source_system: str,
        current_status: Optional[Iterable[str]] = None,
    ) -> typing.List[str]:
        """Get motion alarm ids for which the last motion event was
        recorded number of seconds before or equal to the given interval.
        The alarms are ordered in descending order of the timestamp at
        which last motion event was recorded for the alarm.
        Args:
            tenant_id (str):
            timestamp (datetime):
            time_delta (int): Number of seconds before (or at) which last
                              motion event was recorded for the alarm.
            source_entity_id (str):
            source_system (str):
        Returns:
            List of matching alarm ids matching the given criteria.
        """
        delta_ts = datetime.timedelta(seconds=time_delta)
        start_time = timestamp - delta_ts
        # Is referring another controller fine ?
        alarm_type_controller = AlarmTypesController(self.db)
        alarm_type_id = (
            alarm_type_controller.get_alarm_type_id_from_alarm_type(
                alarm_type=ALARM_TYPE_MOTION,
            )
        )
        with self.db.get_session() as sess:
            query = (
                select(RawAlarms.uuid)
                .filter(
                    RawAlarms.alarm_type_id == alarm_type_id,
                    RawAlarms.source_entity_id == source_entity_id,
                    RawAlarms.source_system == source_system,
                    RawAlarms.tenant_id == tenant_id,
                )
                .filter(
                    RawAlarms.last_motion_event_timestamp_utc.between(
                        start_time, timestamp
                    )
                )
            )
            if start_time and timestamp:
                query = query.filter(
                    get_default_partition_key_filter(
                        start_at_utc=start_time, end_at_utc=timestamp
                    )
                )
            else:
                query = query.filter(get_default_partition_key_filter())
            start = time.time()
            alarms = sess.execute(
                query.order_by(
                    RawAlarms.last_motion_event_timestamp_utc.desc()
                )
            ).all()
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_READ_SUMMARY.labels(
                tenant=tenant_id,
                query="get_raw_alarms_by_motion_timestamp",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)

            return [a[0] for a in alarms]

    def get_preceding_motion_alarm_ids_v2(
        self,
        tenant_id: str,
        timestamp: datetime.datetime,
        time_delta: int,
        source_entity_id: str,
        source_system: str,
        current_status: Optional[Iterable[str]] = None,
        partition_key: int = None,
    ) -> typing.List[str]:
        """Get motion alarm ids for which the last alarm time was
        recorded number of seconds before or equal to the given interval.
        The alarms are ordered in descending order of the timestamp

        Args:
            tenant_id (str):
            timestamp (datetime):
            time_delta (int): Number of seconds before (or at) which last
                              last alarm was.
            source_entity_id (str):
            source_system (str):

        Returns:
            List of matching alarm ids matching the given criteria.
        """
        # TODO: Rename method to be non motion specific
        # (no longer using last_motion_timestamp)
        delta_ts = datetime.timedelta(seconds=time_delta)
        start_time = timestamp - delta_ts
        # Is referring another controller fine ?
        alarm_type_controller = AlarmTypesController(self.db)
        alarm_type_id = (
            alarm_type_controller.get_alarm_type_id_from_alarm_type(
                alarm_type=ALARM_TYPE_MOTION,
            )
        )
        with self.db.get_session() as sess:
            query = (
                select(RawAlarms.uuid)
                .filter(
                    RawAlarms.alarm_type_id == alarm_type_id,
                    RawAlarms.source_entity_id == source_entity_id,
                    RawAlarms.source_system == source_system,
                    RawAlarms.tenant_id == tenant_id,
                )
                .filter(
                    RawAlarms.alarm_timestamp_utc.between(
                        start_time, timestamp
                    )
                )
            )
            if partition_key:
                query = query.filter(RawAlarms.partition_key == partition_key)
            elif start_time and timestamp:
                query = query.filter(
                    get_default_partition_key_filter(
                        start_at_utc=start_time, end_at_utc=timestamp
                    )
                )
            else:
                query = query.filter(get_default_partition_key_filter())
            start = time.time()
            alarms = sess.execute(
                query.order_by(RawAlarms.alarm_timestamp_utc.desc())
            ).all()
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_READ_SUMMARY.labels(
                tenant=tenant_id,
                query="get_raw_alarms_by_motion_timestamp",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)

            return [a[0] for a in alarms]

    def update_last_motion_event_time(
        self, alarm_id, event_ts: datetime.datetime
    ):
        """Update the time at which last motion event happened for given alarm id.

        Args:
            alarm_id (str):
            event_ts (datetime.datetime):
        """
        with self.db.get_session() as sess:
            self._update_raw_alarm(
                sess, alarm_id, last_motion_event_timestamp_utc=event_ts
            )

    def get_neighboring_alarms(
        self,
        alarm: RawAlarms,
        before_limit: int = 5,
        after_limit: int = 5,
        before_time: typing.Optional[datetime.timedelta] = None,
        after_time: typing.Optional[datetime.timedelta] = None,
        partition_key: typing.Optional[int] = None,
    ) -> typing.Tuple[typing.Sequence[typing.Tuple[RawAlarms, str]], int]:
        return self._fetcher.get_neighboring_alarms(
            alarm,
            before_limit,
            after_limit,
            before_time,
            after_time,
            partition_key=partition_key,
        )

    def get_true_motion_alarms(
        self,
        tenant_id: str,
        source_entity_id: str,
        start_timestamp: datetime.datetime,
        end_timestamp: datetime.datetime,
        source_system: str = ACSSource.MOTION.value,
        tap: int = 90,
    ) -> typing.List[RawAlarms]:
        """Get list of true motion alarms (based on TAP) for the specified source entity.

        Args:
            tenant_id (str):
            source_entity_id(str):
            start_timestamp (datetime): start time from which true alarms need to be looked up.
            end_timestamp (datetime): timestamp till which true alarms need to be looked up.
            source_system (str):
            TAP (int): an alarm is considered to be true alarm if its TAP is greater than or equal
            to the TAP value specified for this parameter.

        Returns:
            List of matching alarms matching the given criteria.
        """
        alarm_type_controller = AlarmTypesController(self.db)
        alarm_type_id = (
            alarm_type_controller.get_alarm_type_id_from_alarm_type(
                alarm_type=ALARM_TYPE_MOTION,
            )
        )

        with self.db.get_session() as sess:
            query = (
                select(RawAlarms)
                .filter(
                    RawAlarms.alarm_type_id == alarm_type_id,
                    RawAlarms.source_entity_id == source_entity_id,
                    RawAlarms.source_system == source_system,
                    RawAlarms.tenant_id == tenant_id,
                    RawAlarms.true_alarm_probability >= tap,
                )
                .filter(
                    RawAlarms.alarm_timestamp_utc.between(
                        start_timestamp, end_timestamp
                    )
                )
            )
            if start_timestamp and end_timestamp:
                query = query.filter(
                    get_default_partition_key_filter(
                        start_at_utc=start_timestamp,
                        end_at_utc=end_timestamp,
                    )
                )
            else:
                query = query.filter(get_default_partition_key_filter())
            alarms = (
                sess.execute(
                    query.order_by(RawAlarms.alarm_timestamp_utc.desc())
                )
                .scalars()
                .all()
            )
            sess.expunge_all()

        return alarms

    def get_processing_config(
        self, alarm: alarm_module.Alarm
    ) -> typing.Optional[AlarmProcessingConfig]:
        return self._config_handler.get_processing_config(alarm)

    def get_alarm_to_process_from_queue(self, alarm_criteria):
        message = self._redis_consumer_utils.get_message_from_queue()
        if message is None:
            log.info(
                "No message in queue to process fetching from set",
            )
            message = self._redis_consumer_utils.get_old_unprocessed_message()
        alarm_details = [None, None, False]
        if message:
            alarm_id, tenant_id, alarm_timestamp_utc = (
                message["alarm_id"],
                message["tenant_id"],
                message["alarm_timestamp_utc"],
            )
            if not (
                alarm_criteria.enterprise_processor
                or alarm_criteria.per_tenant_processor
            ):
                if isinstance(alarm_timestamp_utc, str):
                    alarm_timestamp_utc = dt.fromisoformat(
                        alarm_timestamp_utc.replace("Z", "+00:00")
                    )
                alarm_time_diff = int(time.time()) - int(
                    alarm_timestamp_utc.timestamp()
                )
                if alarm_time_diff > config.HAIE.OLD_MOTION_ALARM_SKIP:
                    log.info(
                        "Motion Alarm is more than an hour old, skipping processing...",
                        alarm_id=alarm_id,
                        tenant_id=tenant_id,
                    )
                    self.transition_alarm(
                        alarm_id, tenant_id, AlarmState.PROCESSED
                    )
                    return message, [None, None, True], [alarm_id, tenant_id]
            self.transition_alarm(alarm_id, tenant_id, AlarmState.IN_PROGRESS)
            log.info(
                "Updated alarm state",
                old_state=AlarmState.UNPROCESSED.name,
                new_state=AlarmState.IN_PROGRESS.name,
                alarm_id=alarm_id,
                tenant_id=tenant_id,
            )
            alarm_details[0], alarm_details[1] = alarm_id, tenant_id
            log.info(
                "AlarmProcessingLog",
                event_type="Alarm Consumed",
                alarm_id=alarm_id,
                tenant_id=tenant_id,
            )
        return message, alarm_details, [None, None]
