from typing import Optional

import sqlalchemy
import structlog
from sqlalchemy import and_

from controller.interface import ControllerBase
from models_rds.alarm_group import AlarmGroup
from vision.services.src.event_processor.models.events import (
    AlarmGroupState,
    Resolution,
)

logger = structlog.get_logger("hakimo", "alarm_group_controller")


class AlarmGroupController(ControllerBase):
    def create_alarm_group(self, alarm_group: AlarmGroup) -> AlarmGroup:
        try:
            with self.db.get_session() as session:
                # Optimistic check first for better performance
                existing_active = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.camera_group_id
                            == alarm_group.camera_group_id,
                            AlarmGroup.state.in_(
                                [
                                    AlarmGroupState.PENDING,
                                    AlarmGroupState.IN_PROGRESS,
                                ]
                            ),
                        )
                    )
                    .first()
                )

                if existing_active:
                    # Detach from session before returning
                    session.expunge(existing_active)
                    logger.debug(
                        "active_alarm_group_exists",
                        group_id=existing_active.id,
                        camera_group_id=existing_active.camera_group_id,
                        state=existing_active.state,
                    )
                    return existing_active

                try:
                    session.add(alarm_group)
                    session.commit()
                    # Detach from session before returning
                    session.expunge(alarm_group)
                    logger.debug(
                        "alarm_group_created",
                        group_id=alarm_group.id,
                        camera_group_id=alarm_group.camera_group_id,
                        state=alarm_group.state,
                    )
                    return alarm_group

                except sqlalchemy.exc.IntegrityError as e:
                    session.rollback()
                    # Double check if someone else created an active alarm
                    # between our check and insert
                    existing_active = (
                        session.query(AlarmGroup)
                        .filter(
                            and_(
                                AlarmGroup.camera_group_id
                                == alarm_group.camera_group_id,
                                AlarmGroup.state.in_(
                                    [
                                        AlarmGroupState.PENDING,
                                        AlarmGroupState.IN_PROGRESS,
                                    ]
                                ),
                            )
                        )
                        .first()
                    )

                    if existing_active:
                        # Detach from session before returning
                        session.expunge(existing_active)
                        logger.info(
                            "active_alarm_group_created_by_another_transaction",
                            group_id=existing_active.id,
                            camera_group_id=existing_active.camera_group_id,
                            state=existing_active.state,
                        )
                        return existing_active

                    # If no existing active found, it was a different integrity error
                    logger.error(
                        "unexpected_integrity_error",
                        error=str(e),
                        camera_group_id=alarm_group.camera_group_id,
                        exc_info=True,
                    )
                    raise

        except Exception as e:
            logger.error(
                "unexpected_error_creating_alarm_group",
                error=str(e),
                exc_info=True,
            )
            raise

    def get_pending_alarm_group(self, group_id: str) -> Optional[AlarmGroup]:
        """
        Get an alarm group by ID that is in PENDING state
        Args:
            group_id: ID of the alarm group to retrieve
        Returns:
            AlarmGroup if found in PENDING state, None otherwise
        """
        try:
            with self.db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.id == group_id,
                            AlarmGroup.state == AlarmGroupState.PENDING,
                        )
                    )
                    .first()
                )
                if alarm_group:
                    # Detach from session before returning
                    session.expunge(alarm_group)
                return alarm_group

        except Exception as e:
            logger.error(
                "error_getting_pending_alarm_group",
                error=str(e),
                group_id=group_id,
                exc_info=True,
            )
            raise

    def get_escalated_alarm_group(self, group_id: str) -> Optional[AlarmGroup]:
        """
        Get an alarm group by ID that is in IN_PROGRESS state with ESCALATION_OPEN resolution
        Args:
            group_id: ID of the alarm group to retrieve
        Returns:
            AlarmGroup if found with matching criteria, None otherwise
        """
        try:
            with self.db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.id == group_id,
                            AlarmGroup.state == AlarmGroupState.IN_PROGRESS,
                            AlarmGroup.resolution
                            == Resolution.ESCALATION_OPEN,
                        )
                    )
                    .first()
                )
                if alarm_group:
                    # Detach from session before returning
                    session.expunge(alarm_group)
                return alarm_group

        except Exception as e:
            logger.error(
                "error_getting_escalated_alarm_group",
                error=str(e),
                group_id=group_id,
                exc_info=True,
            )
            raise

    def update_alarm_group(self, alarm_group: AlarmGroup) -> AlarmGroup:
        with self.db.get_session() as session:
            session.add(alarm_group)
            session.commit()
            # Detach from session before returning
            session.expunge(alarm_group)
            return alarm_group

    def get_active_alarm_group(self, group_id: str) -> Optional[AlarmGroup]:
        """
        Get an alarm group by ID that is in either PENDING or IN_PROGRESS state
        Args:
            group_id: ID of the alarm group to retrieve
        Returns:
            AlarmGroup if found in PENDING or IN_PROGRESS state, None otherwise
        """
        try:
            with self.db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.id == group_id,
                            AlarmGroup.state.in_(
                                [
                                    AlarmGroupState.PENDING,
                                    AlarmGroupState.IN_PROGRESS,
                                ]
                            ),
                        )
                    )
                    .first()
                )
                if alarm_group:
                    # Detach from session before returning
                    session.expunge(alarm_group)
                return alarm_group

        except Exception as e:
            logger.error(
                "error_getting_active_alarm_group",
                error=str(e),
                group_id=group_id,
                exc_info=True,
            )
            raise

    def get_active_alarm_group_for_operator(
        self, group_id: str, operator_id: str
    ) -> Optional[AlarmGroup]:
        """
        Get an alarm group by ID and operator ID that is in either PENDING or IN_PROGRESS state
        Args:
            group_id: ID of the alarm group to retrieve
            operator_id: ID of the operator assigned to the alarm group
        Returns:
            AlarmGroup if found in PENDING or IN_PROGRESS state with matching operator, None otherwise
        """
        try:
            with self.db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(
                        and_(
                            AlarmGroup.id == group_id,
                            AlarmGroup.operator_id == operator_id,
                            AlarmGroup.state.in_(
                                [
                                    AlarmGroupState.PENDING,
                                    AlarmGroupState.IN_PROGRESS,
                                ]
                            ),
                        )
                    )
                    .first()
                )
                if alarm_group:
                    # Detach from session before returning
                    session.expunge(alarm_group)
                return alarm_group

        except Exception as e:
            logger.error(
                "error_getting_active_alarm_group_for_operator",
                error=str(e),
                group_id=group_id,
                operator_id=operator_id,
                exc_info=True,
            )
            raise

    def get_dummy_alarm_group_for_tenant(
        self, tenant_id: str
    ) -> Optional[AlarmGroup]:
        """
        Get a dummy alarm group for a tenant
        Args:
            tenant_id: ID of the tenant to retrieve
        Returns:
            AlarmGroup if found, None otherwise
        """
        try:
            with self.db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(
                        AlarmGroup.tenant_id == tenant_id,
                        AlarmGroup.operator_id
                        == "system_operator_do_not_delete",
                    )
                    .first()
                )
                if alarm_group:
                    # Detach from session before returning
                    session.expunge(alarm_group)
                return alarm_group
        except Exception as e:
            logger.error(
                "error_getting_dummy_alarm_group_for_tenant",
                error=str(e),
                tenant_id=tenant_id,
                exc_info=True,
            )
            raise

    def get_alarm_group_by_id(
        self, alarm_group_id: str
    ) -> Optional[AlarmGroup]:
        """
        Get an alarm group by ID
        Args:
            alarm_group_id: ID of the alarm group to retrieve
        Returns:
            AlarmGroup if found, None otherwise
        """
        try:
            with self.db.get_session() as session:
                alarm_group = (
                    session.query(AlarmGroup)
                    .filter(AlarmGroup.id == alarm_group_id)
                    .first()
                )
                if alarm_group:
                    # Detach from session before returning
                    session.expunge(alarm_group)
                    return alarm_group
                return None
        except Exception as e:
            logger.error(
                "error_getting_alarm_group_by_id",
                error=str(e),
                alarm_group_id=alarm_group_id,
                exc_info=True,
            )
            raise
