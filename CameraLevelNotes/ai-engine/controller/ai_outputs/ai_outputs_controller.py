import typing

from sqlalchemy import and_

from controller.interface import ControllerBase
from models_rds.ai_outputs_location_alarms import AIOutputsLocationAlarms
from models_rds.ai_outputs_raw_alarms import AIOutputsRawAlarms
from models_rds.alarm_group import AlarmGroup
from vision.services.src.event_processor.models.events import (
    AlarmGroupState,
    Severity,
)


class AIOutputsController(ControllerBase):
    def add_ai_output_for_raw_alarm(
        self,
        raw_alarm_id: str,
        tenant_id: str,
        analysis: typing.Optional[str],
        recommendation: typing.Optional[str],
    ) -> int:
        with self.db.get_session() as sess:
            ao = AIOutputsRawAlarms(
                raw_alarm_id=raw_alarm_id,
                tenant_id=tenant_id,
                analysis=analysis,
                recommendation=recommendation,
            )
            sess.add(ao)
            sess.commit()
            sess.refresh(ao)
            return ao.int_id

    def get_latest_ai_output_for_raw_alarm(
        self, raw_alarm_id: str
    ) -> typing.Optional[AIOutputsRawAlarms]:
        with self.db.get_session() as sess:
            ao = (
                sess.query(AIOutputsRawAlarms)
                .filter(AIOutputsRawAlarms.raw_alarm_id == raw_alarm_id)
                .order_by(AIOutputsRawAlarms.created_at_utc.desc())
                .first()
            )
            sess.expunge_all()
            return ao

    def add_ai_output_for_location_alarm(
        self,
        location_alarm_id: int,
        tenant_id: str,
        summary: typing.Optional[str],
        recommendation: typing.Optional[str],
        explanation: typing.Optional[str] = None,
        raw_state: typing.Optional[typing.Dict] = None,
    ) -> int:
        with self.db.get_session() as sess:
            ao = AIOutputsLocationAlarms(
                location_alarm_id=location_alarm_id,
                tenant_id=tenant_id,
                summary=summary,
                recommendation=recommendation,
                explanation=explanation,
                raw_state=raw_state,
            )
            sess.add(ao)
            sess.commit()
            sess.refresh(ao)
            return ao.int_id

    def get_latest_ai_output_for_location_alarm(
        self, location_alarm_id: str
    ) -> typing.Optional[AIOutputsLocationAlarms]:
        with self.db.get_session() as sess:
            ao = (
                sess.query(AIOutputsLocationAlarms)
                .filter(
                    AIOutputsLocationAlarms.location_alarm_id
                    == location_alarm_id
                )
                .order_by(AIOutputsLocationAlarms.created_at_utc.desc())
                .first()
            )
            sess.expunge_all()
            return ao

    def get_latest_ai_output_for_alarm_group_id(
        self, alarm_group_id: str
    ) -> typing.Optional[AIOutputsLocationAlarms]:
        with self.db.get_session() as sess:
            ao = (
                sess.query(AIOutputsLocationAlarms)
                .filter(
                    AIOutputsLocationAlarms.alarm_group_id == alarm_group_id
                )
                .first()
            )
            sess.expunge_all()
            return ao

    def get_latest_ai_output_for_camera_group_id(
        self, camera_group_id: str
    ) -> typing.Optional[typing.List[AIOutputsLocationAlarms]]:
        with self.db.get_session() as sess:
            alarm_groups = sess.query(AlarmGroup).filter(
                and_(
                    AlarmGroup.camera_group_id == camera_group_id,
                    AlarmGroup.state.in_(
                        [AlarmGroupState.PENDING, AlarmGroupState.IN_PROGRESS]
                    ),
                    AlarmGroup.severity.in_([Severity.HIGH, Severity.LOW]),
                )
            )

            if not alarm_groups:
                return None

            alarm_group_ids = [alarm_group.id for alarm_group in alarm_groups]

            ao = sess.query(AIOutputsLocationAlarms).filter(
                AIOutputsLocationAlarms.alarm_group_id.in_(alarm_group_ids)
            )
            sess.expunge_all()
            return ao
