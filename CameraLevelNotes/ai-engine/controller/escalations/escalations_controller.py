import datetime
import typing
from typing import List, Optional

import structlog
from sqlalchemy import select
from sqlalchemy.orm import aliased, joinedload

from controller.escalations.escalations_filters import EscalationFilters
from controller.interface import ControllerBase
from interfaces.escalations import EscalationStatus
from models_rds.escalation_update import EscalationUpdates
from models_rds.escalations import Escalations
from models_rds.locations import Locations
from models_rds.users import Users

log = structlog.get_logger("hakimo", module="Escalations Controller")


class EscalationsController(ControllerBase):
    def create_escalation(
        self,
        location_id: int,
        camera_id: Optional[str] = None,
        created_by_user_id: Optional[str] = None,
    ) -> int:
        with self.db.get_session() as sess:
            escalation = Escalations(
                camera_id=camera_id,
                location_id=location_id,
                created_by_user_id=created_by_user_id,
            )
            sess.add(escalation)
            sess.commit()
            sess.refresh(escalation)
            return escalation.int_id

    def update_escalation(
        self,
        escalation_id: int,
        current_status: Optional[EscalationStatus] = None,
        resolved_by_user_id: Optional[str] = None,
        resolution_comment: Optional[str] = None,
    ) -> int:
        with self.db.get_session() as sess:
            query = select(Escalations).filter(
                Escalations.int_id == escalation_id
            )
            escalation = sess.execute(query).scalars().first()
            if not escalation:
                raise ValueError(f"Escalation not found: {escalation_id}")
            if current_status is not None:
                escalation.current_status = current_status
                if current_status == EscalationStatus.RESOLVED:
                    escalation.resolved_at_utc = datetime.datetime.now(
                        datetime.timezone.utc
                    )
            if resolved_by_user_id is not None:
                escalation.resolved_by_user_id = resolved_by_user_id
            if resolution_comment is not None:
                escalation.resolution_comment = resolution_comment
            sess.commit()
            sess.refresh(escalation)
            return escalation.int_id

    def get_escalation_by_id(
        self, escalation_id: int
    ) -> Optional[Escalations]:
        with self.db.get_session() as sess:
            query = select(Escalations).filter(
                Escalations.int_id == escalation_id
            )
            escalation = sess.execute(query).scalars().first()
            sess.expunge_all()
            return escalation

    def get_escalation_updates(
        self, escalation_ids: typing.List[int]
    ) -> typing.Sequence[EscalationUpdates]:
        stmt = (
            select(EscalationUpdates)
            .filter(EscalationUpdates.escalation_id.in_(escalation_ids))
            .order_by(EscalationUpdates.int_id)
        )
        stmt = stmt.options(joinedload(EscalationUpdates.user))
        with self.db.get_session() as sess:
            escalation_updates = sess.execute(stmt).scalars().all()
            sess.expunge_all()
        return escalation_updates

    def create_escalation_updates(
        self, escalation_update: EscalationUpdates
    ) -> None:
        with self.db.get_session() as sess:
            sess.add(escalation_update)
            sess.flush()
            sess.refresh(escalation_update)

    def update_escalation_update_text(
        self, escalation_id: int, update_type: str, new_update_text: str
    ) -> bool:
        """Update the update_text of an existing escalation update"""
        with self.db.get_session() as sess:
            query = (
                select(EscalationUpdates)
                .filter(
                    EscalationUpdates.escalation_id == escalation_id,
                    EscalationUpdates.update_type == update_type,
                )
                .order_by(EscalationUpdates.update_timestamp_utc.desc())
            )

            escalation_update = sess.execute(query).scalars().first()
            if escalation_update:
                escalation_update.update_text = new_update_text
                sess.commit()
                return True
            return False

    def get_escalations(
        self,
        vision_tenant_ids: List[str],
        filters: EscalationFilters,
        offset: int = 0,
        limit: Optional[int] = 10,
    ) -> List[Escalations]:
        with self.db.get_session() as sess:
            created_by_user_alias = aliased(Users)
            resolved_by_user_alias = aliased(Users)
            q = (
                select(Escalations)
                .join(Escalations.location)
                .join(
                    created_by_user_alias,
                    Escalations.created_by_user_id
                    == created_by_user_alias.uuid,
                )
                .join(
                    resolved_by_user_alias,
                    Escalations.resolved_by_user_id
                    == resolved_by_user_alias.uuid,
                    isouter=True,
                )
                .options(
                    joinedload(Escalations.location),
                    joinedload(Escalations.created_by_user).load_only(
                        "uuid", "name"
                    ),
                    joinedload(Escalations.resolved_by_user).load_only(
                        "uuid", "name"
                    ),
                )
            )
            if vision_tenant_ids is not None:
                q = q.filter(Locations.tenant_id.in_(vision_tenant_ids))
            if filters.location_ids is not None:
                q = q.filter(Escalations.location_id.in_(filters.location_ids))
            if filters.status is not None:
                q = q.filter(Escalations.current_status.in_(filters.status))
            if filters.utc_time_interval is not None:
                start_time, end_time = filters.utc_time_interval
                if start_time:
                    q = q.filter(Escalations.created_at_utc >= start_time)
                if end_time:
                    q = q.filter(Escalations.created_at_utc <= end_time)
            if filters.camera_ids is not None:
                q = q.filter(Escalations.camera_id.in_(filters.camera_ids))
            q = q.order_by(Escalations.created_at_utc.desc()).offset(offset)
            if limit is not None:
                q = q.limit(limit)

            escalations = sess.execute(q).scalars().all()
            sess.expunge_all()
            return escalations
