import typing
from abc import ABC, abstractmethod

from database.db_adapter import DBAdapter


class ControllerABC(ABC):
    @abstractmethod
    def get_db(self) -> DBAdapter: ...


class ControllerBase(ControllerABC):
    def __init__(
        self, db: DBAdapter, read_db: typing.Optional[DBAdapter] = None
    ):
        """
        Since this is the base class for all controllers, all controllers will have access to both read
        and write db replicas. Falls back to write replica, if read replica is not specified at initialization.
        """
        self.db = db
        self.read_db = read_db if read_db is not None else db

    def get_db(self) -> DBAdapter:
        return self.db

    def get_read_db(self) -> typing.Optional[DBAdapter]:
        return self.read_db
