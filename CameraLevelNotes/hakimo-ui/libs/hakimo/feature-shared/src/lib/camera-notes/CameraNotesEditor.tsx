/* eslint-disable max-lines */
import { useUpdateCameraNotes } from '@hakimo-ui/hakimo/data-access';
import { toast } from '@hakimo-ui/hakimo/util';
import { Al<PERSON>, Button, InputField } from '@hakimo-ui/shared/ui-base';
import { TrashIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';

interface Props {
  cameraId: string;
  initialNotes: string[];
  onNotesUpdated?: () => void;
  showSaveCancel?: boolean;
  className?: string;
}

export function CameraNotesEditor(props: Props) {
  const {
    cameraId,
    initialNotes,
    onNotesUpdated,
    showSaveCancel = true,
    className = '',
  } = props;
  const [localNotes, setLocalNotes] = useState<string[]>(initialNotes);
  const [showValidationError, setShowValidationError] = useState(false);

  const updateNotesMutation = useUpdateCameraNotes(cameraId, () => {
    toast('Notes updated successfully', { type: 'success' });
    onNotesUpdated?.();
  });

  useEffect(() => {
    setLocalNotes(initialNotes);
  }, [initialNotes]);

  const hasChanges =
    JSON.stringify(localNotes) !== JSON.stringify(initialNotes);

  const handleAddNote = () => {
    setLocalNotes([...localNotes, '']);
  };

  const handleDeleteNote = (index: number) => {
    setLocalNotes(localNotes.filter((_, i) => i !== index));
  };

  const handleNoteChange =
    (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const newNotes = [...localNotes];
      newNotes[index] = e.target.value;
      setLocalNotes(newNotes);
      setShowValidationError(false);
    };

  const handleSave = () => {
    if (localNotes.some((note) => note.trim() === '')) {
      setShowValidationError(true);
      return;
    }
    updateNotesMutation.mutate({ notes: localNotes });
  };

  const handleCancel = () => {
    setLocalNotes(initialNotes);
    setShowValidationError(false);
  };

  return (
    <div className={className}>
      {showValidationError && (
        <div className="mb-4">
          <Alert type="warning">
            Notes should not be empty. Provide info or delete the empty ones.
          </Alert>
        </div>
      )}

      <div className="space-y-2">
        {localNotes.map((note, index) => (
          <div key={index} className="flex items-center gap-2">
            <div className="flex-1">
              <InputField
                value={note}
                type="text"
                onChange={handleNoteChange(index)}
                placeholder="Enter note text..."
              />
            </div>
            <Button variant="icon" onClick={() => handleDeleteNote(index)}>
              <TrashIcon className="h-5 w-5" />
            </Button>
          </div>
        ))}
        <div>
          <Button onClick={handleAddNote} className="py-1">
            Add Note
          </Button>
        </div>
      </div>

      {showSaveCancel && hasChanges && (
        <div className="flex gap-2 border-t border-gray-200 pt-4 dark:border-gray-700">
          <Button
            onClick={handleSave}
            variant="primary"
            disabled={updateNotesMutation.isLoading}
            loading={updateNotesMutation.isLoading}
          >
            Save Notes
          </Button>
          <Button
            onClick={handleCancel}
            variant="outline"
            disabled={updateNotesMutation.isLoading}
          >
            Cancel
          </Button>
        </div>
      )}
    </div>
  );
}

export function useDeleteAllCameraNotes(
  cameraId: string,
  onSuccess?: () => void
) {
  const updateNotesMutation = useUpdateCameraNotes(cameraId, onSuccess);

  return {
    deleteAllNotes: () => updateNotesMutation.mutate({ notes: [] }),
    isLoading: updateNotesMutation.isLoading,
  };
}

interface DeleteCameraNotesButtonProps {
  cameraId: string;
  cameraName: string;
  onSuccess: () => void;
}

export function DeleteCameraNotesButton({
  cameraId,
  cameraName,
  onSuccess,
}: DeleteCameraNotesButtonProps) {
  const { deleteAllNotes, isLoading } = useDeleteAllCameraNotes(
    cameraId,
    () => {
      toast('All camera notes deleted successfully', { type: 'success' });
      onSuccess();
    }
  );

  const handleDelete = () => {
    if (
      window.confirm(
        `Are you sure you want to remove all notes for camera "${cameraName}"?`
      )
    ) {
      deleteAllNotes();
    }
  };

  return (
    <Button
      variant="icon"
      onClick={handleDelete}
      title="Remove camera notes"
      disabled={isLoading}
    >
      <XMarkIcon className="text-status-red h-5 w-5" />
    </Button>
  );
}

export default CameraNotesEditor;
