/* eslint-disable max-lines */
import { useLocationCameraNotes } from '@hakimo-ui/hakimo/data-access';
import { CameraNotesBulkResponse } from '@hakimo-ui/hakimo/types';
import { <PERSON><PERSON>, Button, HakimoSpinner } from '@hakimo-ui/shared/ui-base';
import {
  CameraNotesEditor,
  DeleteCameraNotesButton,
} from '@hakimo-ui/hakimo/feature-shared';
import {
  ArrowTopRightOnSquareIcon,
  PencilSquareIcon,
} from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';

interface Props {
  locationId: number;
  tenantId: string;
}

interface CameraNotesState {
  cameraId: string;
  cameraName: string;
  notes: string[];
  isEditing: boolean;
  hasChanges: boolean;
}

export function LocationCameraNotes(props: Props) {
  const { locationId } = props;
  const navigate = useNavigate();

  const {
    data: cameraNotesData = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useLocationCameraNotes(locationId.toString());
  const [localCameraNotes, setLocalCameraNotes] = useState<CameraNotesState[]>(
    []
  );

  useEffect(() => {
    const initialState = cameraNotesData.map(
      (camera: CameraNotesBulkResponse) => ({
        cameraId: camera.cameraId,
        cameraName: camera.cameraName,
        notes: [...camera.notes],
        isEditing: false,
        hasChanges: false,
      })
    );
    setLocalCameraNotes(initialState);
  }, [cameraNotesData]);

  const handleOpenCameraDetails = (cameraId: string) => {
    window.open(`/cameras?camId=${cameraId}`, '_blank');
  };

  const handleAddCameraNotes = () => {
    navigate(`/cameras?locationId=${locationId}`);
  };

  const handleEditCamera = (cameraId: string) => {
    setLocalCameraNotes((prev) =>
      prev.map((camera) =>
        camera.cameraId === cameraId ? { ...camera, isEditing: true } : camera
      )
    );
  };

  const handleCancelEdit = (cameraId: string) => {
    setLocalCameraNotes((prev) =>
      prev.map((camera) =>
        camera.cameraId === cameraId
          ? { ...camera, isEditing: false, hasChanges: false }
          : camera
      )
    );
  };

  if (isLoading) {
    return (
      <div className="relative px-8">
        <div className="dark:bg-dark-bg/70 absolute inset-0 z-50 bg-white/70"></div>
        <div className="absolute inset-0 z-50 flex items-center justify-center">
          <HakimoSpinner />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="px-8">
        <Alert type="error">
          {error?.message || 'Failed to load camera notes'}
        </Alert>
      </div>
    );
  }

  return (
    <div className="px-8">
      {/* Header Section */}
      <div className="border-onlight-line-2 dark:border-ondark-line-2 mb-6 flex items-center justify-between border-b pb-4">
        <div className="flex items-center gap-4">
          <span className="text-lg font-bold">Camera Notes</span>
        </div>
        <Button onClick={handleAddCameraNotes} variant="primary">
          <span className="flex items-center gap-2">Add New Camera Notes</span>
        </Button>
      </div>

      {/* Camera Notes Content */}
      {localCameraNotes.length === 0 ? (
        <div className="p-6">
          <Alert>No camera notes available for this location</Alert>
        </div>
      ) : (
        <div className="space-y-4">
          {localCameraNotes.map((cameraNote, index) => (
            <div
              key={cameraNote.cameraId}
              className="border-onlight-line-2 dark:border-ondark-line-2 bg-onlight-bg-1 dark:bg-ondark-bg-1 space-y-2 rounded-md border p-4"
            >
              <div className="border-onlight-line-2 dark:border-ondark-line-2 mb-2 flex items-center justify-between border-b pb-2">
                <div className="flex items-center">
                  <div className="bg-ondark-primary dark:bg-primary-500 mr-2 h-2 w-2 rounded-full"></div>
                  <span className="text-onlight-text-1 dark:text-ondark-text-1 font-medium">
                    {cameraNote.cameraName}
                  </span>
                  <Button
                    variant="icon"
                    onClick={() => handleOpenCameraDetails(cameraNote.cameraId)}
                    title="Open camera details"
                    className="ml-1"
                  >
                    <ArrowTopRightOnSquareIcon className="text-onlight-text-2 dark:text-ondark-text-2 h-5 w-5" />
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  {!cameraNote.isEditing ? (
                    <>
                      <Button
                        variant="icon"
                        onClick={() => handleEditCamera(cameraNote.cameraId)}
                        title="Edit camera notes"
                      >
                        <PencilSquareIcon className="h-5 w-5" />
                      </Button>
                      <DeleteCameraNotesButton
                        cameraId={cameraNote.cameraId}
                        cameraName={cameraNote.cameraName}
                        onSuccess={refetch}
                      />
                    </>
                  ) : (
                    <Button
                      onClick={() => handleCancelEdit(cameraNote.cameraId)}
                      variant="outline"
                      className="py-1 text-xs"
                    >
                      Cancel Edit
                    </Button>
                  )}
                </div>
              </div>

              {cameraNote.isEditing ? (
                <CameraNotesEditor
                  cameraId={cameraNote.cameraId}
                  initialNotes={cameraNote.notes}
                  onNotesUpdated={() => {
                    refetch();
                    setLocalCameraNotes((prev) =>
                      prev.map((cam, idx) =>
                        idx === index
                          ? { ...cam, isEditing: false, hasChanges: false }
                          : cam
                      )
                    );
                  }}
                  showSaveCancel={true}
                  className="space-y-2"
                />
              ) : (
                <ul className="list-disc space-y-2 pl-8">
                  {cameraNote.notes.map((note: string, j: number) => (
                    <li
                      key={j}
                      className="text-onlight-text-1 dark:text-ondark-text-1"
                    >
                      {note}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default LocationCameraNotes;
